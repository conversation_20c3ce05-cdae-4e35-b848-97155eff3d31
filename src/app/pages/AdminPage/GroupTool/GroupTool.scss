.group-tool {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .group-tool-info-card,
  .group-tool-search-card,
  .group-tool-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .group-tool-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .group-tool-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .group-tool-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create-group-tool {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  // Search form styles
  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .form-filter {
    width: 100%;
  }

  .group-name-value {
    font-weight: 600;
    color: var(--typo-colours-primary-black);
    font-size: 14px;
  }

  // Table styles
  .tool-group-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .group-tool-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .tool-group-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 8px;

      .btn-edit-group-tool,
      .btn-delete-group-tool {
        &:hover {
          background: var(--background-hover);
        }
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .group-tool-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .btn-create-group-tool {
        width: 100%;
        justify-content: center;
      }
    }

    .form-filter {
      .search-buttons {
        margin-top: 16px;
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}