import React, { useEffect, useRef, useState } from "react";
import { connect, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { io } from "socket.io-client";

import { toast } from "@component/ToastProvider";
import { useSpeaking } from "@app/pages/Student/Speaking";

import AntButton from "@component/AntButton";

import { CONSTANT, BUTTON, RECOGNIZE_STATUS, RECORD_STATUS } from "@constant";
import { renderAudioDuration } from "@common/functionCommons";
import { createProjectFromTool, getProjectDetail } from "@services/Project";

import Microphone from "@component/SvgIcons/Microphone";

import STOP_INNER from "@src/asset/icon/stop-inner.svg";

import "./SpeakingRecorder.scss";
import { actions, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";


function SpeakingRecorder({ user, studentTools }) {
  const { t } = useTranslation();
  const { speakingForm, handleDataResponse } = useSpeaking();
  const { setRecordResult, setText } = useSpeaking();
  const { setWordAnalyzed, setCountErrors } = useSpeaking();
  const { setAudioFileId, setAudioUrlDownload } = useSpeaking();
  const { recognizeStatus, setRecognizeStatus } = useSpeaking();
  const { projectNameTemp, projectData, setProjectData, disabledEdit } = useSpeaking();
  
  const socket = useRef(null);
  
  const [recordStatus, setRecordStatus] = useState(RECORD_STATUS.STOPPED);
  const timeRef = useRef(null);
  const [timeRecorded, setTimeRecorded] = useState(0);
  
  const [hasMicAccess, setHasMicAccess] = useState(true);
  //microphone_not_accessible
  
  const dispatch = useDispatch();
  
  const audioSourceRef = useRef(null);
  const scriptProcessorRef = useRef(null);
  
  useEffect(() => {
    // Dọn dẹp khi component unmount
    return () => {
      if (socket.current) socket.current.disconnect();
    };
  }, []);
  
  useEffect(() => {
    if (timeRecorded >= CONSTANT.MAX_RECORD) {
      setRecordStatus(RECORD_STATUS.STOPPED);
    }
  }, [timeRecorded]);
  
  async function handleStartRecord() {
    const { errorFields } = await speakingForm.validateFields();
    if (!!errorFields?.length) return;
    
    setRecognizeStatus(RECOGNIZE_STATUS.RECOGNIZING);
    setRecordStatus(RECORD_STATUS.PREPARE_RECORD);
    setRecordResult([]);
    setText("");
    
    setWordAnalyzed([]);
    setCountErrors({});
    
    setAudioFileId("");
    setAudioUrlDownload("");
    setTimeRecorded(0);
    dispatch(actions.trackCustomClick(TRACKING_ACTIONS.INPUT_ESSAY));
  }
  
  function handleStopRecord() {
    clearInterval(timeRef.current);
    setRecordStatus(RECORD_STATUS.STOPPED);
    
    console.log("stopRecording");
    if (scriptProcessorRef.current) {
      scriptProcessorRef.current.onaudioprocess = null; // Hủy bỏ callback để ngừng xử lý âm thanh
      scriptProcessorRef.current.disconnect(); // Ngừng kết nối scriptProcessor
    }
    
    if (audioSourceRef.current) {
      console.log("stopRecording audioSteam");
      audioSourceRef.current.disconnect();
      audioSourceRef.current.mediaStream?.getTracks()?.forEach(track => track.stop());
    }
    
    socket.current?.emit("close-recording");
  }
  
  
  useEffect(() => {
    console.log("recordStatus", recordStatus);
    
    switch (recordStatus) {
      case RECORD_STATUS.PREPARE_RECORD:
        handlePrepareRecord();
        break;
      case RECORD_STATUS.RECORDING:
        handleDataResponse(socket.current);
        handleRecordAudio();
        break;
      case RECORD_STATUS.STOPPED:
        handleStopRecord();
        break;
      default:
        break;
    }
    
  }, [recordStatus]);
  
  async function handlePrepareRecord() {
    const apiRequest = { toolId: studentTools?.speaking._id };
    if (projectNameTemp) apiRequest.projectName = projectNameTemp;
    const apiResponse = await createProjectFromTool(apiRequest);
    if (!apiResponse?._id) {
      setRecordStatus(RECORD_STATUS.STOPPED);
      return toast.error("AN_ERROR_OCCURRED");
    }
    
    const projectResponse = await getProjectDetail(apiResponse._id);
    setProjectData(projectResponse?.data || apiResponse);
    
    socket.current = io("/student", { transports: ["websocket"], path: "/socket" });

    socket.current.on("connect", () => {
      console.log("Connected to WebSocket server");
      // Không gửi dữ liệu ở đây, chỉ log kết nối thành công
    });
    
    socket.current.on("server_ready", () => {
      setRecordStatus(RECORD_STATUS.RECORDING);
    });
    
    socket.current.on("error", (err) => {
      console.log("socket error", err);
      socket.current.disconnect();
      setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
    });
  }
  
  
  async function handleRecordAudio() {
    const topic = speakingForm.getFieldValue("topic");
    const instructionId = studentTools.speaking.instructionIds?.[0]?._id;
    
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
                                  .then(stream => {
                                    console.log("Đã truy cập micrô:", stream);
                                    return stream; // Trả về stream
                                  })
                                  .catch(error => {
                                    console.error("Lỗi truy cập micrô:", error);
                                    return null; // Trả về null nếu có lỗi
                                  });
    
    if (!stream) {
      setRecordStatus(RECORD_STATUS.STOPPED);
      setRecognizeStatus(RECOGNIZE_STATUS.NOT_STARTED);
      setHasMicAccess(false);
      return toast.error(t("MICROPHONE_NOT_ACCESSIBLE"), { replace: true });
    }
    
    // 2. Tạo AudioContext
    const audioContext = new AudioContext({ sampleRate: 16000 });
    
    // 3. Kết nối stream với AudioContext
    const source = audioContext.createMediaStreamSource(stream);
    audioSourceRef.current = source;
    // 4. Tạo ScriptProcessorNode
    const bufferSize = 4096; // Kích thước khối xử lý
    const scriptProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);
    scriptProcessorRef.current = scriptProcessor;
    // 5. Xử lý dữ liệu PCM trong callback
    scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
      const inputBuffer = audioProcessingEvent.inputBuffer;
      const channelData = inputBuffer.getChannelData(0); // Lấy dữ liệu kênh đầu tiên (mono)
      
      // Chuyển đổi Float32 (-1.0 -> 1.0) sang Int16 (-32768 -> 32767)
      const pcmInt16Array = new Int16Array(channelData.length);
      for (let i = 0; i < channelData.length; i++) {
        pcmInt16Array[i] = Math.max(-1, Math.min(1, channelData[i])) * 0x7FFF;
      }
      
      // Tạo Uint8Array từ Int16Array để gửi qua socket
      const pcmUint8Array = new Uint8Array(pcmInt16Array.buffer);
      
      // Gửi dữ liệu qua socket
      const inputData = {
        topic,
        instructionId,
        projectId: projectData._id,
      };
      
      socket.current?.emit("audio", {
        buffer: pcmUint8Array,
        inputData,
        userId: user._id,
      });
    };
    
    // 6. Kết nối các nodes
    source.connect(scriptProcessor);
    scriptProcessor.connect(audioContext.destination);
    
    timeRef.current = setInterval(() => {
      setTimeRecorded(prevState => prevState + 1);
    }, 1000);
  }
  
  
  function renderRecorderText() {
    switch (recordStatus) {
      case RECORD_STATUS.STOPPED:
        if (recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING) return t("RECOGNIZING") + "...";
        return <>
          {t("CLICK_TO_RECORD")}<br/>
          {t("MAXIMUM_2M30S")}
        </>;
      case RECORD_STATUS.PREPARE_RECORD:
        return t("PREPARING") + "...";
      case RECORD_STATUS.RECORDING:
        return t("RECORDING");
      default:
        return "";
    }
  }
  
  
  return <div className="speaking-record__recorder">
    <div className="speaking-record__recorder-icon">
      {recordStatus === RECORD_STATUS.STOPPED && <AntButton
        size="large"
        shape="circle"
        type={BUTTON.DEEP_BLUE}
        icon={<Microphone/>}
        onClick={handleStartRecord}
        disabled={recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING || disabledEdit}
      />}
      
      {recordStatus === RECORD_STATUS.PREPARE_RECORD && <AntButton
        size="large"
        shape="circle"
        type={BUTTON.DEEP_BLUE}
        loading
      />}
      
      {recordStatus === RECORD_STATUS.RECORDING && <AntButton
        size="large"
        shape="circle"
        type={BUTTON.DEEP_RED}
        icon={<img src={STOP_INNER} alt=""/>}
        onClick={() => {
          setRecordStatus(RECORD_STATUS.STOPPED);
        }}
      />}
    </div>
    <div className="speaking-record__recorder-text">
      {renderRecorderText()}
    </div>
    
    {recordStatus !== RECORD_STATUS.STOPPED && <div className="speaking-record__recorder-time">
      {renderAudioDuration(timeRecorded)}
    </div>}
  
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  const { studentTools } = store.tool;
  return { user, studentTools };
}

export default connect(mapStateToProps)(SpeakingRecorder);

