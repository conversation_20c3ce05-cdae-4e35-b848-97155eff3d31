import axios from "axios";

import { API } from "@api";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";


export function getVideoDetail(videoId, hideNoti = false) {
  
  const config = { hideNoti };
  
  return axios
    .get(`${API.VIDEO_DETAIL}?url=https://www.youtube.com/watch?v=${videoId}`, config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => err?.response?.data);
}

