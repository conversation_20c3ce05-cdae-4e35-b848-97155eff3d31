.manager-organization-user {

  .manager-organization-user-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 8px;
  }

  .table-manager-user {
    .invitation-pending {
      span {
        white-space: nowrap;
      }

      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;
      flex-wrap: nowrap;
    }

    .ant-table-thead {
      .ant-table-cell {
        background-color: var(--background-light-background-2);
      }
    }

    .ant-table-cell {
      font-family: Segoe UI;
      height: 60px !important;

      &:before {
        display: none;
      }
    }

    .manager-user-rows {

      color: var(--typo-colours-support-blue-light);
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
    }
  }

  .tooltip-resend-invitation {
    cursor: pointer;
  }


}

.dropdown-actions-manager-user {
  margin-top: -20px !important;
}

.select-member-role-popup {
  padding: 16px 0;

  .rc-virtual-list-holder {
    max-height: none !important;

    .rc-virtual-list-holder-inner {
      gap: 8px;

      .ant-select-item-option {
        padding: 8px 24px;
        border-radius: 0;

        &.ant-select-item-option-selected {
          background-color: var(--primary-colours-blue);
          color: var(--typo-colours-support-white);
        }

        .ant-select-item-option-content {
          white-space: break-spaces;
        }
      }
    }

  }
}