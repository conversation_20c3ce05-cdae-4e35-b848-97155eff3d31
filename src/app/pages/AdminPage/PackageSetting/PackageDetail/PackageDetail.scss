.package-detail {
  display: flex;
  font-family: Segoe UI;
  flex-direction: column;
  gap: 24px;
  border-radius: 8px;
  background-color: var(--background-light-background-2);
  padding: 24px;
  .required-color{
    color: red;
    margin-left: 5px;
  }
  .form-package-details {
    .ant-form-item-label {
      > label {
        height: fit-content !important;
      }
    }

    .row-price-unit {
      display: flex;
      gap: 24px;
      flex-direction: row;

      .baseline-row {
        width: calc(100% - 16px);
      }

      .btn-cancel-add-price {
        box-shadow: var(--shadow-level-2);
      }

      .price-action {
        display: flex;
        flex-direction: column;
        margin-top: 3px;
      }

    }

    .add-price-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      button {
        box-shadow: var(--shadow-level-2);
        color: var(--primary-colours-blue) !important;
      }
    }

    .ant-form-item-required {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 4px;
      padding: 0px;
      flex-direction: row-reverse;

      &:after {
        display: none;
      }
    }

    .btn-remove-feature {
      box-shadow: var(--shadow-level-2);
    }

    .package-detail-feature-list {
      display: flex;
      flex-direction: column;
    }


    .package-detail-feature-list-item {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      gap: 24px;

      .baseline-row {
        width: 100%;
      }

      .ant-input-group-addon {
        background-color: var(--background-light-background-1);
        border-color: var(--lighttheme-content-background-stroke);
        color: var(--typo-colours-support-blue-light);
      }
    }
  }

  .package-detail__title {
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
  }

  .baseline-row {
    align-items: baseline;

    .ant-input-group-wrapper {
      display: inline;
    }
  }

  .save-button {
    text-align: center;
    padding-top: 10px;
  }
}
