import { Navigate, Route, Routes } from "react-router-dom";
import { connect } from "react-redux";

import { LINK } from "@link";
import { CONSTANT } from "@constant";

import NeedAccess from "../component/NeedAccess";

import Instruction from "@src/app/pages/AdminPage/Instruction";
import Options from "@src/app/pages/AdminPage/Options";
import OptionDetail from "@src/app/pages/AdminPage/Options/OptionDetail";
import OutputType from "@src/app/pages/AdminPage/OutputType";
import InstructionDetail from "@src/app/pages/AdminPage/Instruction/InstructionDetail";
import Tools from "@src/app/pages/AdminPage/Tools";
import ToolDetail from "@src/app/pages/AdminPage/Tools/ToolDetail";

import Knowlegde from "@src/app/pages/AdminPage/Knowlegde";
import GroupTool from "@src/app/pages/AdminPage/GroupTool";
import TotalTokenByUser from "@src/app/pages/AdminPage/TotalTokenByUser";
import AverageToken from "@src/app/pages/AdminPage/AverageToken";
import GroupToolDetail from "@src/app/pages/AdminPage/GroupTool/GroupToolDetail";
import Persona from "@app/pages/AdminPage/Persona";
import PersonaDetail from "@app/pages/AdminPage/Persona/PersonaDetail";
import AdminCustomer from "@src/app/pages/AdminPage/Customer";
import AdminPaymentHistory from "@app/pages/AdminPage/PaymentHistory";
import UserTracking from "@app/pages/AdminPage/UserTracking";

import ModelGPT from "@src/app/pages/AdminPage/ModelGPT";
import Settings from "@src/app/pages/AdminPage/Settings";
import OpenAICost from "@src/app/pages/AdminPage/OpenAICost";

import DocumentTemplate from "@app/pages/AdminPage/DocumentTemplate";
import DocumentTemplateDetail from "@app/pages/AdminPage/DocumentTemplate/DocumentTemplateDetail";

import Exercise from "@app/pages/AdminPage/DictationAndShadowing";
import ExerciseDetail from "@app/pages/AdminPage/DictationAndShadowing/ExerciseDetail";
import SpeakingExercise from "@app/pages/AdminPage/SpeakingExercise";
import SpeakingExerciseDetail from "@app/pages/AdminPage/SpeakingExercise/ExerciseDetail";
import APIKey from "@app/pages/AdminPage/APIKey";

import WaitList from "@src/app/pages/AdminPage/WaitList";
import Whitelist from "@src/app/pages/AdminPage/Whitelist";

import VoiceOptions from "@src/app/pages/AdminPage/VoiceOptions";
import VoiceOptionDetail from "@src/app/pages/AdminPage/VoiceOptions/VoiceOptionDetail";
import PackageSetting from "@src/app/pages/AdminPage/PackageSetting";
import PackageDetail from "@src/app/pages/AdminPage/PackageSetting/PackageDetail";
import OrganizationManagement from "@src/app/pages/AdminPage/OrganizationManagement";
import OrganizationDetail from "@src/app/pages/AdminPage/OrganizationManagement/OrganizationDetail";
import Discount from "@src/app/pages/AdminPage/Discount";
import Promotion from "@src/app/pages/AdminPage/Promotion";
import TransactionHistory from "@src/app/pages/AdminPage/TransactionHistory";
import User from "@src/app/pages/AdminPage/User";

import FeedbackStatistics from "@src/app/pages/AdminPage/FeedbackStatistics";
import FeedbackAnalysis from "@src/app/pages/AdminPage/FeedbackAnalysis";
import SupportBusiness from "../pages/AdminPage/SupportBusiness";
import ErrorReports from "../pages/AdminPage/ErrorReports";
import Explain from "../pages/AdminPage/Explain";
import ExplainDetail from "../pages/AdminPage/Explain/ExplainDetail";

// Email Marketing
import EmailCampaign from "../pages/AdminPage/EmailMarketing/EmailCampaign";
import EmailCampaignDetail from "../pages/AdminPage/EmailMarketing/EmailCampaign/EmailCampaignDetail";
import EmailGroup from "../pages/AdminPage/EmailMarketing/EmailGroup";
import EmailGroupDetail from "../pages/AdminPage/EmailMarketing/EmailGroup/EmailGroupDetail";
import EmailTemplate from "../pages/AdminPage/EmailMarketing/EmailTemplate";
import EmailTemplateDetail from "../pages/AdminPage/EmailMarketing/EmailTemplate/EmailTemplateDetail";
import EmailStatistics from "../pages/AdminPage/EmailMarketing/EmailStatistics";

const AdminPageRouter = ({ user }) => {

  function linkToAdmin(adminUrl) {
    return adminUrl.replace(LINK.ADMIN_PAGE, "");
  }
  if (!user?.isSystemAdmin) return <NeedAccess />;

  return (
    <Routes>
      <Route>
        <Route path={linkToAdmin(LINK.PACKAGE)} element={<PackageSetting />} />
        <Route path={linkToAdmin(LINK.PACKAGE_CREATE)} element={<PackageDetail />} />
        <Route path={linkToAdmin(LINK.PACKAGE_ID.format(":id"))} element={<PackageDetail />} />

        <Route path={LINK.TOOL} element={<Tools />} />
        <Route path={`${LINK.TOOL}/create`} element={<ToolDetail />} />
        <Route path={`${LINK.TOOL}/:id`} element={<ToolDetail />} />

        <Route path={LINK.INSTRUCTION} element={<Instruction />} />
        <Route path={`${LINK.INSTRUCTION}/create`} element={<InstructionDetail />} />
        <Route path={`${LINK.INSTRUCTION}/:id`} element={<InstructionDetail />} />

        <Route path={LINK.OPTIONS} element={<Options />} />
        <Route path={`${LINK.OPTIONS}/create`} element={<OptionDetail />} />
        <Route path={`${LINK.OPTIONS}/:id`} element={<OptionDetail />} />
        <Route path={LINK.OUTPUT_TYPE} element={<OutputType />} />
        <Route path={LINK.TOOL_STUDIO_KNOWLEDGE} element={<Knowlegde />} />
        <Route path={LINK.TOOL_GROUP} element={<GroupTool />} />
        <Route path={LINK.TOTAL_TOKEN_BY_USER} element={<TotalTokenByUser />} />
        <Route path={LINK.AVERAGE_TOKEN} element={<AverageToken />} />
        <Route path={LINK.ADMIN_CREATE_GROUP_TOOL} element={<GroupToolDetail />} />
        <Route path={LINK.DETAIL_GROUP_TOOL.format(":id")} element={<GroupToolDetail />} />
        <Route path={LINK.PERSONA} element={<Persona />} />
        <Route path={LINK.CREATE_PERSONA} element={<PersonaDetail />} />
        <Route path={LINK.PERSONA_DETAIL.format(":id")} element={<PersonaDetail />} />
        <Route path={LINK.CUSTOMER} element={<AdminCustomer />} />
        <Route path={LINK.PAYMENT_HISTORY_ID.format(":id")} element={<AdminPaymentHistory />} />
        <Route path={LINK.USER_TRACKING} element={<UserTracking />} />
        <Route path={LINK.GPTMODEL} element={<ModelGPT />} />
        <Route path={LINK.GPTMODEL} element={<ModelGPT />} />
        <Route path={LINK.VOICE_OPTIONS} element={<VoiceOptions />} />
        <Route path={`${LINK.VOICE_OPTIONS}/create`} element={<VoiceOptionDetail />} />
        <Route path={`${LINK.VOICE_OPTIONS}/:id`} element={<VoiceOptionDetail />} />

        <Route path={linkToAdmin(LINK.ADMIN.DOCUMENT_TEMPLATE)} element={<DocumentTemplate />} />
        <Route path={linkToAdmin(LINK.ADMIN.API_KEY)} element={<APIKey />} />
        <Route path={linkToAdmin(LINK.ADMIN.DOCUMENT_TEMPLATE_ID.format(":id"))} element={<DocumentTemplateDetail />} />
        <Route path={linkToAdmin(LINK.ADMIN.SETTING)} element={<Settings />} />

        <Route path={linkToAdmin(LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT)} element={<Exercise />} />
        <Route path={linkToAdmin(LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT_CREATE)} element={<ExerciseDetail />} />
        <Route path={linkToAdmin(LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT_ID.format(":id"))} element={<ExerciseDetail />} />

        <Route path={linkToAdmin(LINK.ADMIN.SPEAKING_EXERCISE)} element={<SpeakingExercise />} />
        <Route path={linkToAdmin(LINK.ADMIN.SPEAKING_EXERCISE_CREATE)} element={<SpeakingExerciseDetail />} />
        <Route path={linkToAdmin(LINK.ADMIN.SPEAKING_EXERCISE_ID.format(":id"))} element={<SpeakingExerciseDetail />} />

        <Route path={LINK.OPENAI_COST} element={<OpenAICost />} />
        <Route path={LINK.WAITING_LIST} element={<WaitList />} />
        <Route path={LINK.WHITELIST} element={<Whitelist />} />

        <Route path={linkToAdmin(LINK.ADMIN.ORGANIZATION)} element={<OrganizationManagement />} />
        <Route path={linkToAdmin(LINK.ADMIN.ORGANIZATION_ID.format(":id"))} element={<OrganizationDetail />} />

        <Route path={linkToAdmin(LINK.ADMIN.DISCOUNT)} element={<Discount/>}/>
        <Route path={linkToAdmin(LINK.ADMIN.PROMOTION)} element={<Promotion/>}/>
        <Route path={linkToAdmin(LINK.ADMIN.TRANSACTION_HISTORY)} element={<TransactionHistory/>}/>

        <Route path={linkToAdmin(LINK.ADMIN.USER)} element={<User/>}/>

        <Route path={linkToAdmin(LINK.ADMIN.FEEDBACK_STATISTIC)} element={<FeedbackStatistics />} />
        <Route path={linkToAdmin(LINK.ADMIN.FEEDBACK_ANALYSIS)} element={<FeedbackAnalysis />} />

        <Route path={linkToAdmin(LINK.ADMIN.SUPPORT_BUSINESS)} element={<SupportBusiness />} />
        <Route path={linkToAdmin(LINK.ADMIN.ERROR_REPORTS)} element={<ErrorReports />} />

        {/* Explain Service Routes */}
        <Route path={LINK.ADMIN_EXPLAIN.replace(LINK.ADMIN_PAGE, "")} element={<Explain />} />
        <Route path={LINK.ADMIN_EXPLAIN_CREATE.replace(LINK.ADMIN_PAGE, "")} element={<ExplainDetail />} />
        <Route path={LINK.ADMIN_EXPLAIN_DETAIL.format(":id").replace(LINK.ADMIN_PAGE, "")} element={<ExplainDetail />} />

        {/* Email Marketing Routes */}
        <Route path={linkToAdmin(LINK.ADMIN.EMAIL_CAMPAIGN)} element={<EmailCampaign />} />
        <Route path={linkToAdmin(LINK.ADMIN.EMAIL_CAMPAIGN_CREATE)} element={<EmailCampaignDetail />} />
        <Route path={linkToAdmin(LINK.ADMIN.EMAIL_CAMPAIGN_ID.format(":id"))} element={<EmailCampaignDetail />} />

        <Route path={linkToAdmin(LINK.ADMIN.EMAIL_GROUP)} element={<EmailGroup />} />
        <Route path={linkToAdmin(LINK.ADMIN.EMAIL_GROUP_CREATE)} element={<EmailGroupDetail />} />
        <Route path={linkToAdmin(LINK.ADMIN.EMAIL_GROUP_ID.format(":id"))} element={<EmailGroupDetail />} />

        <Route path={linkToAdmin(LINK.ADMIN.EMAIL_TEMPLATE)} element={<EmailTemplate />} />
        <Route path={linkToAdmin(LINK.ADMIN.EMAIL_TEMPLATE_CREATE)} element={<EmailTemplateDetail />} />
        <Route path={linkToAdmin(LINK.ADMIN.EMAIL_TEMPLATE_ID.format(":id"))} element={<EmailTemplateDetail />} />

        <Route path={linkToAdmin(LINK.ADMIN.EMAIL_STATISTICS)} element={<EmailStatistics />} />

        <Route path="*" element={<Navigate to={LINK.ERROR_404} replace />} />

      </Route>
    </Routes>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(AdminPageRouter);
