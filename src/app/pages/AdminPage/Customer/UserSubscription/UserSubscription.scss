.user-subscription-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .user-subscription-search-card,
  .user-subscription-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  .form-filter {
    width: 100%;
  }

  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
  }

  // Table styles
  .user-subscription-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .user-subscription-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .user-name-value {
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      font-size: 14px;
    }

    .package-name-value {
      font-weight: 500;
      color: var(--typo-colours-support-blue);
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .search-buttons {
      margin-top: 16px;
      width: 100%;
      justify-content: space-between;
    }
  }
}