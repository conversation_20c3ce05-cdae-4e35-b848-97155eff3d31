import React, { useEffect, useMemo, useRef, useState } from "react";
import { Form, Input, InputNumber, Select } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import { useProject } from "@app/pages/Project";
import { useContentInput } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm";

import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT, FIELD_TYPE } from "@constant";
import { cloneObj, stringSplit } from "@common/functionCommons";
import { extractIds } from "@common/dataConverter";

import "./InstructionForm.scss";

const TEXTAREA_MAX_WORDS = 1000;

function InstructionForm(props) {
  const { t, i18n } = useTranslation();
  
  const { examOptionData, isMarkStudent } = useProject();
  const { formKey } = useContentInput();
  
  const [instructionForm] = Form.useForm();
  
  const {
    isDisabledContent,
    toolInfo,
    content,
    inputData,
    instructionSelected,
    setInstructionSelected,
  } = props;
  
  const inputDataPrev = useRef(null);
  
  const [instructionExtra, setInstructionExtra] = useState([]);
  
  useEffect(() => {
    setTimeout(() => {
      const dataForm = {};
      
      const newInputData = cloneObj(inputData);
      
      Object.entries(newInputData).forEach(([key, value]) => {
        if (inputDataPrev.current?.[key] !== value) {
          dataForm[key] = value;
        }
      });
      
      if (dataForm.instructionId && toolInfo.instructionIds?.length
        && !extractIds(toolInfo.instructionIds).includes(dataForm.instructionId)) {
        delete dataForm.instructionId;
      }
      
      instructionForm.setFieldsValue(dataForm);
      
      inputDataPrev.current = newInputData;
    }, 200);
  }, [inputData, examOptionData]);
  
  useEffect(() => {
    if (!toolInfo?.instructionIds?.length) {
      setInstructionSelected(null);
      setInstructionExtra([]);
      return;
    }
    
    if (toolInfo.instructionIds.length === 1) {
      instructionForm.setFieldsValue({ instructionId: toolInfo.instructionIds[0]._id });
      setInstructionSelected(toolInfo.instructionIds[0]);
      const options = toolInfo.instructionIds[0].options || [];
      setInstructionExtra(options);
      
      setInstructionFormDefault(options);
    } else {
      if (inputData?.instructionId) {
        const instruction = toolInfo.instructionIds?.find(instruction => instruction._id === inputData.instructionId);
        setInstructionSelected(instruction);
        setInstructionExtra(instruction?.options || []);
      }
    }
  }, [toolInfo, inputData?.instructionId]);
  
  
  useEffect(() => {
    setInstructionFormDefault(instructionExtra);
    inputDataPrev.current = null;
  }, [instructionExtra]);
  
  
  function setInstructionFormDefault(options) {
    if (!options?.length) return;
    const instFormData = {};
    options.forEach(option => {
      instFormData[option.code] = option.defaultValue;
    });
    instructionForm.setFieldsValue(instFormData);
  }
  
  function handleSelectInstruction(instruction) {
    if (!instruction?._id || instructionSelected?._id === instruction._id) return;
    instructionForm.resetFields();
    instructionForm.setFieldsValue({ instructionId: instruction._id });
    setInstructionSelected(instruction);
    if (toolInfo.instructionIds?.length !== 1) {
      setInstructionExtra(instruction?.options || []);
    }
  }
  
  function renderTypeOfTool() {
    if (toolInfo?.instructionIds?.length < 2) return;
    
    return <Form.Item label={t("TYPE_OF_TOOL")} className="type-of-question">
      {toolInfo.instructionIds.map(instruction => {
        return <AntButton
          key={instruction._id}
          size="large"
          type={BUTTON.GHOST_NAVY}
          onClick={() => handleSelectInstruction(instruction)}
          disabled={isDisabledContent}
          active={instructionSelected?._id === instruction._id}
        >
          {instruction.shortName}
        </AntButton>;
      })}
    </Form.Item>;
  }
  
  const localizationInstructionExtra = useMemo(() => {
    return instructionExtra.map(item => {
      const extraType = item.type?.toUpperCase();

      if ([FIELD_TYPE.SELECT, FIELD_TYPE.SELECT_MULTIPLE].includes(extraType)) {
        const selectOptions = item.selectOptions || [];
        const updateSelectOptions = selectOptions.map(option => {
          const label = option.label?.[i18n.language] || option.label;
          return { ...option, label };
        });
        
        return { ...item, selectOptions: updateSelectOptions };
      } else {
        return item;
      }
    });
  }, [instructionExtra, i18n.language]);
  
  return <>
    <AntForm
      id={`${CONSTANT.INSTRUCTION}-${content._id}${formKey}`}
      name={`${CONSTANT.INSTRUCTION}-${content._id}${formKey}`}
      className={clsx("form-instruction-input", { hidden: toolInfo?.instructionIds?.length < 2 && !instructionExtra?.length })}
      form={instructionForm}
      layout="vertical"
      disabled={isDisabledContent}
      size="large"
    >
      <Form.Item hidden name="instructionId">
        <Input readOnly />
      </Form.Item>
      
      {renderTypeOfTool()}
      
      {localizationInstructionExtra
        ?.filter(option => !extractIds(examOptionData)?.includes(option._id))
        .map(extra => {
          const extraType = extra.type?.toUpperCase();
          const extraOptions = extra.selectOptions || [];
          const { placeholder } = extra || {};
          const maxWords = (isMarkStudent && extra?.code === 'text') ? 3000 : TEXTAREA_MAX_WORDS;
          
          const rules = [];
          switch (extra.rule) {
            case "required":
              rules.push({ required: true, message: t("CAN_NOT_BE_BLANK") });
              break;
            default:
              break;
          }
          
          if (extraType === FIELD_TYPE.TEXTAREA) {
            const validateWordCount = (rule, value) => {
              if (!value) return Promise.resolve();
              
              const wordCount = stringSplit(value).length;
              if (wordCount > maxWords) {
                return Promise.reject(new Error(t("ONLY_ENTER_UP_TO_NUMBER_WORD").format(maxWords)));
                //return Promise.reject(new Error(`You can only enter up to ${maxWords} words.`));
              }
              
              return Promise.resolve();
            };

            if (extra.rule === "required") rules[0].whitespace = true;
            
            rules.push({ validator: validateWordCount });
          }
          
          if (extraType === FIELD_TYPE.TAG) {
            return <AntForm.TagItem
              key={extra._id}
              name={extra.code} label={extra.name} rules={rules}
              placeholder={placeholder}
            />;
          }
          
          return <Form.Item
            key={extra._id}
            name={extra.code}
            label={extra.localization?.name?.[i18n.language] || extra.name}
            rules={rules}
            className={clsx({
              "form-instruction__textarea": extraType === FIELD_TYPE.TEXTAREA,
            })}
          >
            {extraType === FIELD_TYPE.SELECT && <Select options={extraOptions} placeholder={placeholder} />}
            {extraType === FIELD_TYPE.SELECT_MULTIPLE &&
              <Select mode="multiple" options={extraOptions} placeholder={placeholder} />}
            {extraType === FIELD_TYPE.TEXT && <Input placeholder={placeholder} />}
            {extraType === FIELD_TYPE.TEXTAREA && <Input.TextArea
              count={{
                show: true, max: maxWords,
                strategy: (txt) => stringSplit(txt).length,
              }}
              autoSize={{ minRows: 1 }}
              placeholder={placeholder}
            />}
            {extraType === FIELD_TYPE.NUMBER && <InputNumber placeholder={placeholder} />}
          </Form.Item>;
        })}
    
    </AntForm>
  </>;
}

export default InstructionForm;