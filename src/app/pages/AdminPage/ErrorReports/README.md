# Error Reports Management System

## Tổng quan
Hệ thống quản lý báo cáo lỗi cho phép admin theo dõi và xử lý các lỗi được người dùng báo cáo.

## Cấu trúc thư mục
```
ErrorReports/
├── index.js                    # Component chính - danh sách error reports
├── ErrorReports.scss          # Styling cho component chính
├── ErrorReportDetail/         # Component chi tiết error report
│   ├── index.js              # Component detail view
│   └── ErrorReportDetail.scss # Styling cho detail view
└── README.md                 # Tài liệu này
```

## Tính năng chính

### 1. Danh sách Error Reports
- Hiển thị bảng với các thông tin cơ bản: ID, User, Description, Impact Level, URL, Status
- Pagination để xử lý số lượng lớn
- Filtering theo:
  - Mức độ ảnh hưởng (low/medium/high)
  - Tr<PERSON>ng thái thông báo (notified/not notified)
  - <PERSON><PERSON><PERSON><PERSON> thời gian
- Search theo description và URL
- Sorting theo ngày tạo

### 2. Chi tiết Error Report
- Modal hiển thị đầy đủ thông tin
- Xem media content (images/videos)
- Cập nhật trạng thái notification
- Thêm/chỉnh sửa admin notes
- Responsive design

### 3. Quản lý trạng thái
- Đánh dấu đã/chưa thông báo
- Xóa error report (soft delete)
- Thêm ghi chú nội bộ

## API Endpoints sử dụng
- `GET /api/errorreports` - Lấy danh sách với pagination
- `GET /api/errorreports/:id` - Lấy chi tiết
- `PATCH /api/errorreports/:id` - Cập nhật trạng thái
- `DELETE /api/errorreports/:id` - Xóa (soft delete)

## Cấu trúc dữ liệu Error Report
```javascript
{
  _id: "string",
  userId: {
    _id: "string",
    email: "string",
    fullName: "string"
  },
  imageIds: [
    {
      _id: "string",
      url: "string",
      path: "string"
    }
  ],
  videoId: {
    _id: "string",
    url: "string",
    path: "string"
  },
  description: "string",
  impactLevel: "low|medium|high",
  errorUrl: "string",
  notified: "boolean",
  adminNotes: "string",
  isDeleted: "boolean",
  createdAt: "date",
  updatedAt: "date"
}
```

## Translation Keys
Tất cả các key translation đã được thêm vào `src/translations/admin_aside_keys.js` với cả tiếng Anh và tiếng Việt.

## Routing
- Route: `/admin/error-reports`
- Đã được thêm vào `AdminPageRouter.js`
- Menu item đã được thêm vào `AdminAsideGroup.js` trong nhóm "System Configuration"

## Styling
- Sử dụng Ant Design components
- Custom SCSS cho responsive design
- Consistent với design system hiện tại
- Dark/light theme support

## Responsive Design
- Mobile-friendly layout
- Adaptive table columns
- Touch-friendly buttons
- Optimized modal for mobile

## Performance Considerations
- Lazy loading cho images
- Pagination để tránh load quá nhiều data
- Debounced search
- Optimized re-renders với React.memo (có thể thêm sau)

## Security
- Admin-only access (đã có check `isSystemAdmin`)
- Proper error handling
- Input validation
- XSS protection với Ant Design components

## Future Enhancements
1. Export error reports to CSV/Excel
2. Bulk actions (mark multiple as notified)
3. Email notifications to admins
4. Advanced filtering (by user, date range, etc.)
5. Error report analytics/dashboard
6. Auto-categorization of errors
7. Integration with issue tracking systems
