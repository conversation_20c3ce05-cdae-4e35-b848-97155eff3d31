import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { Col, Form, Row, Select, Input, DatePicker } from "antd";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { SearchOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import queryString from "query-string";

import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";

import { BUTTON, PAGINATION_INIT } from "@constant";

import { handleSearchParams } from "@src/common/functionCommons";

import "./Filter.scss";
import { getAllGroupFeedback } from "@src/app/services/Feedback";

const Filter = ({ groupFeedback }) => {
  const [formFilter] = Form.useForm();
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [isShowSelectDate, setShowSelectDate] = useState(false);
  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);
  const [isSearchable, setSearchable] = useState(false);

  useEffect(() => {
    const queryObj = queryString.parseUrl(location.search).query;
    handleQueryFromUrl(queryObj);
  }, [location.search]);

  const handleQueryFromUrl = (query) => {
    const { time, fromDate, toDate } = query;

    const newQuery = {
      ...query,
      ...fromDate ? { fromDate: dayjs(fromDate * 1000) } : {},
      ...toDate ? { toDate: dayjs(toDate * 1000) } : {},
    };

    setShowSelectDate(time === "custom");
    setMinDate(newQuery?.fromDate);
    setMaxDate(newQuery?.toDate);
    Object.keys(newQuery).forEach((key) => {
      if (newQuery[key] === "true") newQuery[key] = true;
      if (newQuery[key] === "false") newQuery[key] = false;
      if (key === 'feedback1') newQuery[key] = parseInt(newQuery[key]);
    })
    formFilter.setFieldsValue(newQuery);
  };

  const onFilterSubmit = (values) => {
    const { fromDate, toDate } = values;
    const repareValues = {
      ...values,
      ...fromDate ? { fromDate: dayjs(fromDate)?.startOf("day")?.unix() } : {},
      ...toDate ? { toDate: dayjs(toDate)?.endOf("day")?.unix() } : {},
    };
    updateUrlQuery(repareValues);
  };

  const updateUrlQuery = (dataSearch = {}) => {
    let searchParams = new URLSearchParams();
    Object.entries(dataSearch).forEach(([key, value]) => {
      if (value || value === false) searchParams.append(key, value);
    });
    navigate(`?${searchParams.toString()}`, { replace: true });
    setSearchable(false);
  };

  const clearFormFilter = () => {
    setShowSelectDate(false);
    formFilter.resetFields();
    setSearchable(true);
  };

  const onFormChange = () => {
    setSearchable(true);
  };

  const hanldeChangeSelectTime = (value) => {
    if (value === "custom") {
      setShowSelectDate(true);
    } else {
      setShowSelectDate(false);
      formFilter.setFieldsValue({ fromDate: null, toDate: null });
    }
  };

  return <AntForm
    form={formFilter}
    size={"large"}
    className={"filter-form-feeback-statistics"}
    onFinish={onFilterSubmit}
    onValuesChange={onFormChange}
  >
    <Row gutter={24} className="grow">
      <Col xs={24} md={12} lg={6}>
        <AntForm.Item name={"groupFeedbackId"} className="search-form-item">
          <Select
            placeholder={t("SELECT_GROUP_FEEDBACK")}
            allowClear>
            {groupFeedback?.map((item) => <Select.Option key={item._id} value={item._id}>
              {item?.localization?.groupName?.[i18n.language]}
            </Select.Option>)}
          </Select>
        </AntForm.Item>
      </Col>
      <Col xs={24} md={12} lg={6}>
        <AntForm.Item name={"toolName"} className="search-form-item">
          <Input
            placeholder={t("SEARCH_FEATURE_PLACEHOLDER")}
            allowClear
            prefix={<SearchOutlined />}
          />
        </AntForm.Item>
      </Col>
      <Col xs={24} md={12} lg={6}>
        <AntForm.Item name={"email"} className="search-form-item">
          <Input
            placeholder={t("SEARCH_EMAIL_PLACEHOLDER")}
            allowClear
            prefix={<SearchOutlined />}
          />
        </AntForm.Item>
      </Col>
      <Col xs={24} md={12} lg={6}>
        <AntForm.Item name={"feedback1"} className="search-form-item">
          <Select
            options={[
              { value: 1, label: t("VERY_BAD") },
              { value: 2, label: t("BAD") },
              { value: 3, label: t("AVERAGE") },
              { value: 4, label: t("GOOD") },
              { value: 5, label: t("EXCELLENT") },
            ]}
            placeholder={t("SELECT_SATISFACTION_LEVEL")}
            allowClear />
        </AntForm.Item>
      </Col>
      <Col xs={24} md={12} lg={6}>
        <AntForm.Item name={"feedback2"} className="search-form-item">
          <Select placeholder={t("SELECT_USEFULNESS_LEVEL")} allowClear>
            <Select.Option value={1}>1</Select.Option>
            <Select.Option value={2}>2</Select.Option>
            <Select.Option value={3}>3</Select.Option>
            <Select.Option value={4}>4</Select.Option>
            <Select.Option value={5}>5</Select.Option>
          </Select>
        </AntForm.Item>
      </Col>
      <Col xs={24} md={12} lg={6}>
        <AntForm.Item name={"feedback3"} className="search-form-item">
          <Select placeholder={t("UPGRADE_INTEREST")} allowClear>
            <Select.Option value={true}>{t("YES")}</Select.Option>
            <Select.Option value={false}>{t("NO")}</Select.Option>
          </Select>
        </AntForm.Item>
      </Col>
      <Col xs={24} md={12} lg={6}>
        <AntForm.Item name={"feedback4"} className="search-form-item">
          <Select placeholder={t("RECOMMEND_TO_OTHERS")} allowClear>
            <Select.Option value={true}>{t("YES")}</Select.Option>
            <Select.Option value={false}>{t("NO")}</Select.Option>
          </Select>
        </AntForm.Item>
      </Col>
      <Col xs={24} md={12} lg={6}>
        <AntForm.Item name={"time"} className="search-form-item">
          <Select placeholder={t("SELECT_TIME")} onChange={hanldeChangeSelectTime} allowClear>
            <Select.Option value={"week"}>{t("THIS_WEEK")}</Select.Option>
            <Select.Option value={"month"}>{t("THIS_MONTH")}</Select.Option>
            <Select.Option value={"custom"}>{t("CUSTOM")}</Select.Option>
          </Select>
        </AntForm.Item>
      </Col>
      {isShowSelectDate && <>
        <Col xs={24} md={12} lg={6}>
          <AntForm.Item
            name={"fromDate"}
            className="search-form-item"
            rules={[
              () => ({
                validator(_, value) {
                  if (!!value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                },
              }),
            ]}
          >
            <DatePicker
              placeholder={t("SELECT_FROM_DATE")}
              size="large"
              className="filter-form__date-picker"
              format="DD/MM/YYYY"
              maxDate={maxDate}
              onChange={setMinDate}
            />
          </AntForm.Item>
        </Col>
        <Col xs={24} md={12} lg={6}>
          <AntForm.Item
            name={"toDate"}
            className="search-form-item"
            rules={[
              () => ({
                validator(_, value) {
                  if (!!value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                },
              }),
            ]}>
            <DatePicker
              placeholder={t("SELECT_TO_DATE")}
              size="large"
              className="filter-form__date-picker"
              format="DD/MM/YYYY"
              minDate={minDate}
              onChange={setMaxDate}
            />
          </AntForm.Item>
        </Col>
      </>}
    </Row>
    <Row justify="end" className="search-buttons-row">
      <Col>
        <div className={"search-buttons"}>
          <AntButton type={BUTTON.GHOST_WHITE} onClick={clearFormFilter}>{t("CLEAR")}</AntButton>
          <AntButton type={BUTTON.DEEP_NAVY} htmlType={"submit"} disabled={!isSearchable}>{t("SEARCH")}</AntButton>
        </div>
      </Col>
    </Row>
  </AntForm>;
};

const mapStateToProps = (store) => {
  return { groupFeedback: store.feedback.groupFeedback }
}

export default connect(mapStateToProps)(Filter);