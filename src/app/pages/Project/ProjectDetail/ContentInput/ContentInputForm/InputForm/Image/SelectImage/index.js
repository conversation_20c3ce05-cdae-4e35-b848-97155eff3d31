import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import AntModal from "@component/AntModal";
import ResourceCategories from "@component/Resource/ResourceCategories";

import { CONSTANT } from "@constant";

import ImageFromResource from "./ImageFromResource";
import UploadImage from "./UploadImage";

import { getAllResource } from "@services/Resource";

import "./SelectImage.scss";

function SelectImage({ user, ...props }) {
  const { t } = useTranslation();
  const { isShowSelectImage, setShowSelectImage, onSelectResourceImage, isDisabledContent } = props;
  
  const [categorySelected, setCategorySelected] = useState(CONSTANT.MY_COMPUTER);
  const [myResourceData, setMyResourceData] = useState([]);
  const [orgResourceData, setOrgResourceData] = useState([]);
  const [isLoading, setLoading] = useState(false);
  
  useEffect(() => {
    if (isShowSelectImage) {
      getResourceImage();
    } else {
      setCategorySelected(CONSTANT.MY_COMPUTER);
    }
  }, [isShowSelectImage]);
  
  async function getResourceImage() {
    setLoading(true);
    const query = { type: CONSTANT.IMAGE };
    const allRequest = [getAllResource({ ...query, userId: user._id })];
    if (user.organizationId?._id) {
      allRequest.push(getAllResource({ ...query, organizationId: user.organizationId._id }));
    }
    
    const [myResourceList, orgResourceList] = await Promise.all(allRequest);
    if (Array.isArray(myResourceList)) setMyResourceData(myResourceList);
    if (Array.isArray(orgResourceList)) setOrgResourceData(orgResourceList);
    setLoading(false);
  }
  
  return <AntModal
    width={1056}
    title={t("SELECT_FROM_RESOURCE")}
    open={isShowSelectImage}
    onCancel={() => setShowSelectImage(false)}
    footerless
    destroyOnClose
    maskClosable={false}
    className="select-from-resource-modal"
  >
    <div className="select-from-resource image-from-resource-container">
      <ResourceCategories
        allowUpload={true}
        categorySelected={categorySelected}
        setCategorySelected={setCategorySelected}
      />
      
      {categorySelected === CONSTANT.MY_COMPUTER
        ? <UploadImage
          categorySelected={categorySelected}
          onSelectResourceImage={onSelectResourceImage}
          isDisabledContent={isDisabledContent}
        />
        : <ImageFromResource
          setShowSelectImage={setShowSelectImage}
          onSelectResourceImage={onSelectResourceImage}
          categorySelected={categorySelected}
          myResourceData={myResourceData}
          orgResourceData={orgResourceData}
          isLoading={isLoading}
        />
      }
    </div>
  </AntModal>;
}


function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(SelectImage);