import { cloneObj } from "@common/functionCommons";
import { ALPHABET, CONSTANT } from "@constant";


export function convertAnswerToObj(answerList) {
  return answerList?.reduce(function (grouped, element) {
    grouped[element?.questionId] = element?.correctAnswer;
    return grouped;
  }, {});
}

export function updateAbcdQuestionId(questionList) {
  const questionSorted = cloneObj(questionList).sort((a, b) => a?.questionId - b?.questionId);
  questionSorted.forEach((question, index) => {
    question.questionId = index + 1;
  });
  return questionSorted;
}

export function updateAbcdOptionId(optionList) {
  const optionSorted = optionList.sort((a, b) => a?.optionId?.localeCompare(b?.optionId));
  optionSorted.forEach((optionItem, index) => {
    optionItem.optionId = ALPHABET[index];
  });
  return optionSorted;
}

export function handleMoveOutput(dataSource, sourceId, type, sourceKey = "questionId") {
  const questionMoved = cloneObj(dataSource);
  for (let i = 0; i < questionMoved.length; i++) {
    if (questionMoved[i]?.[sourceKey] === sourceId) {
      if (type === CONSTANT.MOVE_DOWN) {
        questionMoved[i][sourceKey] = questionMoved[i + 1][sourceKey];
        questionMoved[i + 1][sourceKey] = sourceId;
      } else if (type === CONSTANT.MOVE_UP) {
        questionMoved[i][sourceKey] = questionMoved[i - 1][sourceKey];
        questionMoved[i - 1][sourceKey] = sourceId;
      }
      break;
    }
  }
  return questionMoved.sort((a, b) => a?.[sourceKey] - b?.[sourceKey]);
}