import { useEffect, useState } from 'react';
import clsx from 'clsx';
import { Image } from 'antd';

import { getImageDetail } from '@src/app/services/Image';

import { API } from '@api';
import { BUTTON } from '@constant';
import ChevronLeft from '@src/app/component/SvgIcons/ChevronLeft';
import AntButton from '@src/app/component/AntButton';
import ChevronRight from '@src/app/component/SvgIcons/ChevronRight';
import { ZoomInOutlined } from '@ant-design/icons';

import './PreviewImageEssay.scss';

const PreviewImageEssay = ({ imageIds = [], isResult = false }) => {
  const [imagesDisplay, setImagesDisplay] = useState([]);
  const [previewIndex, setPreviewIndex] = useState(0);
  const [displayIndex, setDisplayIndex] = useState(0);

  useEffect(() => {
    if (imageIds.length > 0) {
      getImagesData();
    } else {
      setImagesDisplay([]);
    }
  }, [imageIds]);

  const getImagesData = async () => {
    const allRequest = imageIds.map(id => getImageDetail(id));
    const responses = await Promise.all(allRequest);
    if (responses) {
      setImagesDisplay(responses);
      setDisplayIndex(0);
      setPreviewIndex(0);
    }
  }

  if (imagesDisplay.length === 0) return null;

  return (
    <div className="preview-image-essay">
      <Image.PreviewGroup
        preview={{
          onChange: setPreviewIndex,
          current: previewIndex,
          onVisibleChange: () => setPreviewIndex(displayIndex)
        }}
      >
        {imagesDisplay.map((item, index) => {
          const src = API.STREAM_ID.format(item?.imageFileId);
          return <Image
            key={item?._id}
            className={clsx("preview-image-essay__image",
              { "image-hidden": index !== displayIndex },
              { "image-result": isResult })}
            src={src}
            preview={{ mask: <ZoomInOutlined className="image-preview-icon" /> }}
          />
        })}
      </Image.PreviewGroup>

      {imagesDisplay.length > 1 && <div className="select-image">
        <AntButton
          size="mini"
          type={BUTTON.GHOST_WHITE}
          icon={<ChevronLeft />}
          onClick={() => setDisplayIndex(prevState => prevState - 1)}
          disabled={displayIndex < 1}
        />

        {`${displayIndex + 1} / ${imagesDisplay?.length || 0}`}

        <AntButton
          size="mini"
          type={BUTTON.GHOST_WHITE}
          icon={<ChevronRight />}
          onClick={() => setDisplayIndex(prevState => prevState + 1)}
          disabled={displayIndex === imagesDisplay?.length - 1}
        />
      </div>}
    </div>
  );
};

export default PreviewImageEssay;