import { calculateIeltsAverage, convertCamelAndNumberToTitleCase, WRITING_EVALUATION_LIST } from "./functionCommon";
import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";
import { CONSTANT } from "@constant";

const Arguments = ({ argumention = {}, evaluationSelected }) => {
  const { t } = useTranslation();
  const isObject = typeof argumention === "object";
  if (!argumention
    || (isObject && !Object.keys(argumention)?.length)
    || !['argumention', CONSTANT.ALL].includes(evaluationSelected)) return null;

  return <div className="evaluation__content__item evaluation-criteria-assessment">
    <div
      className="content__item__title evaluation-task-achievement__title"
      style={{ color: WRITING_EVALUATION_LIST.argumention.color }}
    >
      <img src={WRITING_EVALUATION_LIST.argumention.icon} alt="" />
      {t("ARGUMENTS")}
    </div>

    {typeof argumention === "object" ? Object.entries(argumention).map(([key, values], index) => {
      const feedback = values.feedback || [];

      return (
        <div key={index}>
          <div className="criteria-assessment__item-title">{convertCamelAndNumberToTitleCase(key)}</div>
          <ul>
            <li>{values.original}
              <ul>
                {feedback.map((item, itemIndex) => (
                  <li key={itemIndex}>
                    <span
                      className="criteria-assessment__item-title">{convertCamelAndNumberToTitleCase(item.category)}:</span>
                    {item.feedback}
                  </li>
                ))}
              </ul>
            </li>
            <li>
              <span className="criteria-assessment__item-title">Improved {convertCamelAndNumberToTitleCase(key)}:</span>
              {values.improved}
            </li>
          </ul>
        </div>
      );
    }) : <HtmlContent dangerouslySetInnerHTML={{ __html: argumention }} />}
  </div>;
};

export default Arguments;