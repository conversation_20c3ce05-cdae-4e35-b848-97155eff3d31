.option-modal {
  .option-modal__content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    color: #000;

    .option-modal__body {
      overflow-y: auto;
      max-height: 600px;
      overflow-x: hidden;

      .select-options-item {
        display: flex;
        gap: 24px;
        padding-bottom: 8px;
        align-items: center;
        font-weight: 600;

        .add-option-button {
          padding: 5px 10px;
        }
      }

      .ant-row {
        width: 100%;
      }

      .select-options-item__btnRemove {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        padding-right: 10px;
      }

      .btn-cancel-add-options {
        box-shadow: var(--shadow-level-2);
      }

    }

    .add-options-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding-bottom: 20px;

      button {
        box-shadow: var(--shadow-level-2);
        color: var(--primary-colours-blue) !important;
      }
    }

  }

  .ant-form-item-label {
    label {
      height: unset !important;
    }
  }

  .ant-form-item-required {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
    padding: 0px;
    flex-direction: row-reverse;

    &:after {
      display: none;
    }
  }

  .instruction-localization-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .instruction-localization-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;


  }

  .items-baseline {
    width: 100%;
  }

  .instruction-item-action {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 5px;
  }

  .first-actions {
    align-items: center;
    margin-top: 33px;
  }

  .btn-cancel-add-options-detail {
    box-shadow: var(--shadow-level-2);
  }

  .localization {
    width: 100%;
  }

  .add-options-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    button {
      box-shadow: var(--shadow-level-2);
      color: var(--primary-colours-blue) !important;
    }
  }

}

.modal-option__footer {
  display: flex;
  justify-content: center;
  gap: 16px;

}

.modal-createConversation {
  .btn-cancel-add-conversation {
    box-shadow: var(--shadow-level-2);
  }

  .btn-add-conversation {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding-bottom: 20px;

    button {
      box-shadow: var(--shadow-level-2);
      color: var(--primary-colours-blue) !important;
    }
  }
}