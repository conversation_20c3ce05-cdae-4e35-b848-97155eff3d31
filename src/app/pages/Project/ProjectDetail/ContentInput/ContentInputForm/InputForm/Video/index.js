import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { Form, Input } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import ReactPlayer from "react-player";
import _ from "lodash";

import { useContentInput } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm";

import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import TimelineSlider from "@component/TimelineSlider";
import DurationInput from "@component/DurationInput";
import ResourceInput from "./ResourceInput";

import { BUTTON, CONSTANT } from "@constant";
import RULE from "@rule";
import { API } from "@api";
import { getYoutubeVideoId } from "@common/functionCommons";
import { getVideoDetail } from "@services/Video";
import { getOfflineVideoDetail } from "@services/OfflineVideo";

import PlaySoft from "@component/SvgIcons/Play/PlaySoft";

import "./Video.scss";


const VIDEO_DATA_INIT = {
  videoId: undefined,
  offlineVideoId: undefined,
  videoLength: 0,
  selectedDuration: { start: 0, end: 0 },
  thumbnail: null,
  videoType: null,
};

function Video({ maxDurationSeconds, ...props }) {
  const { t } = useTranslation();
  const [videoForm] = Form.useForm();
  
  const { formKey } = useContentInput();
  const { content, inputData, isDisabledContent, setFetchingContent, toolInfo } = props;
  
  const [, updateState] = useState();
  const forceUpdate = useCallback(() => updateState({}), []);
  
  const videoRef = React.useRef(null);
  const isSeekToStart = React.useRef(false);
  const isPlaying = React.useRef(false);
  
  const [isLoading, setLoading] = useState(false);
  const [videoData, setVideoData] = useState(VIDEO_DATA_INIT);
  
  const [stateResource, setStateResource] = useState({ isShowModal: false });
  
  
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.seekTo(videoData.selectedDuration.start || 0);
      if (!isPlaying.current) {
        isPlaying.current = true;
        forceUpdate();
        isPlaying.current = false;
        forceUpdate();
      }
    }
  }, [videoData.selectedDuration.start]);
  
  useEffect(() => {
    if (inputData) {
      getVideoData();
    } else {
      clearVideoForm();
    }
  }, [inputData?.url, inputData?.offlineVideoId]);
  
  function clearVideoForm() {
    setVideoData(VIDEO_DATA_INIT);
    videoForm.resetFields();
  }
  
  async function getVideoData() {
    setFetchingContent(true);
    if (inputData?.url) {
      await getYoutubeVideoInfo();
    } else if (inputData?.offlineVideoId) {
      await getOfflineVideoInfo();
    }
    setFetchingContent(false);
  }
  
  function setDataForm(values) {
    videoForm.setFieldsValue(values);
    props.onChange(values);
  }
  
  useEffect(() => {
    if (videoData) {
      setDataForm({
        offlineVideoId: videoData.offlineVideoId?._id,
        cutStart: videoData.selectedDuration.start,
        cutEnd: videoData.selectedDuration.end,
        videoType: videoData.videoType,
        videoId: videoData.videoId,
      });
    }
  }, [videoData]);
  
  
  function toggleResourceModal(resourceType = null) {
    setStateResource({ isShowModal: !!resourceType });
  }
  
  function onUseResource(resourceVideo) {
    const { offlineVideoId, videoId } = resourceVideo;
    if (videoId) {
      onUseYoutubeVideo(videoId);
    } else {
      onUseOfflineVideo(offlineVideoId);
    }
    toggleResourceModal();
  }
  
  function onUseYoutubeVideo(videoId) {
    setDataForm({
      url: videoId.url,
    });
    loadVideoYoutube();
  }
  
  const debouncedLoadVideoYoutube = useCallback(_.debounce(loadVideoYoutube, 500), []);
  
  async function loadVideoYoutube() {
    const url = videoForm.getFieldValue("url");
    if (!url) {
      setVideoData(VIDEO_DATA_INIT);
      return;
    }
    
    setLoading(true);
    const videoId = getYoutubeVideoId(url);
    const apiResponse = await getVideoDetail(videoId, true);
    if (apiResponse?.lengthSeconds) {
      setVideoData({
        videoId,
        videoLength: apiResponse.lengthSeconds,
        selectedDuration: {
          start: 0,
          end: apiResponse.lengthSeconds > maxDurationSeconds
            ? maxDurationSeconds
            : apiResponse.lengthSeconds,
        },
        thumbnail: apiResponse?.thumbnailBase64,
        videoType: CONSTANT.YOUTUBE_VIDEO,
      });
    } else {
      setVideoData(VIDEO_DATA_INIT);
      videoForm.setFields([{ name: "url", errors: [apiResponse.message] }]);
    }
    setLoading(false);
  }
  
  function onUseOfflineVideo(offlineVideoId) {
    setDataForm({
      url: offlineVideoId?.name,
    });
    
    if (typeof offlineVideoId?.videoFileId?._id === "string") {
      offlineVideoId.videoFileId = offlineVideoId.videoFileId._id;
    }
    setVideoData({
      offlineVideoId,
      selectedDuration: { start: 0, end: 0 },
      videoLength: 0,
      videoType: CONSTANT.OFFLINE_VIDEO,
    });
  }
  
  async function getYoutubeVideoInfo() {
    setLoading(true);
    setDataForm({ url: inputData.url });
    
    const videoId = getYoutubeVideoId(inputData.url);
    const apiResponse = await getVideoDetail(videoId);
    if (apiResponse?.lengthSeconds) {
      setVideoData({
        videoId,
        videoLength: apiResponse.lengthSeconds,
        selectedDuration: { start: inputData.cutStart, end: inputData.cutEnd },
        thumbnail: apiResponse?.thumbnailBase64,
        videoType: CONSTANT.YOUTUBE_VIDEO,
      });
    } else {
      setVideoData(VIDEO_DATA_INIT);
    }
    setLoading(false);
  }
  
  async function getOfflineVideoInfo() {
    if (!inputData?.offlineVideoId || inputData.offlineVideoId === videoData?.offlineVideoId?._id) return;
    setLoading(true);
    
    const apiResponse = await getOfflineVideoDetail(inputData.offlineVideoId);
    if (apiResponse) {
      setDataForm({
        url: apiResponse?.name,
      });
      setVideoData(prevState => Object.assign({}, prevState,
        {
          offlineVideoId: apiResponse,
          selectedDuration: { start: inputData.cutStart, end: inputData.cutEnd },
          videoType: CONSTANT.OFFLINE_VIDEO,
          videoId: null,
        }));
    }
    
    setLoading(false);
  }
  
  async function onValuesChange(changedValues, allValues) {
    if (changedValues.hasOwnProperty("url")) {
      debouncedLoadVideoYoutube();
    }
  }
  
  function handleChangeDuration(selectedDuration) {
    setVideoData({ ...videoData, selectedDuration });
  }
  
  
  function onEndedYouTubeVideo() {
    if (videoRef.current && videoData.selectedDuration.start) {
      videoRef.current.seekTo(videoData.selectedDuration.start);
      isSeekToStart.current = true;
      isPlaying.current = false;
      forceUpdate();
    }
  }
  
  function onPlayYouTubeVideo() {
    if (isSeekToStart.current) {
      isSeekToStart.current = false;
    } else {
      isPlaying.current = true;
    }
  }
  
  function onPauseYouTubeVideo() {
    isPlaying.current = false;
    forceUpdate();
  }
  
  function onDuration(duration) {
    const updatedOfflineVideo = { videoLength: duration };
    if (!inputData?.offlineVideoId || videoData?.offlineVideoId?._id !== inputData?.offlineVideoId) {
      updatedOfflineVideo.selectedDuration = { start: 0, end: Math.min(maxDurationSeconds, duration) };
    }
    setVideoData(prevState => Object.assign({}, prevState, updatedOfflineVideo));
    setDataForm({ isUploading: false });
  }
  
  const youtubeUrl = useMemo(() => {
    return CONSTANT.YTB_EMBED_URL_START.format(videoData.videoId, videoData.selectedDuration.start);
  }, [videoData.videoId]);
  
  const isPreviewOfflineVideo = !!videoData?.offlineVideoId?.videoFileId;
  const isPreviewOnlineVideo = !!videoData?.videoId;
  
  return <>
    <AntForm
      id={`${CONSTANT.VIDEO}-${content?._id}${formKey}`}
      name={`${CONSTANT.VIDEO}-${content?._id}${formKey}`}
      className="form-video-input"
      form={videoForm} layout="vertical"
      onValuesChange={onValuesChange}
      disabled={isDisabledContent}
    >
      <AntForm.Item
        name="url"
        className="youtube-url"
        htmlFor=""
        label={<div className="youtube-url-label">
          <label htmlFor={`${CONSTANT.VIDEO}-${content._id}_url`}>
            {toolInfo?.inputLabel || t("YOUTUBE_OR_OFFLINE_VIDEO")}
          </label>
          
          {!isDisabledContent && <AntButton
            size="xsmall"
            className="select-from-resource-button"
            type={BUTTON.WHITE_BLUE}
            icon={<PlaySoft />}
            onClick={() => toggleResourceModal(CONSTANT.VIDEO)}
          >
            {t("SELECT_FROM_RESOURCE")}
          </AntButton>}
        </div>}
        rules={[RULE.REQUIRED]}
      >
        {/*<Input size="large" placeholder={t("VIDEO_INPUT_PLACEHOLDER")}/>*/}
        <Input size="large" placeholder={toolInfo?.inputPlaceholder || t("VIDEO_INPUT_PLACEHOLDER")} />
      </AntForm.Item>
      <Form.Item hidden name="isUploading">
        <Input readOnly />
      </Form.Item>
      
      <Form.Item hidden name="cutStart">
        <Input readOnly />
      </Form.Item>
      
      <Form.Item hidden name="cutEnd">
        <Input readOnly />
      </Form.Item>
      
      <Form.Item hidden name="offlineVideoId">
        <Input readOnly />
      </Form.Item>
      
      <Form.Item hidden name="videoId">
        <Input readOnly />
      </Form.Item>
      
      <Form.Item hidden name="videoType">
        <Input readOnly />
      </Form.Item>
    
    </AntForm>
    
    {isPreviewOnlineVideo && <div className="youtube-frame-preview">
      <Loading active={isLoading}>
        <div className="youtube-frame-inner">
          <div className="youtube-embed">
            <ReactPlayer
              ref={videoRef}
              url={youtubeUrl}
              controls
              playing={isPlaying.current}
              width="100%"
              height="100%"
              progressInterval={0}
              onPlay={onPlayYouTubeVideo}
              onPause={onPauseYouTubeVideo}
              onEnded={onEndedYouTubeVideo}
            />
          </div>
          <img
            src={`data:image/png;base64,${videoData?.thumbnail}`}
            className="youtube-embed-thumbnail"
            alt=""
          />
        </div>
      </Loading>
    </div>}
    
    {isPreviewOfflineVideo && <div className="offline-video-preview-container">
      <ReactPlayer
        ref={videoRef}
        className="offline-video-preview"
        url={API.STREAM_MEDIA.format(videoData.offlineVideoId.videoFileId)}
        controls
        width="496px"
        height="279px"
        progressInterval={0}
        onDuration={onDuration}
      />
    </div>}
    
    <Form layout="vertical">
      <DurationInput
        value={videoData.selectedDuration}
        maxValue={videoData.videoLength}
        disabled={!videoData.videoLength || isDisabledContent}
        onChange={handleChangeDuration}
      />
    </Form>
    
    <TimelineSlider
      maxSlider={videoData.videoLength}
      value={[videoData.selectedDuration.start, videoData.selectedDuration.end]}
      onChange={handleChangeDuration}
      disabled={!videoData.videoLength || isDisabledContent}
    />
    
    <ResourceInput
      stateResource={stateResource}
      toggleResourceModal={toggleResourceModal}
      onUseResource={onUseResource}
    />
  </>;
}

function mapStateToProps(store) {
  const { maxDurationSeconds } = store.app;
  return { maxDurationSeconds };
}

export default connect(mapStateToProps)(Video);