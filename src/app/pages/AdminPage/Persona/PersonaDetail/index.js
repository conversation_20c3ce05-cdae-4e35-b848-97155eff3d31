import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Card, Col, Divider, Form, Input, Row, Select, Tooltip } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import { InfoCircleOutlined } from "@ant-design/icons";

import { BUTTON, CONSTANT, OPTION_TOOL_WELCOME, SHOW_ON_WELCOME } from "@constant";

import { getAllTemplate } from "@services/Template";

import { LINK } from "@link";

import AntButton from "@component/AntButton";
import clsx from "clsx";
import CancelIcon from "@component/SvgIcons/CancelIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";
import Loading from "@component/Loading";

import { cloneObj } from "@common/functionCommons";
import { getAllToolAvailable } from "@services/Tool";
import { createPersona, getAllPersona, getPersona, updatePersona } from "@services/Persona";
import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";

import "./PersonaDetail.scss";


PersonaDetail.propTypes = {};

function PersonaDetail(props) {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [formPersona] = Form.useForm();
  const [allTemplates, setAllTemplates] = useState([]);
  const [typeOfTools, setTypeOfTools] = useState([]);
  const [allTools, setAllTools] = useState([]);
  const [allPersona, setAllPersona] = useState([]);
  const [detailPersona, setDetailPersona] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getTemplateAndTools();
    getAPIAllPersona();
  }, []);

  useEffect(() => {
    getPersonaData();
  }, [id]);

  const getTypeOfTools = (typeTools) => {
    switch (typeTools.trim().toUpperCase()) {
      case CONSTANT.VIDEO:
        return CONSTANT.VIDEO;
      case CONSTANT.OFFLINE_VIDEO:
        return CONSTANT.VIDEO;
      case CONSTANT.TOPIC:
        return CONSTANT.DOCUMENT;
      case CONSTANT.TEXT:
        return CONSTANT.DOCUMENT;
      case CONSTANT.AUDIO:
        return CONSTANT.AUDIO;
      case CONSTANT.IMAGE:
        return CONSTANT.IMAGE;
      default:
        return typeTools;
    }
  };

  const getAPIAllPersona = async () => {
    const apiResponse = await getAllPersona();
    if (apiResponse) {
      setAllPersona(apiResponse);
    }
  };

  const getAvaliblePersona = () => {
    let optionsSelected = [];
    allPersona?.forEach((item) => {
      if (item?.code !== detailPersona?.code) optionsSelected.push(item?.code);
    });
    const availableOptions = SHOW_ON_WELCOME.filter((option) => !optionsSelected.includes(option.value));
    return availableOptions;
  };

  const getPersonaData = async () => {
    if (id) {
      setLoading(true);
      let dataTools = [];
      const apiResponse = await getPersona(id, ["toolId"]);
      let groupedByCategories = {};

      apiResponse?.toolId?.forEach((obj) => {
        if (!groupedByCategories[getTypeOfTools(obj.inputType)]) {
          groupedByCategories[getTypeOfTools(obj.inputType)] = [];
        }
        groupedByCategories[getTypeOfTools(obj.inputType)].push(obj._id);
      });

      let outputArray = Object.keys(groupedByCategories).map((key) => ({
        type: key,
        tools: groupedByCategories[key],
      }));
      outputArray.map((res, index) => {
        onSelect({ value: res.type }, index, setTypeOfTools);
      });
      setDetailPersona(apiResponse);
      formPersona.setFieldsValue({ ...apiResponse, options: outputArray });
      setLoading(false);
    }
  };

  const getTemplateAndTools = async () => {
    setLoading(true);
    const apiTemplateResponse = await getAllTemplate({ type: CONSTANT.SYSTEM });
    if (apiTemplateResponse) {
      setAllTemplates(apiTemplateResponse);
    }
    const apiToolsReponse = await getAllToolAvailable();
    if (apiToolsReponse) {
      setAllTools(apiToolsReponse);
    }
    setLoading(false);
  };
  const getAvalibleOptionsTools = (typeTools) => {
    let avalibleTools = [];
    if (typeTools === CONSTANT.VIDEO) {
      avalibleTools = allTools.filter((item) => {
        return [CONSTANT.VIDEO, CONSTANT.OFFLINE_VIDEO].includes(item?.inputType?.trim().toUpperCase());
      });
    } else if (typeTools === CONSTANT.DOCUMENT) {
      avalibleTools = allTools.filter((item) => {
        return [CONSTANT.TOPIC, CONSTANT.TEXT].includes(item?.inputType.trim().toUpperCase());
      });
    } else if (typeTools === CONSTANT.AUDIO) {
      avalibleTools = allTools.filter((item) => {
        return [CONSTANT.AUDIO].includes(item?.inputType.trim().toUpperCase());
      });
    } else if (typeTools === CONSTANT.IMAGE) {
      avalibleTools = allTools.filter((item) => {
        return [CONSTANT.IMAGE].includes(item?.inputType.trim().toUpperCase());
      });
    } else return avalibleTools;
    return avalibleTools.map((res) => ({ label: res.name, value: res._id }));
  };

  const filterOption = (input, option) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase());

  const getAvailabeOptions = (index, options) => {
    let optionsSelected = [];
    options?.forEach((lang, optionIndex) => {
      if (optionIndex !== index) {
        optionsSelected.push(lang);
      }
    });
    const availableOptions = OPTION_TOOL_WELCOME.filter((option) => !optionsSelected.includes(option.value));
    return availableOptions;
  };

  const onSelect = (data, index, setData) => {
    const { value } = data;
    setData((pre) => {
      const newData = cloneObj(pre);
      newData[index] = value;
      return newData;
    });
  };

  const handleCancel = () => {
    navigate(-1);
  };

  const formSubmit = async (dataForm) => {
    setLoading(true);
    const allToolIds = dataForm.options.map((option) => option.tools).flat();
    delete dataForm.options;
    if (!id) {
      const apiResponse = await createPersona({ ...dataForm, toolId: allToolIds });
      if (apiResponse) {
        toast.success(t("CREATE_PERSONA_SUCCESSFULLY"));
        navigate(LINK.ADMIN_PAGE + LINK.PERSONA_DETAIL.format(apiResponse?._id));
      } else {
        toast.error(t("CREATE_PERSONA_ERROR"));
      }
    } else {
      const apiResponse = await updatePersona({ ...dataForm, toolId: allToolIds, _id: id });
      if (apiResponse) {
        toast.success(t("UPDATE_PERSONA_SUCCESSFULLY"));
      } else {
        toast.error(t("UPDATE_PERSONA_ERROR"));
      }
    }
    setLoading(false);
  };

  const onRemove = (index, name, remove, setData) => {
    remove(index, name);
    setData((pre) => {
      const newData = cloneObj(pre);
      newData.splice(index, 1);
      return newData;
    });
  };
  return (
    <Loading active={loading} transparent>
      <div className="persona-detail-container">
        <Card className="persona-detail-header-card">
          <div className="persona-detail-header">
            <div>
              <h1 className="persona-detail-title">{id ? t("PERSONA_DETAIL") : t("CREATE_PERSONA")}</h1>
              <p className="persona-detail-description">{id ? t("PERSONA_DETAIL_DESCRIPTION") : t("CREATE_PERSONA_DESCRIPTION")}</p>
            </div>
          </div>
        </Card>

        <Card className="persona-detail-form-card">
          <Form
            form={formPersona}
            layout="vertical"
            size={"large"}
            name="form_persona"
            onFinish={formSubmit}
            initialValues={{}}
          >
            <div className="persona-detail-form-section">
              <h2 className="section-title">{t("BASIC_INFORMATION")}</h2>
              <Row gutter={24}>
                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                  <Form.Item
                    name="code"
                    label={
                      <span>
                        {t("PERSONA_NAME")}
                        <Tooltip title={t("PERSONA_NAME_DESC")}>
                          <InfoCircleOutlined className="info-icon" />
                        </Tooltip>
                      </span>
                    }
                    rules={[
                      {
                        required: true,
                        message: t("PERSONA_NAME_REQUIRED"),
                      },
                    ]}
                  >
                    <Select
                      showSearch
                      options={getAvaliblePersona()}
                      filterOption={filterOption}
                      placeholder={t("SELECT_PERSONA_NAME")}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                  <Form.Item
                    name="templateId"
                    label={
                      <span>
                        {t("TEMPLATE")}
                        <Tooltip title={t("TEMPLATE_DESC")}>
                          <InfoCircleOutlined className="info-icon" />
                        </Tooltip>
                      </span>
                    }
                    rules={[
                      {
                        required: true,
                        message: t("TEMPLATE_REQUIRED"),
                      },
                    ]}
                  >
                    <Select
                      options={allTemplates.map((template) => ({ label: template.name, value: template._id }))}
                      mode="multiple"
                      filterOption={filterOption}
                      placeholder={t("SELECT_TEMPLATE")}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </div>
            <Divider className="section-divider" />

            <div className="persona-detail-form-section">
              <h2 className="section-title">
                {t("TOOL_OPTIONS")}
                <Tooltip title={t("TOOL_OPTIONS_DESC")}>
                  <InfoCircleOutlined className="info-icon-section" />
                </Tooltip>
              </h2>

              <Form.Item required>
                <Form.List name="options">
                  {(fields, { add, remove }) => {
                    if (fields.length === 0) add();
                    return (
                      <div className={"persona-detail-options-container"}>
                        {fields.map(({ key, name, ...restField }, index) => (
                          <Card key={key} className="option-card">
                            <div className={"persona-detail-option-item"}>
                              <Row gutter={24} className="items-baseline">
                                <Col xs={24} lg={6}>
                                  <Form.Item
                                    {...restField}
                                    label={
                                      <span>
                                        {t("OPTION_TYPE")}
                                        <Tooltip title={t("OPTION_TYPE_DESC")}>
                                          <InfoCircleOutlined className="info-icon" />
                                        </Tooltip>
                                      </span>
                                    }
                                    name={[name, "type"]}
                                    rules={[{ required: true, message: t("OPTION_TYPE_REQUIRED") }]}
                                  >
                                    <Select
                                      options={getAvailabeOptions(index, typeOfTools)}
                                      placeholder={t("SELECT_OPTION_TYPE")}
                                      onSelect={(_, option) => onSelect(option, index, setTypeOfTools)}
                                    />
                                  </Form.Item>
                                </Col>
                                {typeOfTools[index] && (
                                  <Col xs={24} lg={18}>
                                    <Form.Item
                                      {...restField}
                                      name={[name, "tools"]}
                                      label={
                                        <span>
                                          {t("TOOLS")}
                                          <Tooltip title={t("TOOLS_DESC")}>
                                            <InfoCircleOutlined className="info-icon" />
                                          </Tooltip>
                                        </span>
                                      }
                                      rules={[{ required: true, message: t("TOOLS_REQUIRED") }]}
                                    >
                                      <Select
                                        options={getAvalibleOptionsTools(typeOfTools[index])}
                                        placeholder={t("SELECT_TOOLS")}
                                        mode="multiple"
                                        filterOption={filterOption}
                                      />
                                    </Form.Item>
                                  </Col>
                                )}
                              </Row>
                              <div className={"persona-detail-item-action"}>
                                <Tooltip title={t("REMOVE_OPTION")}>
                                  <AntButton
                                    type={BUTTON.WHITE}
                                    shape={"circle"}
                                    className={"btn-remove-option"}
                                    icon={<CancelIcon/>}
                                    size={"small"}
                                    onClick={() => onRemove(index, name, remove, setTypeOfTools)}
                                  />
                                </Tooltip>
                              </div>
                            </div>
                          </Card>
                        ))}

                        {fields.length < 4 && (
                          <div className={"add-option-actions"}>
                            <AntButton
                              type={BUTTON.WHITE_BLUE}
                              onClick={() => add()}
                              icon={<PlusIcon/>}
                            >
                              {t("ADD_OPTION")}
                            </AntButton>
                          </div>
                        )}
                      </div>
                    );
                  }}
                </Form.List>
              </Form.Item>
            </div>
            <div className="persona-detail-actions">
              <Row gutter={16} justify="center">
                <Col>
                  <AntButton onClick={handleCancel} type={BUTTON.WHITE}>
                    {t("BACK")}
                  </AntButton>
                </Col>
                <Col>
                  <AntButton htmlType="submit" type={BUTTON.DEEP_NAVY} loading={loading}>
                    {id ? t("SAVE") : t("CREATE")}
                  </AntButton>
                </Col>
              </Row>
            </div>
          </Form>
        </Card>
      </div>
    </Loading>
  );
}

export default PersonaDetail;
