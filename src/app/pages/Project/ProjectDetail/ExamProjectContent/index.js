import React, { useEffect, useMemo, useState } from "react";
import { Form, InputNumber } from "antd";
import { useTranslation } from "react-i18next";

import { toast } from "@component/ToastProvider";
import { useProject } from "@app/pages/Project";
import { AntForm } from "@component/AntForm";

import AntButton from "@component/AntButton";
import DynamicForm from "@component/DynamicForm";
import ExamProjectInput from "@app/pages/Project/ProjectDetail/ExamProjectContent/ExamProjectInput";
import ExamProjectOutput from "@app/pages/Project/ProjectDetail/ExamProjectContent/ExamProjectOutput";
import confirmNotSupport from "@app/component/ConfirmNotSupport";

import { BUTTON, FIELD_TYPE } from "@constant";
import { getToolInfo, handleDataRequest } from "@app/pages/Project/ProjectDetail/projectCommons";
import { checkMediaExistInHtml, cloneObj, stringToBool } from "@common/functionCommons";

import { getAllOptions } from "@services/Option";
import { submitExam } from "@services/Content";
import { getProjectDetail } from "@services/Project";


import "./ExamProjectContent.scss";


function ExamProjectContent() {
  const { t, i18n } = useTranslation();
  const [formSubmitExam] = Form.useForm();
  const { projectId, projectContentData, handleSaveContentData, projectData } = useProject();
  const { createContentThumbnail, totalExam, setExamOrderSelected } = useProject();
  const { examOptionData, setExamOptionData } = useProject();
  
  const [isSubmitting, setSubmitting] = useState(false);
  
  useEffect(() => {
    if (!isSubmitting) {
      createContentThumbnail();
    }
  }, [isSubmitting]);
  
  useEffect(() => {
    getOptionsData();
  }, []);
  
  const formFields = useMemo(() => {
    return cloneObj(examOptionData).map(option => {
      if (projectData?.commonOptions?.hasOwnProperty(option.code)) {
        option.defaultValue = projectData?.commonOptions?.[option.code];
      }
      const extraType = option.type?.toUpperCase();
      if ([FIELD_TYPE.SELECT, FIELD_TYPE.SELECT_MULTIPLE].includes(extraType)) {
        const selectOptions = option.selectOptions || [];
        const updateSelectOptions = selectOptions.map(optionSelect => {
          const label = optionSelect.label?.[i18n.language] || optionSelect.label;
          return { ...optionSelect, label };
        });
        option.selectOptions = updateSelectOptions;
      }
      return option;
    });
    
  }, [examOptionData, projectData?.commonOptions, i18n.language]);
  
  const getOptionsData = async () => {
    const query = { isExamOption: true };
    const dataResponse = await getAllOptions(query);
    if (Array.isArray(dataResponse)) {
      dataResponse.forEach(field => {
        field.width ||= 136;
      });
      setExamOptionData(dataResponse);
    }
  };
  
  
  async function onFormFinish(name, info) {
    if (name !== "form-submit-exam") return;
    
    if (!projectContentData?.length) {
      return toast.warning({ description: t("PLEASE_ADD_INPUT") });
    }
    
    const { numberOfExam, ...commonOptions } = info.forms["form-submit-exam"].getFieldValue();
    if (!numberOfExam) {
      return toast.warning({ description: t("PLEASE_ENTER_NUMBER_OF_EXAM") });
    }
    
    
    const formData = { ...info.forms };
    delete formData["form-submit-exam"];
    
    const formGrouped = {};
    Object.keys(formData).forEach(formName => {
      const [_, formContentId] = formName.split("-");
      formGrouped[formContentId] ||= {};
      formGrouped[formContentId][formName] = formData[formName];
    });
    
    let isValid = true;
    const contentRequest = [], allExamData = {};
    for (const formContentId of Object.keys(formGrouped)) {
      const content = projectContentData.find(content => content._id === formContentId);
      const toolData = getToolInfo(content);
      const inputData = await handleDataRequest(formGrouped[formContentId], toolData);
      if (inputData) {
        const examData = { inputData, contentId: formContentId, inputType: toolData.inputType };
        allExamData[formContentId] = examData;
        contentRequest.push(examData);
      } else {
        isValid = false;
      }
    }
    if (!isValid) return;
    
    
    let ckeditorHasMedia = false;
    Object.values(allExamData).forEach(dataItem => {
      if (dataItem.inputType === "html") {
        const existMedia = Object.values(dataItem.inputData).some(inputItem => checkMediaExistInHtml(inputItem));
        if (existMedia) ckeditorHasMedia = true;
      }
    });
    
    if (ckeditorHasMedia && !stringToBool(localStorage.getItem("clickeeNotSupportMedia"))) {
      const agreement = await confirmNotSupport();
      localStorage.setItem("clickeeNotSupportMedia", agreement.toString());
    }
    
    const apiRequest = {
      projectId,
      workspaceId: projectData?.workspaceId?._id || projectData?.workspaceId,
      data: contentRequest,
      numberOfExam,
      examCode: projectData.examCode,
      commonOptions,
    };
    
    setSubmitting(true);
    
    setTimeout(() => {
      document.getElementById("js-exam-output").scrollIntoView();
    }, 200);
    
    
    const apiResponse = await submitExam(apiRequest);
    if (Array.isArray(apiResponse)) {
      const projectResponse = await getProjectDetail(projectId);
      if (Array.isArray(projectResponse?.data?.content)) {
        const { content, ...projectDetail } = projectResponse.data;
        handleSaveContentData(content);
        setExamOrderSelected(projectDetail.activeExam);
      }
    }
    setSubmitting(false);
  }
  
  return <>
    
    <div className="exam-project-content">
      <div className="exam-project-header">
        <div className="exam-project-header__title">
          {projectData.projectName}
        </div>
        {/*<div className="exam-project-header__folder">
         <div className="exam-project-header__folder-icon">
         <img src={WORK_GREEN} alt="" />
         </div>
         {t(projectData.examCode?.toUpperCase())}
         </div>*/}
      </div>
      <Form.Provider onFormFinish={onFormFinish}>
        <AntForm
          form={formSubmitExam}
          className="exam-project__submit"
          name="form-submit-exam"
          size="large"
          disabled={isSubmitting}
        >
          <DynamicForm
            formFields={formFields}
          />
          
          <AntForm.Item initialValue={1} hidden name="numberOfExam">
            <InputNumber readOnly min={1} max={3} />
          </AntForm.Item>
          
          <AntButton
            size="large"
            className="exam-project__submit-button"
            type={BUTTON.DEEP_NAVY}
            htmlType="submit"
            disabled={isSubmitting}
          >
            {t("CREATE_AN_EXAM")}
          </AntButton>
        </AntForm>
        
        <ExamProjectInput
          isSubmitting={isSubmitting}
          formSubmitExam={formSubmitExam}
        />
      </Form.Provider>
      
      <ExamProjectOutput
        isSubmitting={isSubmitting}
      />
    </div>
  
  </>;
}

export default ExamProjectContent;