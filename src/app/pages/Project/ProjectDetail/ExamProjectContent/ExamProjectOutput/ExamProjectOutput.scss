.exam-project-output-container {

  .exam-project-output__change-output {
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: center;

    .change-output__prev,
    .change-output__next {
      .ant-btn-icon {
        svg {
          width: 24px;
          height: 24px;

          path {
            transition: stroke 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            //stroke: red
          }
        }
      }
    }

    .change-output__next {
    }

    .change-output__index {
      color: var(--primary-colours-blue);
      font-weight: 600;
      align-self: center;
      min-width: 50px;
      text-align: center;
    }
  }

  .exam-project-output__header-action {
    display: flex;
    gap: 8px;
  }

  .exam-project-output__body {
    margin-top: 6px;
    display: flex;
    flex-direction: column;
    gap: 41px;
  }

  .exam-project-output__loading {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;

    .exam-project-output__loading-title {
      display: flex;
      flex-direction: row;
      gap: 8px;
      align-items: center;
      font-size: 24px;
      font-weight: 600;

      svg {
        height: 24px;
        width: 24px;
      }
    }
  }

}