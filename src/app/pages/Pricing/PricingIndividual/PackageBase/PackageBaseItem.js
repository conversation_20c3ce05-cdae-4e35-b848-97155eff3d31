import AntButton from "@src/app/component/AntButton";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import CheckedIcon from "../../CheckedIcon";
import { connect } from "react-redux";
import { renderTextTime } from "@src/common/functionCommons";
import { Link } from "react-router-dom";
import { LINK } from "@link";
import { PROMOTION_TYPE } from "@constant";


const PackageBaseItem = ({ packageData, activeKey }) => {
  const { t, i18n } = useTranslation();

  const { paidType, name, prices, features, isUsingPackage } = packageData || {};

  const isFree = paidType === 'free';

  const VND = new Intl.NumberFormat("vi-VN", {
    currency: "VND",
  });

  const priceData = useMemo(() => {
    const originalPrice = prices.find(item => {
      return (item.intervalCount === activeKey
        || (activeKey === "12" && item.intervalCount === "1" && item.unitName === "year"));
    });
    if (!originalPrice) return { ...prices[0], unitAmount: 0, promotionalPrice: 0 };
    const { promotionType, promotion } = originalPrice || {};
    const { unitAmount } = originalPrice || {};
    let promotionalPrice = unitAmount;
    if (unitAmount) {
      if (promotionType === PROMOTION_TYPE.PERCENTAGE.value) {
        promotionalPrice = unitAmount - (unitAmount * promotion / 100);
      } else if (promotionType === PROMOTION_TYPE.FIXED.value) {
        promotionalPrice = unitAmount - promotion;
      }
    }
    return { ...originalPrice, promotionalPrice: promotionalPrice };
  }, [prices, activeKey]);

  const generateMoney = () => {
    const { unitAmount, promotionalPrice, intervalCount, unitName } = priceData || {};
    const monthNumber = renderMonthNumber(intervalCount, unitName);
    return <>
      {+promotionalPrice !== +unitAmount && <del>{VND.format(Math.round(unitAmount / monthNumber))}</del>}
      <span className="price">{VND.format(Math.round(monthNumber ? promotionalPrice / monthNumber : promotionalPrice))}</span>
      <span className="currency">{t("VND")}</span>
    </>
  }

  const renderMonthNumber = (intervalCount, unitName) => {
    if (unitName === 'year') return +intervalCount * 12;
    if (unitName === 'month') return +intervalCount;
    else return 0;
  }

  const generatePeriod = () => {
    const { intervalCount, unitName, promotionalPrice } = priceData || {};
    const monthNumber = renderMonthNumber(intervalCount, unitName);
    const monthText = renderTextTime(Math.round(+intervalCount), t(unitName.toUpperCase()), i18n.language);
    if ([1, 0].includes(monthNumber)) {
      return monthText;
    } else {
      return `x ${monthText} = ${VND.format(Math.round(promotionalPrice)) } ${t("VND")}`;
    }

  }

  const renderFeature = (feature) => {
    const featureName = feature?.name?.[i18n.language];
    if (isFree) return feature.count ? `${feature.count} ${featureName}` : featureName;
    return feature.count ? `${feature.count} ${featureName}/${t("MONTH")}` : featureName;
  }

  // if (isFree && !isUsingPackage) return null;

  return (
    <div className="pricing-package-base__item">
      <div className="pricing-package-base__item__content">
        <div className="item__content__info">
          <div className="item__content__info__name">{name}</div>
          <div className="item__content__info__price">{generateMoney()}</div>
          <div className="item__content__info__period">{generatePeriod()}</div>
        </div>
        <div className="item__content__features">
          {features?.map((feature, index) => (
            <div key={index} className="item__content__features__item">
              <CheckedIcon />
              <div className="item__content__features__item__info">
                {renderFeature(feature)}
              </div>
            </div>
          ))}
        </div>
      </div>

      <Link to={LINK.PAYMENT_ID.format(packageData?._id)} state={{ ...priceData }}>
        <AntButton
          size="large"
          className="pricing-package-base__item__btn"
          disabled={isFree}
        >
          {t("BUY_NOW")}
        </AntButton>
      </Link>
    </div>
  );
}

const mapStateToProps = (state) => ({
  user: state.auth.user,
});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(PackageBaseItem);