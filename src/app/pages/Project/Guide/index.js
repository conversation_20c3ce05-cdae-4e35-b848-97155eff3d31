import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";

import AntModal from "@component/AntModal";
import AntButton from "@component/AntButton";
import { Content } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";

import { BUTTON } from "@constant";
import { INPUT_GUIDE, SUBMIT_STEP, OUTPUT_STEP } from "./guideSteps";

import Union32 from "@component/SvgIcons/Union/Union32";

import "./Guide.scss";

function Guide() {
  const { t } = useTranslation();
  
  const { guideData, setGuideData } = useProject();
  const topMark = useRef(null);
  const bottomMark = useRef(null);
  const jsGuideStep = useRef(null);
  
  const [stepActive, setStepActive] = useState(0);
  
  useEffect(() => {
    setTimeout(() => {
      handleFocus();
    }, 500);
  }, [guideData.showGuide]);
  
  useEffect(() => {
    handleFocus();
  }, [stepActive]);
  
  
  const stepList = useMemo(() => {
    if (guideData?.toolInfo?.inputType) {
      const inputStep = INPUT_GUIDE[guideData.toolInfo.inputType];
      //  case TYPE_OF_TOOL.AUDIO:
      //  case TYPE_OF_TOOL.TEXT:
      //  case TYPE_OF_TOOL.TOPIC:
      //  case TYPE_OF_TOOL.WORDS:
      //  case TYPE_OF_TOOL.IMAGE:
      //  case TYPE_OF_TOOL.VIDEO:
      //  case TYPE_OF_TOOL.OFFLINE_VIDEO:
      
      return [...inputStep, SUBMIT_STEP, ...OUTPUT_STEP];
    }
    return [];
  }, [guideData?.toolInfo?._id]);
  
  function getElementOffset(elementClassName) {
    const element = document.getElementById("js-guide-content")
                            .getElementsByClassName(elementClassName)?.[0];
    if (!element || !element.offsetHeight) return null;
    const containerPosition = document.getElementById("js-guide-content")?.getBoundingClientRect();
    const elementTop = element.getBoundingClientRect().top - containerPosition.top;
    
    return { top: elementTop, bottom: elementTop + element.offsetHeight };
  }
  
  function handleNextStep() {
    setStepActive(prevState => prevState < stepList.length - 1 ? prevState + 1 : 0);
  }
  
  function handleFocus() {
    if (!guideData.showGuide) return;
    
    
    if (!topMark.current) topMark.current = document.getElementById("js-guide-top-mark");
    if (!bottomMark.current) bottomMark.current = document.getElementById("js-guide-bottom-mark");
    if (!jsGuideStep.current) jsGuideStep.current = document.getElementById("js-guide-step");
    
    const step = stepList[stepActive];
    let targetStartPosition = null;
    let targetEndPosition = null;
    
    for (let i = 0; i < step.target.start.length; i++) {
      targetStartPosition = getElementOffset(step.target.start[i]);
      if (targetStartPosition) break;
    }
    
    for (let i = 0; i < step.target.end.length; i++) {
      targetEndPosition = getElementOffset(step.target.end[i]);
      if (targetStartPosition) break;
    }
    
    if (targetStartPosition?.hasOwnProperty("top") && targetEndPosition?.hasOwnProperty("bottom")) {
      topMark.current.style.height = targetStartPosition.top - (step.isTop ? 24 : 12) + "px";
      bottomMark.current.style.top = targetEndPosition.bottom + (step.isBottom ? 24 : 12) + "px";
      
      jsGuideStep.current.style.top = targetStartPosition.top - (step.isTop ? 24 : 12) + "px";
      jsGuideStep.current.style.display = "block";
      
      scrollModalMark(targetStartPosition.top - (step.isTop ? 24 : 12) + 100);
    } else {
      setTimeout(() => {
        handleFocus();
      }, 10);
    }
  }
  
  function scrollModalMark(top, behavior = "smooth") {
    const modalMark = document.getElementsByClassName("guide-container")?.[0]?.parentNode;
    if (modalMark) {
      modalMark.scrollTo({ top, left: 0, behavior });
    }
  }
  
  function handleCloseGuide() {
    setGuideData({ ...guideData, showGuide: false });
    setTimeout(() => {
      if (topMark.current) topMark.current.style.height = "unset";
      if (bottomMark.current) bottomMark.current.style.top = "unset";
      if (jsGuideStep.current) {
        jsGuideStep.current.style.top = "unset";
        jsGuideStep.current.style.display = "none";
      }
      
      topMark.current = null;
      bottomMark.current = null;
      jsGuideStep.current = null;
      setStepActive(0);
    }, 500);
  }
  
  
  if (!guideData) return;
  return <>
    <AntModal
      width={1427}
      open={guideData.showGuide}
      className="guide-container"
      closeIcon={null}
      footerless
    >
      <div id="js-guide-content" className="guide-content">
        <Content content={guideData.content} />
        
        <div className="guide-mark" />
        <div id="js-guide-top-mark" className="guide-top-mark" />
        <div id="js-guide-bottom-mark" className="guide-bottom-mark" />
      </div>
      <div className="guide-step">
        <div id="js-guide-step" className="guide-step__border">
          <div className="guide-step__content">
            <div className="guide-step__header">
              <Union32 />
              {t("STEP")} {stepActive + 1}/{stepList.length}
            </div>
            <div className="guide-step__body">
              {t(stepList[stepActive]?.lang)}
            </div>
            <div className="guide-step__footer">
              {stepActive === stepList.length - 1
                ? <AntButton size="large" type={BUTTON.DEEP_NAVY} onClick={handleCloseGuide}>
                  {t("FINISH")}
                </AntButton>
                : <>
                  <AntButton size="large" onClick={handleCloseGuide}>
                    {t("CLOSE")}
                  </AntButton>
                  <AntButton size="large" type={BUTTON.DEEP_NAVY} onClick={handleNextStep}>
                    {t("NEXT")}
                  </AntButton>
                </>}
            </div>
          </div>
        </div>
      </div>
    </AntModal>
  </>;
}

export default Guide;