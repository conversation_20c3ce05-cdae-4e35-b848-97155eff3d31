import React, { useState } from "react";
import { <PERSON>ton, Card, Input, Table, Popconfirm, Typography, Space, Tooltip, Modal } from "antd";
import { DeleteOutlined, PlusOutlined, SoundOutlined, AppstoreAddOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import AI_GEN_STAR from "@src/assets/icons/ai-gen-star.svg";
import "../SpeakingParts.scss";

const { TextArea } = Input;

const SpeakingPart1 = ({
  questions,
  onQuestionsChange,
  onCreateAudio,
  onCreateHint,
  onShowHint,
  audioMap,
  loadingHintPart,
  loadingHintIndex,
  loadingAudioPart,
  loadingAudioIndex,
  onBatchAudioCreation,
  onBatchHintCreation,
  loadingBatchAudioPart,
  loadingBatchHintPart,
  isAnyActionRunning
}) => {
  const { t } = useTranslation();
  const [newQuestion, setNewQuestion] = useState("");
  const [isBulkAddModalVisible, setIsBulkAddModalVisible] = useState(false);
  const [bulkQuestions, setBulkQuestions] = useState("");
  const handleAddQuestion = () => {
    if (newQuestion.trim()) {
      const updatedQuestions = [
        ...questions,
        {
          id: `part1_${Date.now()}`,
          text: newQuestion.trim(),
          audioId: null
        }
      ];
      onQuestionsChange(updatedQuestions);
      setNewQuestion("");
    }
  };

  const handleDeleteQuestion = (index) => {
    const updatedQuestions = [...questions];
    updatedQuestions.splice(index, 1);
    onQuestionsChange(updatedQuestions);
  };

  const handleQuestionChange = (index, value) => {
    const updatedQuestions = [...questions];
    updatedQuestions[index].text = value;
    onQuestionsChange(updatedQuestions);
  };

  const handleCreateAudio = (index) => {
    onCreateAudio(questions[index].text, "part1", index);
  };

  const handleCreateHint = (index) => {
    onCreateHint(questions[index].text, "part1", index);
  };

  const handleShowHint = (index) => {
    onShowHint(questions[index].hint, "part1", index);
  };

  const handleBulkAddQuestions = () => {
    if (bulkQuestions.trim()) {
      // Tách các câu hỏi theo dòng và loại bỏ dòng trống
      const questionLines = bulkQuestions
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

      if (questionLines.length > 0) {
        const newQuestions = questionLines.map((questionText, index) => ({
          id: `part1_${Date.now()}_${index}`,
          text: questionText,
          audioId: null
        }));

        const updatedQuestions = [...questions, ...newQuestions];
        onQuestionsChange(updatedQuestions);
        setBulkQuestions("");
        setIsBulkAddModalVisible(false);
      }
    }
  };

  const handleBulkAddModalCancel = () => {
    setIsBulkAddModalVisible(false);
    setBulkQuestions("");
  };

  return (
    <Card
      title={<Typography.Title level={4}>{t("SPEAKING_PART1_TITLE")}</Typography.Title>}
      className="speaking-part1-container"
      extra={<Typography.Text type="secondary">{t("SPEAKING_PART1_SUBTITLE")}</Typography.Text>}
    >
      {/*<Typography.Paragraph className="part-description">*/}
      {/*  {t("SPEAKING_PART1_DESCRIPTION")}*/}
      {/*</Typography.Paragraph>*/}

      <Table
        dataSource={questions.map((question, index) => ({
          ...question,
          key: question.id,
          index: index
        }))}
        pagination={false}
        className="speaking-questions-table"
        columns={[
          {
            title: '#',
            dataIndex: 'index',
            key: 'index',
            width: 60,
            render: (text) => <span className="question-number">{text + 1}.</span>
          },
          {
            title: 'Question',
            dataIndex: 'text',
            key: 'text',
            render: (text, record) => (
              <TextArea
                value={text}
                onChange={(e) => handleQuestionChange(record.index, e.target.value)}
                autoSize={{ minRows: 1, maxRows: 1 }}
                className="question-textarea"
              />
            )
          },
          {
            title: 'Audio',
            dataIndex: 'audioId',
            key: 'audio',
            width: 300,
            render: (audioId, record) => (
              audioId && audioMap[audioId] ? (
                <audio
                  src={audioMap[audioId]}
                  controls
                  className="question-audio"
                />
              ) : null
            )
          },
          {
            title: 'Actions',
            key: 'actions',
            width: 150,
            render: (_, record) => (
              <Space>
                <Tooltip
                  title={record.audioId ? t("RECREATE_AUDIO_TOOLTIP") : t("CREATE_AUDIO_TOOLTIP")}
                  placement="top"
                >
                  <Button
                    icon={<SoundOutlined />}
                    onClick={() => handleCreateAudio(record.index)}
                    type={record.audioId ? "primary" : "default"}
                    className={!record.audioId ? "audio-btn-default" : ""}
                    size="small"
                    loading={loadingAudioPart === "part1" && loadingAudioIndex === record.index}
                    disabled={isAnyActionRunning && !(loadingAudioPart === "part1" && loadingAudioIndex === record.index)}
                  />
                </Tooltip>
                <Tooltip
                  title={record.hint ? t("SHOW_HINT_TOOLTIP") : t("CREATE_HINT_TOOLTIP")}
                  placement="top"
                >
                  <Button
                    onClick={() => record.hint ? handleShowHint(record.index) : handleCreateHint(record.index)}
                    size="small"
                    className={record.hint ? "ai-hint-btn show-hint" : "ai-hint-btn"}
                    loading={!record.hint && loadingHintPart === "part1" && loadingHintIndex === record.index}
                    disabled={isAnyActionRunning && !(loadingHintPart === "part1" && loadingHintIndex === record.index)}
                  >
                    {!(loadingHintPart === "part1" && loadingHintIndex === record.index) &&
                      <img src={AI_GEN_STAR} alt="AI Generate Hint" className="ai-gen-star-icon" />}
                    {record.hint && <span className="hint-badge"></span>}
                  </Button>
                </Tooltip>
                <Popconfirm
                  title={t("DELETE_QUESTION_CONFIRM")}
                  description={t("DELETE_QUESTION_DESCRIPTION")}
                  onConfirm={() => handleDeleteQuestion(record.index)}
                  okText={t("YES")}
                  cancelText={t("NO")}
                  placement="left"
                  okButtonProps={{ danger: true }}
                  icon={<DeleteOutlined style={{ color: 'red' }} />}
                >
                  <Tooltip title={t("DELETE_QUESTION_TOOLTIP")} placement="top">
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      size="small"
                      disabled={isAnyActionRunning}
                    />
                  </Tooltip>
                </Popconfirm>
              </Space>
            )
          }
        ]}
        footer={() => (
          <div className="add-question-container">
            <div className="add-question-label">{t("ADD_NEW_QUESTION")}</div>
            <TextArea
              value={newQuestion}
              onChange={(e) => setNewQuestion(e.target.value)}
              placeholder={t("NEW_QUESTION_PLACEHOLDER")}
              autoSize={{ minRows: 1, maxRows: 3 }}
              disabled={isAnyActionRunning}
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault();
                  handleAddQuestion();
                }
              }}
            />
            <div className="add-question-buttons">
              <Tooltip title={t("ADD_QUESTION_TOOLTIP")} placement="top">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddQuestion}
                  disabled={!newQuestion.trim() || isAnyActionRunning}
                >
                  {t("ADD_QUESTION")}
                </Button>
              </Tooltip>
              <Tooltip title={t("ADD_MULTIPLE_QUESTIONS_TOOLTIP")} placement="top">
                <Button
                  type="dashed"
                  icon={<AppstoreAddOutlined />}
                  onClick={() => setIsBulkAddModalVisible(true)}
                  disabled={isAnyActionRunning}
                  className="add-multiple-questions-button"
                >
                  {t("ADD_MULTIPLE_QUESTIONS")}
                </Button>
              </Tooltip>
            </div>
          </div>
        )}
      />

      <div className="batch-operations-container">
        {/*<div className="batch-operations-label">{t("BATCH_OPERATIONS")}</div>*/}
        <div className="batch-operations-buttons">
          <Button
            type="primary"
            icon={<SoundOutlined />}
            onClick={() => onBatchAudioCreation("part1")}
            loading={loadingBatchAudioPart === "part1"}
            disabled={(questions.filter(q => !q.audioId).length === 0) || (isAnyActionRunning && loadingBatchAudioPart !== "part1")}
            className="batch-operation-button"
          >
            {t("CREATE_AUDIO_FOR_ALL")}
          </Button>
          <Button
            type="primary"
            icon={<img src={AI_GEN_STAR} alt="AI" className="ai-gen-star-icon" />}
            onClick={() => onBatchHintCreation("part1")}
            loading={loadingBatchHintPart === "part1"}
            disabled={(questions.filter(q => !q.hint).length === 0) || (isAnyActionRunning && loadingBatchHintPart !== "part1")}
            className="batch-operation-button ai-batch-button"
          >
            {t("CREATE_HINT_FOR_ALL")}
          </Button>
        </div>
      </div>

      <Modal
        title={t("ADD_MULTIPLE_QUESTIONS")}
        open={isBulkAddModalVisible}
        onOk={handleBulkAddQuestions}
        onCancel={handleBulkAddModalCancel}
        width={800}
        okText={t("ADD_QUESTIONS")}
        cancelText={t("CANCEL")}
        okButtonProps={{
          disabled: !bulkQuestions.trim() || isAnyActionRunning
        }}
        cancelButtonProps={{
          disabled: isAnyActionRunning
        }}
      >
        <div className="bulk-add-modal-content">
          <p className="bulk-add-description">
            {t("BULK_ADD_QUESTIONS_DESCRIPTION")}
          </p>
          <TextArea
            value={bulkQuestions}
            onChange={(e) => setBulkQuestions(e.target.value)}
            placeholder={t("BULK_QUESTIONS_PLACEHOLDER")}
            autoSize={{ minRows: 8, maxRows: 15 }}
            disabled={isAnyActionRunning}
          />
          <div className="bulk-add-info">
            <Typography.Text type="secondary">
              {t("BULK_ADD_INFO")}
            </Typography.Text>
          </div>
        </div>
      </Modal>
    </Card>
  );
};

export default SpeakingPart1;
