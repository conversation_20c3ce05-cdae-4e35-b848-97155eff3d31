.homepage-workspace-body {
  display: grid;
  gap: 24px;

  @media screen and (min-width: 768px) {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  @media screen and (max-width: 767.98px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .folder-project-list-wraper {
    @media screen and (min-width: 768px) {
      grid-column: span 3 / span 3;
    }

    @media screen and (max-width: 767.98px) {
      grid-column: span 2 / span 2;
    }
  }
}