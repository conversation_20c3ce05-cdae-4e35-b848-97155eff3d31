import React, { useEffect, useRef } from "react";

import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";
import { useProject } from "@app/pages/Project";

import TitleInput from "./TitleInput";
import DescriptionInput from "@component/Input/DescriptionInput";
import AbcdOutput from "./OutputElement/AbcdOutput";
import TextOutput from "./OutputElement/TextOutput";
import PlaintextOutput from "./OutputElement/PlaintextOutput";
import QuestionOutput from "./OutputElement/QuestionOutput";
import TrueFalseOutput from "./OutputElement/TrueFalseOutput";
import SelectTitleOutput from "./OutputElement/SelectTitleOutput";
import HtmlOutput from "./OutputElement/HtmlOutput";
import OpenQuestionOutput from "./OutputElement/OpenQuestionOutput";
import HtmlQuestionOutput from "./OutputElement/HtmlQuestionOutput";
import MarkTestWritingOutput from "./OutputElement/MarkTestWritingOutput";
import AudioOutput from "./OutputElement/AudioOutput";

import { BUTTON, CONSTANT } from "@constant";
import { renderPlainTextWithLineBreaks } from "@common/functionCommons";
import { activateResponse } from "@services/Response";

import ChevronLeft from "@component/SvgIcons/ChevronLeft";
import ChevronRight from "@component/SvgIcons/ChevronRight";
import AntButton from "@component/AntButton";

function ContentOutput({ ...props }) {
  const isFirst = useRef(true);
  
  const {
    handleChangeTitle,
    handleChangeDescription,
    isPreviewProject,
    isShowPlainText,
    setProjectContentData,
    isAllowEditing,
    submitState,
  } = useProject();
  const { content, responseSelected, isEditing, setEditing, outputForm } = useContent();
  
  useEffect(() => {
    if (!isFirst.current) {
      const element = document.getElementById(`project-content-${content._id}-output`);
      element.scrollIntoView({ behavior: "smooth" });
    } else {
      isFirst.current = false;
    }
  }, [isEditing]);
  
  function handleShowResponse(type) {
    let indexSelected = content?.responses?.findIndex(response => !!response.isActivate);
    setProjectContentData(prevState => {
      return prevState.map(state => {
        if (state._id === content._id) {
          switch (type) {
            case CONSTANT.PREV:
              indexSelected = indexSelected ? indexSelected - 1 : indexSelected;
              break;
            case CONSTANT.NEXT:
              indexSelected = indexSelected + 1 !== content.responses?.length ? indexSelected + 1 : indexSelected;
              break;
            default:
              break;
          }
          state.responses.forEach((response, index) => {
            response.isActivate = index === indexSelected;
          });
        }
        return state;
      });
    });
    
    // todo: debounce
    debounceActivate();
  }
  
  const debounceActivate = () => {
    const responseActive = content?.responses?.find(response => !!response.isActivate);
    activateResponse(responseActive._id);
  };
  
  
  if (!content.responses?.length) return null;
  
  const generateOutput = () => {
    if (responseSelected?.state !== CONSTANT.DONE.toLowerCase() ||
      (submitState.isSubmit && submitState.contentId === content._id)) return;
    
    const outputProps = { isEditing, setEditing, responseSelected, content, outputForm };
    
    if (isShowPlainText && isPreviewProject) {
      return <div className="result-response__plaintext">
        {renderPlainTextWithLineBreaks(responseSelected.plaintext)}
      </div>;
    }
    
    switch (responseSelected?.outputType) {
      case "html":
        return <HtmlOutput {...outputProps} />;
      case "html_questions":
        return <HtmlQuestionOutput {...outputProps} />;
      case "mark_test_writing":
        return <MarkTestWritingOutput {...outputProps} />;
      case "text":
      case "picture_description":
        return <TextOutput {...outputProps} />;
      case "abcd_question":
      case "multi_choice":
        return <AbcdOutput {...outputProps} />;
      case "summary":
      case "three_titles":
      case "three_ideas":
      case "options":
        return <SelectTitleOutput {...outputProps} />;
      case "open_question":
        return <OpenQuestionOutput {...outputProps} />;
      case "discuss_question":
        return <QuestionOutput {...outputProps} />;
      case "tf_question":
        return <TrueFalseOutput {...outputProps} />;
      case "words":
        return <PlaintextOutput {...outputProps} />;
      case "audio":
        return <AudioOutput {...outputProps} />;
      default:
        //return null;
        return <PlaintextOutput {...outputProps} />;
    }
  };
  const responseState = content?.responses[0].state;
  const isNotProcessing = responseState && responseState !== CONSTANT.PROCESSING.toLowerCase();
  const showTitle = content?.responses?.length > 1 || isNotProcessing;
  const responseActiveCurrent = content.responses.findIndex(response => response.isActivate) + 1;
  
  return <div id={`project-content-${content._id}-output`} className="project-content-output">
    {showTitle && <>
      <TitleInput
        content={content}
        value={content.title}
        onSubmit={title => handleChangeTitle(content._id, title)}
        showSubmit={!isPreviewProject && isAllowEditing && !responseSelected?.isExample}
      />
      <DescriptionInput
        value={content.description}
        onSubmit={des => handleChangeDescription(content._id, des)}
        showSubmit={!isPreviewProject && isAllowEditing && !responseSelected?.isExample}
      />
    </>}
    
    {!!content.responses?.length && !isPreviewProject && <div className="result-response__change-result">
      <div className="result-response__change-result-content">
        <AntButton
          size="xsmall"
          type={BUTTON.WHITE_BLUE}
          className="change-result__prev"
          icon={<ChevronLeft />}
          onClick={() => handleShowResponse(CONSTANT.PREV)}
          disabled={!isAllowEditing || isEditing || responseActiveCurrent === 1}
        />
        
        <div className="change-result__index">
          {responseActiveCurrent}/{content.responses.length}
        </div>
        
        <AntButton
          size="xsmall"
          type={BUTTON.WHITE_BLUE}
          className="change-result__next"
          icon={<ChevronRight />}
          onClick={() => handleShowResponse(CONSTANT.NEXT)}
          disabled={!isAllowEditing || isEditing || responseActiveCurrent === content.responses.length}
        />
      </div>
    </div>}
    
    {generateOutput()}
  </div>;
}


export default ContentOutput;
