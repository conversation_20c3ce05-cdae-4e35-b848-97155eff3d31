import { useTranslation } from "react-i18next";
import './PackageVinaphone.scss'
import CheckedIcon from "../../CheckedIcon";

const PackageVinaphoneItem = ({ packageData, isMobile }) => {
  const { t } = useTranslation();

  const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
  const smsHref = isIOS
    ? `sms:888&body=${packageData.code}`
    : `sms:888?body=${packageData.code}`;

  const renderTimer = () => {
    if (`${packageData?.prices?.[0]?.intervalCount} ${packageData?.prices?.[0]?.unitName}` === '1 day') {
      return "24 giờ";
    }
    if (`${packageData?.prices?.[0]?.intervalCount} ${packageData?.prices?.[0]?.unitName}` === '7 day') {
      return "07 ngày";
    }
    if (`${packageData?.prices?.[0]?.intervalCount} ${packageData?.prices?.[0]?.unitName}` === '1 month') {
      return "30 ngày";
    }
    if (`${packageData?.prices?.[0]?.intervalCount} ${packageData?.prices?.[0]?.unitName}` === '3 month') {
      return "90 ngày";
    }
    if (`${packageData?.prices?.[0]?.intervalCount} ${packageData?.prices?.[0]?.unitName}` === '6 month') {
      return "180 ngày";
    }
    if (`${packageData?.prices?.[0]?.intervalCount} ${packageData?.prices?.[0]?.unitName}` === '1 year') {
      return "360 ngày";
    }
  }

  function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  }

  const features = [
    `Quyền lợi data: ${packageData.vnptData}`,
    `Đối với tài khoản học sinh được chấm chữa <strong class="text-green-600">${packageData?.features?.[0]?.count} lượt bài Writing/ Speaking</strong> và <strong class="text-green-600">không giới hạn</strong> lượt sử dụng tính năng Dictation và Shadowing trong ${renderTimer()} kể từ khi đăng ký/gia hạn thành công`,
    `Đối với tài khoản giáo viên được <strong class="text-green-600">${packageData?.features?.[2]?.count} lượt tạo bài giảng/bài tập/bài kiểm tra</strong> trong ${renderTimer()} kể từ khi đăng ký/gia hạn thành công`,
  ];

  return (
    <div className="package-vinaphone-item">
      <div className="package-vinaphone-item__content">
        <div className="package-vinaphone-item__content__header">
          <div className="package-vinaphone-item__content__header__title">
            <div className="package-vinaphone-item__content__header__title__compose">
              <p>{t('COMPOSE')}</p>
              <span>{packageData.code}</span>
              <p>{t('OR_VINA')}</p>
              <span>DK_{packageData.code}</span>
              <p>{t('SEND_TO')} 888</p>
            </div>
            <div className="package-vinaphone-item__content__header__title__price">
              <div className="package-vinaphone-item__content__header__title__price__title">
                <span className="price">{formatNumber(packageData?.prices?.[0]?.unitAmount)}</span>
                <span className="currency">{t('VND')}</span>
              </div>
              <span className="duration">{renderTimer()}</span>
            </div>
          </div>
          <div className="package-vinaphone-item__content__header__border"></div>
        </div>
        <div className="package-vinaphone-item__content__body">
          {!!packageData.description && (
            <div className="package-vinaphone-item__content__body__item">
              <CheckedIcon />
              <div className="package-vinaphone-item__content__body__item__text" >
                {packageData.description}
              </div>
            </div>
          )}
          {features?.map((text, index) => (
            <div key={index} className="package-vinaphone-item__content__body__item">
              <CheckedIcon />
              <div dangerouslySetInnerHTML={{ __html: text }} className="package-vinaphone-item__content__body__item__text" />
            </div>
          ))}
        </div>
      </div>

      {isMobile && <div className="package-vinaphone-item__btn">
        <a
          className="package-vinaphone-item__btn__deep"
          href={smsHref}
        >
          {t("REGISTER_NOW")}
        </a>
      </div>}
    </div>
  );
}

export default PackageVinaphoneItem;