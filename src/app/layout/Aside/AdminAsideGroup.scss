@import "src/app/styles/scroll";

.admin-aside-container {
  padding: 8px 0;
}

.admin-aside-groups {
  .ant-collapse-item {
    border: none;
    margin-bottom: 4px;

    .ant-collapse-header {
      padding: 12px 8px;
      border-radius: 8px;
      transition: all 0.2s ease;
      align-items: center;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
        border-radius: 8px;
      }

      .ant-collapse-expand-icon {
        color: rgba(0, 0, 0, 0.65);
        transition: transform 0.3s ease;
      }
    }

    &.ant-collapse-item-active {
      .ant-collapse-header {
        // Không thêm background-color khi active

        .ant-collapse-expand-icon {
          transform: rotate(90deg);
        }
      }
    }

    .ant-collapse-content {
      border-top: none;

      .ant-collapse-content-box {
        padding: 8px 0 8px 36px;
      }
    }
  }

  .group-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .group-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      box-shadow: var(--shadow-level-1);
    }

    .group-title {
      font-weight: 600;
      font-size: 14px;
    }
  }

  .group-items {
    display: flex;
    flex-direction: column;

    .aside-item {
      margin-bottom: 4px;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      &.aside-item__active {
        box-shadow: var(--shadow-level-1);
        font-weight: 600;
      }

      .aside-item__icon {
        border-radius: 8px;
        box-shadow: var(--shadow-level-1);
      }

      .aside-item__title {
        align-self: center;
        color: #000000;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  // Badge styles for new or important items
  .badge-new {
    position: relative;

    &::after {
      content: 'New';
      position: absolute;
      top: -6px;
      right: -6px;
      background-color: var(--primary-colours-blue);
      color: white;
      font-size: 10px;
      padding: 1px 6px;
      border-radius: 10px;
      font-weight: 600;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      animation: pulse 2s infinite;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.9;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
}
