import { useMemo } from "react";
import { Dropdown } from "antd";
import { connect, useDispatch } from "react-redux";
import { Link, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { LINK } from "@link";
import { BUTTON, CONSTANT } from "@constant";

import Setting from "@component/SvgIcons/Setting";
import Logout from "@component/SvgIcons/Logout";
import AntButton from "@component/AntButton";

import * as auth from "@src/ducks/auth.duck";

import ChevronDown from "@src/app/component/SvgIcons/ChevronDown";
import AVARTAR_DEFAULT from "@src/asset/icon/user/student-avatar.svg";

import "./HeaderAction.scss";
import SelectLanguage from "@src/app/component/SelectLanguage";
import { actions, TRACKING_ACTIONS, TRACKING_EVENTS, TRACKING_SCREENS } from "@src/ducks/tracking.duck";

function HeaderAction({ user, ...props }) {
  const { t } = useTranslation();
  const location = useLocation();
  const dispatch = useDispatch();

  const items = useMemo(() => {
    let menus = [];
    if (user.isSystemAdmin) menus.push({
      key: LINK.TOOL_STUDIO,
      label: <Link to={LINK.ADMIN_INSTRUCTION}>{t("ADMIN_MANAGER")}</Link>,
      icon: <Setting />,
    });
    if (user?.role === CONSTANT.ADMIN) menus.push({
      key: LINK.ORGANIZATION,
      label: <Link to={LINK.ORGANIZATION}>{t("ORGANIZATION_SETTING")}</Link>,
      icon: <Setting />,
    });

    return [...menus,
      {
        key: LINK.ACCOUNT,
        label: <Link to={LINK.ACCOUNT}
                     onClick={() => {
                       dispatch(actions.trackClickNavigation(TRACKING_SCREENS.PROFILE));
                     }}
        >{t("ACCOUNT_SETTINGS")}</Link>,
        icon: <Setting/>,
      },
    {
      key: LINK.LOGOUT,
      label: t("LOGOUT"),
      icon: <Logout />,
      onClick: props.logout,
    },
    ];
  }, [t, user]);

  return <div className="header-action">
    {/* <div className="language" id="js-language-header">
      <img
        className="language__flag"
        src={i18n.language === LANGUAGE.EN ? EN_FLAG : VN_FLAG}
        alt="" />
      <span className="language__text">{t("LANG_STUDENT")}</span>
      <Dropdown
        menu={{
          selectedKeys: [i18n.language],
          items: [
            { key: LANGUAGE.EN, label: t("ENG"), icon: <img src={EN_FLAG} alt="ENG" />, onClick: () => handleChangeLang(LANGUAGE.EN) },
            { key: LANGUAGE.VI, label: t("VIE"), icon: <img src={VN_FLAG} alt="VN" />, onClick: () => handleChangeLang(LANGUAGE.VI) },
          ],
        }}
        placement="bottomRight"
        className="language__dropdown-btn"
        trigger="click"
        getPopupContainer={() => document.getElementById("js-language-header")}
      >
        <AntButton type={BUTTON.GHOST_WHITE} size="xsmall" icon={<ChevronDown />} />
      </Dropdown>
    </div> */}
    <SelectLanguage />

    <div className="account">
      <img
        className="account__avatar"
        src={AVARTAR_DEFAULT}
        alt=""
      />
      <span className="account__name">{user.fullName}</span>
      <Dropdown
        menu={{
          selectedKeys: [location.pathname],
          items,
        }}
        placement="bottomRight"
        trigger="click"
      >
        <AntButton type={BUTTON.GHOST_WHITE} size="xsmall" icon={<ChevronDown />} />
      </Dropdown>
    </div>
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...auth.actions,
};
export default connect(mapStateToProps, mapDispatchToProps)(HeaderAction);
