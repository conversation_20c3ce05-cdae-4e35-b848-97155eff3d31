import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import clsx from "clsx";

import useWindowDimensions from "@common/windowDimensions";
import { toast } from "@component/ToastProvider";
import { useProject } from "@app/pages/Project";

import Loading from "@component/Loading";
import AntModal from "@component/AntModal";
import AntButton from "@component/AntButton";
import TemplatePage from "@app/pages/Template";
import TemplateList from "@component/TemplateList";
import Tool from "@component/Tool";
import ToolGrid from "@component/Tool/ToolGrid";

import { BUTTON, CONSTANT, PROJECT_TYPE } from "@constant";
import { getAllTemplate, getTemplateAvailable } from "@services/Template";
import { createContentFromTemplate } from "@services/Project";

import PAPER_PURPLE from "@src/asset/icon/paper/paper-purple.svg";
import HUGE_BLUE from "@src/asset/icon/huge/huge-blue.svg";
import ArrowUpRight from "@component/SvgIcons/ArrowUpRight";

import * as tool from "@src/ducks/tool.duck";

import "./TemplateAndTool.scss";

function TemplateAndTool({ mostUsedToolData, ...props }) {
  const { t } = useTranslation();
  const { projectId, projectData, addContentBlock, setToolDemoState } = useProject();
  const { projectContentData, stateMoreTool, setStateMoreTool, isPreviewProject } = useProject();
  const { isExam, isMark } = useProject();
  const { width } = useWindowDimensions();
  
  const { handleSaveContentData } = useProject();
  
  const [isLoading, setLoading] = useState(false);
  const [isShowMore, setShowMore] = useState(false);
  
  const [templateData, setTemplateData] = useState([]);
  
  const projectType = useMemo(() => projectData?.type || PROJECT_TYPE.NORMAL, [projectData?.type]);
  
  useEffect(() => {
    props.getMostUsedTool(projectType);
    
    return () => {
      props.clearMostUsedTool();
    };
  }, [projectType]);
  
  useEffect(() => {
    if (!isMark) getTemplate();
  }, [projectId, isMark]);
  
  async function getTemplate() {
    let apiResponse;
    if (isExam) {
      apiResponse = await getAllTemplate({ projectType: PROJECT_TYPE.EXAM_SCHOOL, sort: "-updatedAt" });
    } else {
      apiResponse = await getTemplateAvailable({ sort: "-updatedAt" });
    }
    
    if (Array.isArray(apiResponse)) {
      setTemplateData(apiResponse);
    }
  }
  
  
  function toggleShowMore() {
    setShowMore(prevState => !prevState);
  }
  
  function toggleShowMoreTool() {
    setStateMoreTool(prevState => ({ ...prevState, isShowModal: !prevState.isShowModal }));
  }
  
  async function onUseTemplate(template) {
    if (!template?._id) {
      //toast.error()
      return;
    }
    setLoading(true);
    const apiRequest = {
      _id: projectId,
      templateId: template._id,
    };
    const apiResponse = await createContentFromTemplate(apiRequest);
    if (apiResponse.content) {
      handleSaveContentData(apiResponse.content);
      toast.success("USE_LESSON_TEMPLATE_SUCCESS");
      setShowMore(false);
    }
    setLoading(false);
  }
  
  function handleShowPreview(isShowModal, linkYoutube = null) {
    if (isShowModal) {
      setToolDemoState({ open: true, link: linkYoutube });
    } else {
      setToolDemoState({ open: false, link: null });
    }
  }
  
  const templateSource = useMemo(() => {
    if (width >= 1920) {
      return templateData.slice(0, 5);
    } else if (width >= 1024) {
      return templateData.slice(0, 3);
    } else if (width >= 768) {
      return templateData.slice(0, 2);
    } else {
      return templateData.slice(0, 1);
    }
  }, [width, templateData]);
  
  const toolSource = useMemo(() => {
    if (!Array.isArray(mostUsedToolData)) return [];
    
    if (width >= 1920) {
      return mostUsedToolData.slice(0, 4);
    } else if (width >= 1536) {
      return mostUsedToolData.slice(0, 3);
    } else if (width >= 1024) {
      return mostUsedToolData.slice(0, 3);
    } else if (width >= 768) {
      return mostUsedToolData.slice(0, 2);
    } else {
      return mostUsedToolData.slice(0, 1);
    }
  }, [width, mostUsedToolData]);
  
  const templateTitle = useMemo(() => {
    switch (projectData.type) {
      case PROJECT_TYPE.NORMAL:
        return t("LIST_OF_LESSON_TEMPLATES");
      case PROJECT_TYPE.EXAM_SCHOOL:
        return t("LIST_OF_EXAM_TEMPLATES");
      case PROJECT_TYPE.EXAM_IELTS:
        return t("LIST_OF_IELTS_TEMPLATES");
      default:
        return "";
    }
  }, [t, projectData.type]);
  
  if (isPreviewProject || (isMark && projectContentData?.length)) return null;
  return <>
    <Loading
      active={isLoading}
      className={clsx("template-and-tool-container", { "template-and-tool-full": projectContentData?.length })}
    >
      <div className="template-and-tool">
        {(!projectContentData?.length && !isExam && !isMark) && <>
          <div className="template-and-tool__title template-and-tool__title-template">
            <div className="template-and-tool__title-img">
              <img src={PAPER_PURPLE} alt="" />
            </div>
            <div className="template-and-tool__title-text">
              {templateTitle}
            </div>
            <div className="template-and-tool__title-action">
              <AntButton
                size="large"
                type={BUTTON.GHOST_PURPLE}
                onClick={toggleShowMore}
                icon={<ArrowUpRight />}
                iconLocation={CONSTANT.RIGHT}
              >
                {t("SHOW_MORE")}
              </AntButton>
            </div>
          </div>
          
          <div className="template-and-tool__template-list">
            <TemplateList
              dataSource={templateSource}
              onUseTemplate={onUseTemplate}
            />
          </div>
        </>}
        
        <div className="template-and-tool__title template-and-tool__title-tool">
          <div className="template-and-tool__title-img">
            <img src={HUGE_BLUE} alt="" />
          </div>
          <div className="template-and-tool__title-text">
            {t("LIST_TOOL")}
          </div>
          <div className="template-and-tool__title-action">
            <AntButton
              size="large"
              type={BUTTON.GHOST_NAVY}
              onClick={toggleShowMoreTool}
              icon={<ArrowUpRight />}
              iconLocation={CONSTANT.RIGHT}
            >
              {t("SHOW_MORE")}
            </AntButton>
          </div>
        </div>
        
        
        <div className="template-and-tool__tool-list">
          {toolSource?.map((toolItem) => {
            return <ToolGrid
              key={toolItem._id}
              toolItemData={toolItem}
              //toolLoading={toolLoading}
              handleAccessTool={() => addContentBlock({ toolId: toolItem._id })}
              onShowPreview={() => handleShowPreview(true, toolItem.linkYoutube)}
            />;
          })}
        </div>
      </div>
    </Loading>
    
    <AntModal
      width={1656}
      title={t("LESSON_TEMPLATE_LIST")}
      open={isShowMore}
      onCancel={toggleShowMore}
      footerless
      className="template-and-tool__modal"
    >
      <TemplatePage
        dataSource={templateData}
        onUseTemplate={onUseTemplate}
      />
    </AntModal>
    
    <AntModal
      width={1656}
      title={t("TOOL_LIST")}
      open={stateMoreTool.isShowModal}
      onCancel={toggleShowMoreTool}
      footerless
      className="template-and-tool__modal"
      destroyOnClose
    >
      <Tool
        toolType={projectType}
        addContentBlock={(...args) => {
          addContentBlock(...args);
          toggleShowMoreTool();
        }}
      />
    </AntModal>
  </>;
}

function mapStateToProps(store) {
  const { mostUsedToolData } = store.tool;
  return { mostUsedToolData };
}

const mapDispatchToProps = {
  ...tool.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(TemplateAndTool);