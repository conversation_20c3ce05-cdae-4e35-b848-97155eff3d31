import React, {useState, useEffect, useCallback, useRef} from 'react';
import {useParams} from 'react-router-dom';
import {Button, Select, Checkbox} from 'antd';
import {useTranslation} from 'react-i18next';
import AudioPlayer from '@src/app/component/AudioPlayer/AudioPlayer';
import StudentBreadcrumb from '@app/layout/StudentLayout/StudentBreadcrumb';
import ResetIcon from '@src/assets/icons/reset.svg';
import ArrowLeftIcon from '@src/assets/icons/arrow-left-square.svg';
import ArrowRightIcon from '@src/assets/icons/arrow-right-square.svg';
import DangerIcon from '@src/assets/icons/danger.svg';
import HideIcon from '@src/assets/icons/hide.svg';
import ShowIcon from '@src/assets/icons/show.svg';
import CheckIcon from '@src/assets/icons/check.svg';
import TickSquareIcon from '@src/assets/icons/tick-square.svg';
import UnTickSquareIcon from '@src/assets/icons/untick-square.svg';
import {getExerciseDetails, checkDictationAnswer, resetAnswerDictation} from '@src/app/services/DictationShadowing';
import './DictationScreen.scss';
import Loading from '@src/app/component/Loading';
import TranscriptDisplay from './TranscriptDisplay';
const {Option} = Select;

export const DictationScreen = () => {
  const {t} = useTranslation();
  const {id: topicId} = useParams();
  const transcriptContainerRef = useRef(null);
  const activeItemRef = useRef(null);
  const inputRef = useRef(null);

  // Data states
  const [topicData, setTopicData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [progress, setProgress] = useState({current: 0, total: 0, correctCount: 0});
  const [settings, setSettings] = useState({
    showTranscript: true,
    showHints: true,
    autoPlay: true,
    playbackSpeed: 1,
  });

  // UI states
  const [loading, setLoading] = useState(true);
  const [dictationMode, setDictationMode] = useState('word');
  const [userInput, setUserInput] = useState('');
  const [isCorrect, setIsCorrect] = useState(null);
  const [showAnswer, setShowAnswer] = useState(false);
  const [activeTranscriptIndex, setActiveTranscriptIndex] = useState(null);
  const [checkingAnswer, setCheckingAnswer] = useState(false);

  // Get current question
  const currentQuestion = questions[currentQuestionIndex];

  // Function to format question data
  const formatQuestionData = exercise => ({
    id: exercise._id || exercise.segment._id,
    submissionId: exercise._id,
    type: exercise.mode,
    text: exercise.segment.text,
    answer: exercise.correctAnswer || (exercise.mode === 'word' ? exercise.segment.hiddenWord : exercise.segment.text),
    audioStart: exercise.segment.start,
    audioEnd: exercise.segment.end,
    transcript: exercise.segment.text,
    studentAnswer: exercise.studentAnswer || '',
    previousResult: exercise.result || null, // 'correct' or 'incorrect'
    errorLists: exercise.errorLists || [],
  });

  // Function to generate hidden answer
  const getHiddenAnswer = answer => {
    return '*'.repeat(answer.length);
  };

  const toggleShowAnswer = () => {
    setShowAnswer(!showAnswer);
  };

  const handlePlaybackSpeedChange = newSpeed => {
    setSettings(prev => ({
      ...prev,
      playbackSpeed: newSpeed,
    }));
  };

  const handleAudioProgress = useCallback(
    playedSeconds => {
      const activeIndex = questions.findIndex(
        question => playedSeconds >= question.audioStart && playedSeconds <= question.audioEnd,
      );
      setActiveTranscriptIndex(activeIndex);
    },
    [questions],
  );

  // Format exercise data from API response
  const formatExerciseData = exerciseData => {
    // Extract basic topic info
    const topicInfo = {
      title: exerciseData.name,
      level: exerciseData.difficulty,
      type: exerciseData.type,
      audio: {
        url: `/api/files/content/${exerciseData.audioId}`,
        audioId: exerciseData.audioId,
      },
      image: exerciseData.avatarId ? `/api/files/content/${exerciseData.avatarId}` : null,
    };

    // Format questions from exercises
    const formattedQuestions = exerciseData.exercises.map(exercise => formatQuestionData(exercise));

    return {
      topic: topicInfo,
      questions: formattedQuestions,
      progress: {
        current: 0,
        total: formattedQuestions.length,
        correctCount: 0,
      },
      settings: {
        showTranscript: true,
        showHints: true,
        autoPlay: true,
        playbackSpeed: 1,
      },
    };
  };

  // Update correctCount whenever questions change
  useEffect(() => {
    if (questions.length > 0) {
      const correctCount = questions.filter(q => q.previousResult === 'correct').length;
      setProgress(prev => ({
        ...prev,
        correctCount,
      }));
    }
  }, [questions]);

  useEffect(() => {
    const fetchExerciseData = async () => {
      try {
        setLoading(true);
        const exerciseData = await getExerciseDetails(topicId, dictationMode);

        if (exerciseData) {
          const formattedData = formatExerciseData(exerciseData);
          setTopicData(formattedData.topic);
          setQuestions(formattedData.questions);
          setProgress(formattedData.progress);
          setSettings(formattedData.settings);

          // Reset states
          setCurrentQuestionIndex(0);
          setUserInput('');
          setIsCorrect(null);
          setShowAnswer(false);
        }
      } catch (error) {
        console.error('Error loading exercise:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchExerciseData();
  }, [topicId, dictationMode, t]);

  // Focus on input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [inputRef.current]);

  // Focus on input when current question changes
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [currentQuestionIndex]);

  useEffect(() => {
    // Reset showAnswer when moving to next question
    setShowAnswer(false);

    // Set initial input to previous student answer if it exists
    if (currentQuestion?.studentAnswer) {
      setUserInput(currentQuestion.studentAnswer);
      setIsCorrect(currentQuestion.previousResult === 'correct');
    } else {
      setUserInput('');
      setIsCorrect(null);
    }
  }, [currentQuestionIndex, currentQuestion]);

  const handleInputChange = e => {
    setUserInput(e.target.value);
    setIsCorrect(null);
  };

  const resetQuestion = async () => {
    if (!currentQuestion) return;
    setUserInput('');
    setIsCorrect(null);
    setShowAnswer(false);

    // Focus on input after reset
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 0);

    if (currentQuestion.submissionId) {
      try {
        setCheckingAnswer(true);

        // Create the request body according to the required format
        const requestBody = {
          submissionId: currentQuestion.submissionId,
        };

        const response = await resetAnswerDictation(requestBody);
        if (response.error) {
          return;
        }

        // Update the current question with response data
        setQuestions(prevQuestions => {
          const newQuestions = [...prevQuestions];
          const formattedQuestion = {
            ...formatQuestionData(response),
          };
          newQuestions[currentQuestionIndex] = formattedQuestion;
          return newQuestions;
        });
      } catch (error) {
        console.error('Error checking answer:', error);
      } finally {
        setCheckingAnswer(false);
      }
    }
  };

  const handleCheckAnswer = async () => {
    if (!currentQuestion) return;

    try {
      setCheckingAnswer(true);

      // Create the request body according to the required format
      const requestBody = {
        segment: {
          start: currentQuestion.audioStart,
          end: currentQuestion.audioEnd,
          text: currentQuestion.text,
          hiddenWord: currentQuestion.answer,
          _id: currentQuestion.id,
        },
        mode: currentQuestion.type,
        studentAnswer: userInput?.trim(),
        exerciseId: topicId,
        submissionId: currentQuestion.submissionId,
      };

      const response = await checkDictationAnswer(requestBody);
      if (response.error) {
        return;
      }

      // Update the current question with response data
      setQuestions(prevQuestions => {
        const newQuestions = [...prevQuestions];
        // Use the formatQuestionData function and merge with existing data
        const updatedQuestion = {
          ...formatQuestionData(response),
        };

        newQuestions[currentQuestionIndex] = updatedQuestion;
        return newQuestions;
      });

      // Check if the answer is correct based on the API response
      const isAnswerCorrect = response?.result === 'correct';
      setIsCorrect(isAnswerCorrect);

      // Remove focus from the input field if answer is correct
      if (isAnswerCorrect) {
        document.activeElement.blur();
      }
    } catch (error) {
      console.error('Error checking answer:', error);
    } finally {
      setCheckingAnswer(false);
    }
  };

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      setUserInput('');
      setIsCorrect(null);
      setShowAnswer(false);
      setProgress(prev => ({
        ...prev,
        current: prev.current + 1,
      }));
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      setUserInput('');
      setIsCorrect(null);
      setShowAnswer(false);
      setProgress(prev => ({
        ...prev,
        current: prev.current - 1,
      }));
    }
  };

  const handleModeChange = value => {
    setDictationMode(value);
  };

  const handleTranscriptToggle = checked => {
    setSettings(prev => ({
      ...prev,
      showTranscript: checked,
    }));
  };

  useEffect(() => {
    const handleKeyPress = event => {
      if (event.key === 'Enter' && document.activeElement.tagName === 'INPUT') {
        userInput && userInput.trim() !== '' && !checkingAnswer && handleCheckAnswer();
      } else if (event.key === 'Enter' && document.activeElement.tagName !== 'INPUT') {
        !checkingAnswer && handleNext();
      }
    };

    window.addEventListener('keydown', handleKeyPress);

    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleCheckAnswer, handleNext]);

  // Add effect to scroll active transcript into view
  useEffect(() => {
    if (activeTranscriptIndex !== null && transcriptContainerRef.current && activeItemRef.current) {
      const container = transcriptContainerRef.current;
      const activeItem = activeItemRef.current;

      const containerRect = container.getBoundingClientRect();
      const activeItemRect = activeItem.getBoundingClientRect();

      // Check if the active item is outside the visible area
      if (activeItemRect.top < containerRect.top || activeItemRect.bottom > containerRect.bottom) {
        // Scroll the item into view with smooth behavior
        activeItem.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }
  }, [activeTranscriptIndex]);

  if (loading) {
    return (
      <div className="dictation-screen">
        <div className="dictation-screen__loading">
          <Loading active transparent />
        </div>
      </div>
    );
  }

  return (
    <div className="dictation-screen">
      <StudentBreadcrumb subTitle={topicData?.title} />
      <div className="dictation-screen__container">
        {topicData && (
          <div className="dictation-screen__topic-info">
            <h1>{topicData.title}</h1>
          </div>
        )}

        {currentQuestion && (
          <>
            <div className="dictation-screen__audio-player">
              <AudioPlayer
                audioId={topicData?.audio?.audioId}
                audioUrl={topicData?.audio?.url}
                startTime={currentQuestion.audioStart}
                endTime={currentQuestion.audioEnd}
                playbackSpeed={settings.playbackSpeed}
                autoPlay={settings.autoPlay}
                onPlaybackSpeedChange={handlePlaybackSpeedChange}
              />
            </div>
            <div className="dictation-screen__mode-select">
              <span>{t('DICTATION_MODE')}:</span>
              <Select className="dictation-screen__mode-select" value={dictationMode} onChange={handleModeChange}>
                <Option value="word">{t('WORD_MODE')}</Option>
                <Option value="sentence">{t('SENTENCE_MODE')}</Option>
                <Option value="mixed">{t('MIXED_MODE')}</Option>
              </Select>
            </div>
            <div className="dictation-screen__header">
              <Button className="reset-button" type="text" onClick={resetQuestion}>
                <img src={ResetIcon} alt="Reset exercise" />
                <span>{t('RESET')}</span>
              </Button>
              <div className="dictation-screen__progress">
                {progress.current + 1}/{progress.total}
              </div>
              <div className="navigation-group">
                <Button
                  className={`nav-button ${currentQuestionIndex === 0 ? 'disabled' : ''}`}
                  type="text"
                  disabled={currentQuestionIndex === 0 || checkingAnswer}
                  onClick={handlePrevious}
                >
                  <img src={ArrowLeftIcon} alt="Previous question" />
                </Button>
                <Button
                  className={`nav-button ${currentQuestionIndex >= questions.length - 1 ? 'disabled' : ''}`}
                  type="text"
                  disabled={currentQuestionIndex >= questions.length - 1 || checkingAnswer}
                  onClick={handleNext}
                >
                  <img src={ArrowRightIcon} alt="Next question" />
                </Button>
              </div>
            </div>
            <div className="dictation-screen__exercise">
              {currentQuestion?.type === 'word' && (
                <div className="exercise-text">
                  {currentQuestion.text.replace(
                    currentQuestion.answer,
                    '[' + '-'.repeat(currentQuestion.answer.length) + ']',
                  )}
                </div>
              )}
              <div className="exercise-input">
                <input
                  ref={inputRef}
                  type="text"
                  value={userInput}
                  onChange={handleInputChange}
                  className={`input-field ${isCorrect === true ? 'correct' : ''} ${
                    isCorrect === false ? 'incorrect' : ''
                  }`}
                  placeholder={t('TYPE_YOUR_ANSWER')}
                  maxLength={100}
                />
              </div>
            </div>

            <div className="dictation-screen__actions">
              <Button
                className="check-button"
                type="primary"
                onClick={handleCheckAnswer}
                disabled={!userInput || userInput.trim() === '' || checkingAnswer}
                loading={checkingAnswer}
              >
                {checkingAnswer ? t('CHECKING') : t('CHECK_ANSWER')}
              </Button>
              <Button
                className="next-button"
                onClick={handleNext}
                disabled={currentQuestionIndex >= questions.length - 1 || checkingAnswer}
              >
                {t('NEXT')}
              </Button>
            </div>

            <div className="dictation-screen__answer-container" style={{ display: questions.some(q => q.studentAnswer) ? 'block' : 'none' }}>
              {isCorrect !== null && (
                <div className="exercise-correct-answer">
                  <div className={`answer-label ${isCorrect ? 'correct' : 'incorrect'}`}>
                    <img src={isCorrect ? CheckIcon : DangerIcon} alt={isCorrect ? t('CORRECT') : t('INCORRECT')} />
                    <span>{isCorrect ? t('CORRECT') : t('INCORRECT')}</span>
                  </div>
                  {isCorrect !== true && (
                    <div className="answer-content">
                      <img
                        src={isCorrect ? ShowIcon : showAnswer ? ShowIcon : HideIcon}
                        alt={!isCorrect && showAnswer ? t('HIDE_ANSWER') : t('SHOW_ANSWER')}
                        onClick={!isCorrect ? toggleShowAnswer : undefined}
                        className={`toggle-answer-icon ${isCorrect ? '' : 'clickable'}`}
                      />
                      <span>
                        {isCorrect || showAnswer ? currentQuestion.answer : getHiddenAnswer(currentQuestion.answer)}
                      </span>
                    </div>
                  )}
                </div>
              )}

              <div className="dictation-screen__score-card">
                <div className="score-card__content">
                  <div className="score-card__count">
                    <span className="correct-count">{progress.correctCount}</span>
                    <span className="total-count">/{progress.total}</span>
                  </div>
                  <div className="score-card__title">{t('CORRECT_ANSWERS')}</div>
                </div>
              </div>

              {progress.current === questions.length - 1 && (
                <div className="dictation-screen__transcript">
                  <div className="transcript-toggle">
                    <div className="custom-checkbox" onClick={() => handleTranscriptToggle(!settings.showTranscript)}>
                      <img
                        src={settings.showTranscript ? TickSquareIcon : UnTickSquareIcon}
                        alt={settings.showTranscript ? t('HIDE_TRANSCRIPT') : t('SHOW_TRANSCRIPT')}
                      />
                      <span>{t('SHOW_TRANSCRIPT')}</span>
                    </div>
                  </div>
                  {settings.showTranscript && (
                    <div className={`transcript-content`}>
                      <div className="transcript-content__audio-player">
                        <AudioPlayer
                          audioUrl={topicData?.audio?.url}
                          startTime={0}
                          playbackSpeed={settings.playbackSpeed}
                          autoPlay={false}
                          onPlaybackSpeedChange={handlePlaybackSpeedChange}
                          onProgress={handleAudioProgress}
                        />
                      </div>
                      <TranscriptDisplay
                        questions={questions}
                        activeTranscriptIndex={activeTranscriptIndex}
                        transcriptContainerRef={transcriptContainerRef}
                        activeItemRef={activeItemRef}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};
