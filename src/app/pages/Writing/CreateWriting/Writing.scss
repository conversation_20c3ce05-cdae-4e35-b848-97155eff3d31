@import 'src/app/styles/scroll';

.writing-container-wrapper {
  display: flex;
  flex-direction: row;
  gap: 16px;
  position: relative;
  align-items: flex-start;
}

.writing-container {
  padding: 32px;
  border-radius: 24px;
  background: radial-gradient(18.71% 33.59% at 50% 8.03%, #f4f3ff 0.01%, #ffffff 100%);
  box-shadow: 0 4px 20px 0 #0000001a;
  display: flex;
  flex: 1;
  width: 100%;
  justify-content: center;

  .writing-inner {
    flex: 1;
    max-width: 832px;
    display: flex;
    align-items: center;
    flex-direction: column;

    gap: 16px;
    line-height: 1.5;

    .writing__header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .writing__header__title {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
      }

      .writing__header__my-essays {
        display: flex;
        gap: 4px;
        align-items: center;

        font-size: 12px;
        font-weight: 400;
        line-height: 16px;

        a {
          display: flex;
          align-items: center;
          color: var(--primary-colours-blue-navy);
          font-weight: 500;
          text-decoration-line: underline;
          text-decoration-style: solid;
          text-underline-position: from-font;

          img {
            width: 22px;
            height: 22px;
          }
        }
      }
    }

    .writing__content {
      display: flex;
      width: 100%;
      flex-direction: column;
      gap: 16px;

      .writing__select-tool {
        width: 50%;
        height: 48px;

        .ant-select-selector {
          padding: 12px 16px;
          border-radius: 16px;
          border: 1px solid #dbdbdb;

          .ant-select-selection-item {
            font-size: 16px;
            font-weight: 500;
            color: var(--primary-colours-blue-navy);
            line-height: 20px;
          }
        }

        .ant-select-arrow {
          .ant-select-suffix {
            svg {
              fill: var(--primary-colours-blue-navy);
            }
          }
        }
      }

      #form-essay {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .form-essay__item {
          margin-bottom: 0;

          .ant-form-item-row {
            padding: 16px;
            border-radius: 16px;

            border: 1px solid #dbdbdb;
            background: #ffffff;

            &:has(.ant-input-suffix) {
              padding-bottom: 50px;
            }

            .ant-form-item-label {
              padding-bottom: 8px;

              label {
                color: var(--primary-colours-blue-navy);
                font-weight: 500;
              }
            }

            .ant-input {
              border: none;
              outline: none;
              box-shadow: none;
              resize: none;
            }
          }

          .ant-form-item-margin-offset {
            display: none;
          }

          &:has(.ant-form-item-explain) {
            margin-bottom: 32px;

            &:has(.exceed-word-error) {
              margin-bottom: 0;
            }

            .ant-form-item-row {
              border-color: #ff7575;
            }

            .ant-form-item-control {
              > :last-child {
                height: 0;
              }
            }

            .ant-form-item-explain {
              position: relative;
              width: 100%;

              .ant-form-item-explain-error {
                position: absolute;
                top: 16px;
                display: flex;
                align-items: center;
                gap: 8px;
                width: 100%;
                background: none;
                padding: 0;
                margin: 8px 0 0 -16px;
                font-size: 14px;
                font-weight: 500;
                line-height: 18.2px;
                color: #ff0307;

                &:has(.exceed-word-error) {
                  display: none;
                }

                &::before {
                  content: url('@src/asset/icon/danger/danger-red.svg');
                  height: 24px;
                  width: 24px;
                }
              }
            }

            &:has(.ant-input-suffix) {
              .ant-form-item-explain {
                .ant-form-item-explain-error {
                  top: 50px;
                }
              }
            }
          }

          .essay__header {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 8px;

            label {
              color: var(--primary-colours-blue-navy);
              font-weight: 500;
              height: 20px;
            }

            .essay__header__left {
              display: flex;
              align-items: center;
              gap: 8px;

              button {
                padding: 4px 8px;
                gap: 4px;
                border-radius: 8px;
                width: fit-content;

                span {
                  font-size: 12px;
                  line-height: 16px;
                  font-weight: 500;
                }
              }

              .generate-idea-btn {
                background-color: #3A18CE !important;
                border: none !important;
                color: #FFFFFF !important;
              }

              .find-vocab-btn {
                background-color: #EF1FD3 !important;
                border: none !important;
                color: #FFFFFF !important;
              }
            }

            .essay__header__right {
              display: flex;
              gap: 4px;
              justify-content: end;
              flex-grow: 1;

              button {
                padding: 4px 8px;
                gap: 4px;
                border-radius: 8px;
                width: fit-content;

                span {
                  font-size: 12px;
                  line-height: 16px;
                  font-weight: 500;
                }
              }

              .btn-delete-essay {
                background-color: #ffdada !important;
                border: none !important;

                svg path {
                  stroke: #ff0307 !important;
                }
              }
            }
          }

          .essay__notication {
            color: var(--primary-colours-blue-navy);
            padding-bottom: 8px;
            font-size: 12px;
            line-height: 20px;
          }

          textarea {
            padding: 0;
            line-height: 20.8px;
            @extend .scrollbar;
            @extend .scrollbar-show;
          }

          .ant-input-affix-wrapper {
            border: none;
            box-shadow: none;
            position: relative;

            &::after {
              position: absolute;
              bottom: -8px;
              content: '';
              border-bottom: 1px solid #dbdbdb;
              height: 1px;
              width: 100%;
            }
          }

          .ant-input-data-count {
            left: 0;
            color: var(--primary-colours-blue-navy);
            font-weight: 500;
            line-height: 16px;
            font-size: 12px;
            height: 21px;
            bottom: -34px;

            .text-area__count-error {
              color: #ff5352;
            }
          }

          &--disabled {
            .ant-form-item-row {
              background-color: #ECF0F4;
            }

            .ant-input {
              background-color: #ECF0F4;
            }
          }

        }
      }

      .writing__mode-toggles {
        display: flex;
        width: 100%;
        justify-content: flex-start;
        gap: 32px;

        .mode-toggle {
          display: flex;
          align-items: center;
          gap: 8px;

          &__label {
            font-family: Inter, sans-serif;
            font-weight: 500;
            font-size: 16px;
            color: var(--primary-colours-blue-navy, #09196b);
          }

          .ant-switch {
            min-width: 30px;
            width: 30px;
            height: 16px;
            background-color: #c5cbd4;
            opacity: 0.6;
            border-radius: 16px;
            border: none;

            .ant-switch-handle {
              width: 12px;
              height: 12px;
              top: 2px;
              left: 2px;
              box-shadow: none;

              &::before {
                background-color: #7b889a;
                border-radius: 50%;
              }
            }

            &.ant-switch-checked {
              background-color: #26d06d;
              opacity: 1;

              .ant-switch-handle {
                left: calc(100% - 12px - 2px);

                &::before {
                  background-color: #ffffff;
                }
              }
            }
          }
        }
      }

      .writing__test-timer {
        display: flex;
        width: 100%;
        justify-content: center;

        .test-timer {
          display: flex;
          align-items: center;
          gap: 8px;
          border-radius: 8px;
          flex-direction: column;

          &__label {
            font-weight: 400;
            font-size: 12px;
            color: #B3B3B3;

            &--ended {
              color: #ff4d4f;
              font-weight: 600;
            }
          }

          &__time {
            font-weight: 600;
            font-size: 22px;
            color: #000;

            &--ended {
              color: #ff4d4f;
            }
          }
        }
      }
    }

    .writing__submit {
      display: flex;
      justify-content: center;
      flex-direction: column;
      gap: 8px;
      margin-top: 16px;

      .ant-btn {
        border-radius: 12px;
        opacity: 1;

        .ant-btn-icon svg,
        .ant-btn-icon img {
          width: 24px;
          height: 24px;
        }
      }

      .writing-submitting {
        display: flex;
        align-items: center;
        flex-direction: column;

        .writing-submitting__image {
          width: 80px;
          height: 80px;
          margin-bottom: 4px;
        }

        .writing-submitting__text {
          font-weight: 500;
          font-size: 12px;
          line-height: 16px;
        }
      }
    }
  }
}

.practice-writing-sections {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.help-write-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  color: #36a6ff;

  .same-color-as-text {
    width: 16px;
    height: 16px;
    filter: invert(56%) sepia(86%) saturate(3795%) hue-rotate(195deg) brightness(103%) contrast(102%);
  }

  span {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43em;
    color: #36a6ff;
  }

  &--disabled {
    opacity: 0.7;
    cursor: not-allowed;

    &:hover {
      opacity: 0.7;
    }
  }

  .loading-icon-small {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3E79F7;
    border-radius: 50%;
    animation: spin 2s linear infinite;
    display: inline-block;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

.writing__submit {
  margin-top: 16px;
  display: flex;
  justify-content: center;

  &__btn {
    background: #09196b;
    border-radius: 12px;
    padding: 8px 24px;
    display: flex;
    align-items: center;
    gap: 8px;

    span {
      font-family: 'Inter', sans-serif;
      font-weight: 600;
      font-size: 16px;
      line-height: 1.5em;
      color: #ffffff;
    }
  }
}

.floating-help-button-container {
  position: fixed;
  bottom: 82px;
  right: 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  gap: 16px;
  z-index: 1000;
}
