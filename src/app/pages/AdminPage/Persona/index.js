import React, { useEffect, useState } from "react";
import i18n from "i18next";
import { Card, Col, Form, Input, Row, Tooltip } from "antd";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { LINK } from "@link";

import AntButton from "@component/AntButton";
import { BUTTON, CONSTANT, PAGINATION_INIT } from "@constant";
import { EditOutlined, PlusOutlined, InfoCircleOutlined, SearchOutlined } from "@ant-design/icons";
import Loading from "@component/Loading";

import { AntForm } from "@component/AntForm";
import { handleReplaceUrlSearch, handleSearchParams, orderColumn, paginationConfig } from "@common/functionCommons";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";
import { deletePersona, getPaginationPersona } from "@services/Persona";
import { handlePagingData } from "@common/dataConverter";

import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";

import TableAdmin from "@src/app/component/TableAdmin";

import "./Persona.scss";

Persona.propTypes = {};

function Persona(props) {
  const { t } = useTranslation();
  const [formSearch] = Form.useForm();
  const [personaData, setPersonaData] = useState(PAGINATION_INIT);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const submitFormFilter = (values) => {
    handleReplaceUrlSearch(1, personaData.paging.pageSize, values);
  };
  const getTypeOfTools = (typeTools) => {
    switch (typeTools?.trim()?.toUpperCase()) {
      case CONSTANT.VIDEO:
        return CONSTANT.VIDEO;
      case CONSTANT.OFFLINE_VIDEO:
        return CONSTANT.VIDEO;
      case CONSTANT.TOPIC:
        return CONSTANT.DOCUMENT;
      case CONSTANT.TEXT:
        return CONSTANT.DOCUMENT;
      case CONSTANT.AUDIO:
        return CONSTANT.AUDIO;
      case CONSTANT.IMAGE:
        return CONSTANT.IMAGE;
      default:
        return typeTools;
    }
  };
  const groupToolByCategory = (tools) => {
    let groupedData = {};
    tools.forEach((item) => {
      if (!groupedData[getTypeOfTools(item?.inputType)]) {
        groupedData[getTypeOfTools(item?.inputType)] = [];
      }
      groupedData[getTypeOfTools(item?.inputType)].push(item);
    });
    return groupedData;
  };
  const pagination = paginationConfig(personaData.paging, personaData.query, i18n.language);

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formSearch.setFieldsValue(query);
    getPersonaData();
  }, [location.search]);
  const getPersonaData = async (paging = personaData.paging, query = personaData.query) => {
    setLoading(true);
    const dataResponse = await getPaginationPersona(paging, query);
    if (dataResponse) {
      setPersonaData(handlePagingData(dataResponse, query));
    }
    setLoading(false);
  };
  const handleDelete = async (id, personaCode) => {
    confirm.delete({
      title: t("DELETE_PERSONA"),
      content: t("DELETE_PERSONA_CONFIRM", { persona: t(personaCode) }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async (e) => {
        setLoading(true);
        const apiResponse = await deletePersona(id);
        if (apiResponse) {
          toast.success(t("DELETE_PERSONA_SUCCESSFULLY"));
          const newPersonalData = personaData?.rows?.filter((item) => item?._id !== apiResponse?._id);
          setPersonaData({ ...personaData, rows: newPersonalData });
        } else {
          toast.error(t("DELETE_PERSONA_ERROR"));
        }
        setLoading(false);
      },
    });
  };
  const column = [
    orderColumn(personaData.paging),
    {
      title: t("PERSONA"),
      dataIndex: "code",
      key: "code",
      render: (_, value) => <span className="persona-name">{t(value?.code)}</span>,
      width: 180,
    },

    {
      title: t("TEMPLATE"),
      dataIndex: "templateId",
      render: (_, value) => {
        return (
          <div className={"template-persona"}>
            {value?.templateId.map((res, index) => {
              return (
                <div key={index} className={"template-persona-item"}>
                  <span>{res?.name}</span>
                </div>
              );
            })}
          </div>
        );
      },
      key: "templateId",
      width: 250,
    },
    {
      title: t("TOOL"),
      dataIndex: "toolId",
      render: (_, value) => {
        const groupTool = groupToolByCategory(value?.toolId);
        return (
          <div className={"tool-persona-container"}>
            {Object.entries(groupTool).map(([key, value]) => {
              return (
                <div className={"tool-persona"} key={key}>
                  <div className={"persona-left-item"}>
                    <span className="tool-category">{t(key)}</span>
                  </div>
                  <div className={"persona-right-item"}>
                    {value.map((res, index) => {
                      return (
                        <div key={index} className={"tool-persona-item"}>
                          <span>{res?.name}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        );
      },
      key: "toolId",
      width: 400,
    },
    {
      title: t("ACTION"),
      key: "action",
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className="persona-actions">
          <Tooltip title={t("EDIT_PERSONA")}>
            <Link to={LINK.ADMIN_PERSONA_DETAILS.format(record._id)}>
              <AntButton type={BUTTON.GHOST_WHITE} size="small" className={"btn-edit-persona"} icon={<EditOutlined/>} />
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_PERSONA")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-persona"}
              icon={<DeleteIcon/>}
              onClick={() => handleDelete(record?._id, record?.code)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const onClearFilter = () => {
    formSearch.resetFields();
    submitFormFilter({});
  };

  return (
    <Loading active={loading} transparent>
      <div className={"persona-container"}>
        <Card className="persona-info-card">
          <div className="persona-info-header">
            <div>
              <h1 className="persona-title">{t("PERSONA_MANAGEMENT")}</h1>
              <p className="persona-description">{t("PERSONA_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN_CREATE_PERSONA}>
              <AntButton type={BUTTON.DEEP_NAVY} size="large" className="btn-create-persona" icon={<PlusOutlined/>}>
                {t("CREATE_PERSONA")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="persona-search-card">
          <AntForm form={formSearch} layout="horizontal" size={"large"} className="form-filter" onFinish={submitFormFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={12} lg={16}>
                <AntForm.Item name="code" className="search-form-item" style={{ marginBottom: 0 }}>
                  <Input
                    placeholder={t("SEARCH_PERSONA_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={12} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton size={"large"} type={BUTTON.GHOST_WHITE} onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton size={"large"} htmlType={"submit"} type={BUTTON.DEEP_NAVY}>
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="persona-table-card">
          <TableAdmin
            columns={column}
            dataSource={personaData.rows}
            pagination={pagination}
            className="persona-table"
            scroll={{ x: 1000 }}
            locale={{ emptyText: t("NO_PERSONA_FOUND") }}
            rowClassName={() => "persona-table-row"}
          />
        </Card>
      </div>
    </Loading>
  );
}

export default Persona;
