import { createBase, deleteBase, joinParams } from "@services/Base";
import { API } from "@api";
import axios from "axios";
import { cloneObj, genQueryParam, genSearchFieldParam } from "@src/common/functionCommons";

export const getWhitelistPagination = async (query = {}) => {
  const queryObj = cloneObj(query);
  queryObj.page ||= 1;
  queryObj.limit ||= 0;
  return axios
    .get(API.WHITELIST_MANAGER, { params: queryObj })
    .then((response) => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return null;
    });
}

export async function deleteWhitelist(id) {
  return deleteBase(API.WHITELIST_ID, id);
}

export async function addWhiteList(data) {
  return createBase(API.WHITELIST, data);
}