import React, { useContext, useEffect, useRef, useState } from "react";
import { Form, Image, Input, InputNumber } from "antd";
import { useTranslation } from "react-i18next";
import { FullscreenOutlined } from "@ant-design/icons";

import { useContentInput } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import TextFromResource from "./TextFromResource";

import { API } from "@api";
import RULE from "@rule";
import { BUTTON, CONSTANT } from "@constant";
import { stringSplit } from "@common/functionCommons";

import File from "@component/SvgIcons/File";
import PlayAround24 from "@component/SvgIcons/Play/PlayAround24";
import PauseCircle24 from "@component/SvgIcons/Pause/PauseCircle24";
import BOY from "@src/asset/voiceOptions/boy.svg";
import GIRL from "@src/asset/voiceOptions/girl.svg";

import ALLOY_AUDIO from "@src/asset/voiceAudio/alloy.mp3";
import NOVA_AUDIO from "@src/asset/voiceAudio/nova.mp3";
import FABLE_AUDIO from "@src/asset/voiceAudio/fable.mp3";
import SHIMMER_AUDIO from "@src/asset/voiceAudio/shimmer.mp3";
import ONYX_AUDIO from "@src/asset/voiceAudio/onyx.mp3";
import ECHO_AUDIO from "@src/asset/voiceAudio/echo.mp3";

import "./Text.scss";

export const TextContext = React.createContext();

const VOICE_OPTIONS = {
  ONYX: {
    title: "Onyx",
    value: "onyx",
    avatar: BOY,
    audio: ONYX_AUDIO,
    type: BUTTON.LIGHT_NAVY
  },
  ALLOY: {
    title: "Alloy",
    value: "alloy",
    avatar: GIRL,
    audio: ALLOY_AUDIO,
    type: BUTTON.LIGHT_PINK
  },
  ECHO: {
    title: "Echo",
    value: "echo",
    avatar: BOY,
    audio: ECHO_AUDIO,
    type: BUTTON.LIGHT_NAVY
  },
  NOVA: {
    title: "Nova",
    value: "nova",
    avatar: GIRL,
    audio: NOVA_AUDIO,
    type: BUTTON.LIGHT_PINK,
  },
  FABLE: {
    title: "Fable",
    value: "fable",
    avatar: BOY,
    audio: FABLE_AUDIO,
    type: BUTTON.LIGHT_NAVY,
  },
  SHIMMER: {
    title: "Shimmer",
    value: "shimmer",
    avatar: GIRL,
    audio: SHIMMER_AUDIO,
    type: BUTTON.LIGHT_PINK,
  },
};

const SPEED_OPTIONS = {
  SLOW: {
    lang: "SLOW",
    speedText: "0.5x",
    value: 0.5,
  },
  SLIGHTLY_SLOW: {
    lang: "SLIGHTLY_SLOW",
    speedText: "0.75x",
    value: 0.75,
  },
  NORMAL: {
    lang: "NORMAL",
    speedText: "1x",
    value: 1,
  },
  SLIGHTLY_FAST: {
    lang: "SLIGHTLY_FAST",
    speedText: "1.25x",
    value: 1.25,
  },
  FAST: {
    lang: "FAST",
    speedText: "1.5x",
    value: 1.5,
  },
};


function Text({ showVoiceOption = false, ...props }) {
  const { t } = useTranslation();
  const { formKey } = useContentInput();
  
  const audioRef = useRef(null);
  const { content, inputData, isDisabledContent, toolInfo } = props;
  
  const [textForm] = Form.useForm();
  
  const [isShowTextFromDocument, setShowTextFromDocument] = useState(false);
  const [documentPreviewData, setDocumentPreviewData] = useState({
    type: null,
    fileName: null,
    previewImgId: null,
  });
  
  const [voiceSelected, setVoiceSelected] = useState(null);
  const [voicePlaying, setVoicePlaying] = useState(null);
  
  const [speedSelected, setSpeedSelected] = useState(1);
  
  const maxWords = toolInfo?.maxInputLength || 1000;
  
  
  useEffect(() => {
    if (inputData) {
      setDataForm(inputData);
    } else {
      textForm.resetFields();
      textForm.setFieldsValue({ speed: 1 });
      
      setShowTextFromDocument(false);
      setDocumentPreviewData({ type: null, fileName: null, previewImgId: null });
      setVoiceSelected(null);
      setVoicePlaying(null);
      setSpeedSelected(1);
    }
  }, [inputData?.text,
    inputData?.speed,
    inputData?.voice,
    inputData?.imageFileId,
    inputData?.thumbnailFileId,
    inputData?.fileId,
    inputData?.fileName,
  ]);
  
  useEffect(() => {
    if (inputData) {
      const previewData = {};
      if (inputData.thumbnailFileId) {
        previewData.type = CONSTANT.IMAGE;
        previewData.previewImgId = inputData.imageFileId;
      } else if (inputData.fileName) {
        previewData.type = CONSTANT.DOCUMENT;
        previewData.fileName = inputData.fileName;
      } else {
        previewData.type = null;
        previewData.fileName = null;
        previewData.previewImgId = null;
      }
      setDocumentPreviewData(prevState => ({ ...prevState, ...previewData }));
    }
  }, [inputData?.imageFileId, inputData?.fileName]);
  
  useEffect(() => {
    audioRef.current?.pause();
    if (voicePlaying) {
      const audio = new Audio(VOICE_OPTIONS[voicePlaying.toUpperCase()].audio);
      audioRef.current = audio;
      audioRef.current.playbackRate = speedSelected;
      
      
      audio.addEventListener("ended", () => {
        setVoicePlaying();
      });
      
      audioRef.current.play();
    }
  }, [voicePlaying]);
  
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.playbackRate = speedSelected;
    }
  }, [speedSelected]);
  
  function setDataForm(values) {
    textForm.setFieldsValue(values);
    props.onChange(values);
    
    if (showVoiceOption) {
      if (values.hasOwnProperty("voice") && voiceSelected !== values.voice) {
        setVoiceSelected(values.voice);
      }
      if (values.hasOwnProperty("speed") && speedSelected !== values.speed) {
        setSpeedSelected(values.speed);
      }
    }
  }
  
  const toggleModalUploadImage = () => {
    setShowTextFromDocument((pre) => !pre);
  };
  
  function onExtractDocument(type, extractedText, extractedDocument) {
    setDocumentPreviewData({
      type,
      fileName: extractedDocument.fileName,
      previewImgId: extractedDocument.imageFileId,
    });
    
    textForm.resetFields(["imageFileId", "thumbnailFileId", "fileId", "fileName"]);
    setDataForm({ text: extractedText, ...extractedDocument });
  }
  
  
  function onBlur() {
    const { text } = textForm.getFieldsValue();
    setDataForm({ text: text?.trim() });
  }
  
  
  function handleSelectVoice(voice) {
    textForm.setFieldsValue({ voice });
    setVoiceSelected(voice);
    
    textForm.validateFields(["voiceOption"]);
  }
  
  function handleSelectSpeed(speed) {
    textForm.setFieldsValue({ speed });
    setSpeedSelected(speed);
  }
  
  return <TextContext.Provider value={{
    isShowTextFromDocument, setShowTextFromDocument,
    
    // function
    onExtractDocument,
  }}>
    {!isDisabledContent && <div className="upload-buttons">
      <AntButton
        size="small"
        type={BUTTON.LIGHT_PURPLE}
        icon={<File />}
        onClick={toggleModalUploadImage}
      >
        {t("DOCUMENT_TO_TEXT")}
      </AntButton>
    </div>}
    
    
    {(!!documentPreviewData.fileName || !!documentPreviewData.previewImgId) && <div className="document-preview">
      
      {documentPreviewData.type === CONSTANT.IMAGE && <>
        <div className="document-preview__label">{t("EXTRACT_TEXT_FROM_IMAGE")}</div>
        <div className="document-preview__image">
          <Image
            width={140}
            height={140}
            src={API.STREAM_ID.format(documentPreviewData.previewImgId)}
            preview={{
              mask: <FullscreenOutlined />,
            }}
          />
        </div>
      </>}
      
      {documentPreviewData.type === CONSTANT.DOCUMENT && <>
        <div className="document-preview__label">{t("EXTRACT_TEXT_FROM_DOCUMENT")}</div>
        <div className="document-preview__document-name">{documentPreviewData.fileName}</div>
      </>}
    </div>}
    
    <AntForm
      id={`${CONSTANT.TEXT}-${content._id}${formKey}`}
      name={`${CONSTANT.TEXT}-${content._id}${formKey}`}
      className="form-text-input"
      layout="vertical"
      form={textForm}
      disabled={isDisabledContent}
    >
      
      <AntForm.Item
        name="text"
        id={`text-${content._id}`}
        label={toolInfo?.inputLabel || t("TEXT")}
        rules={[
          RULE.REQUIRED,
          () => ({
            validator(_, value) {
              if (value && stringSplit(value).length > maxWords) {
                return Promise.reject(new Error(t("ONLY_ENTER_UP_TO_NUMBER_WORD").format(maxWords)));
              }
              return Promise.resolve();
            },
          }),
        ]}
      >
        <Input.TextArea
          size="large" placeholder={toolInfo?.inputPlaceholder || t("PASTE_YOUR_TOPIC_HERE")}
          count={{
            show: true, max: maxWords,
            strategy: (txt) => stringSplit(txt).length,
            // exceedFormatter: (txt, { max }) => stringSplit(txt).slice(0, max).join(" "),
          }}
          autoSize={{ minRows: 1, maxRows: 15 }}
          onBlur={onBlur}
        />
      </AntForm.Item>
      
      
      {showVoiceOption && <>
        <AntForm.Item
          label={t("SPEED")}
        >
          <div className="voice-speed-container">
            {Object.values(SPEED_OPTIONS).map(option => {
              return <AntButton
                key={option.value}
                size="large"
                type={BUTTON.LIGHT_NAVY}
                className="option-speed-btn"
                onClick={() => handleSelectSpeed(option.value)}
                active={speedSelected === option.value}
              >
                <span className="option-speed-text">{option.speedText}</span>
                <span>{t(option.lang)}</span>
              </AntButton>;
            })}
          </div>
        </AntForm.Item>
        <AntForm.Item
          label={t("VOICE_OPTION")}
          name="voiceOption"
          rules={[
            () => ({
              validator() {
                if (!voiceSelected) {
                  return Promise.reject(new Error(t("PLEASE_CHOOSE_VOICE")));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <div className="voice-option-container">
            {Object.values(VOICE_OPTIONS).map(option => {
              return <AntButton
                key={option.value}
                size="large"
                type={option.type}
                className="option-voice-btn"
                onClick={() => handleSelectVoice(option.value)}
                active={voiceSelected === option.value}
              >
                <div className="option-voice-avatar">
                  <img src={option.avatar} alt="" />
                </div>
                {option.title}
                <span
                  className="ant-btn-icon"
                  onClick={e => {
                    e.stopPropagation();
                    setVoicePlaying(prevState => prevState !== option.value ? option.value : null);
                  }}
                >
                  {voicePlaying === option.value ? <PauseCircle24 /> : <PlayAround24 />}
                </span>
              </AntButton>;
            })}
          </div>
        </AntForm.Item>
        
        
        <Form.Item hidden name="voice">
          <Input readOnly />
        </Form.Item>
        <Form.Item hidden name="speed">
          <InputNumber readOnly />
        </Form.Item>
      </>}
      
      <Form.Item hidden name="imageFileId">
        <Input readOnly />
      </Form.Item>
      
      <Form.Item hidden name="thumbnailFileId">
        <Input readOnly />
      </Form.Item>
      
      <Form.Item hidden name="fileId">
        <Input readOnly />
      </Form.Item>
      
      <Form.Item hidden name="fileName">
        <Input readOnly />
      </Form.Item>
    </AntForm>
    
    <TextFromResource />
  </TextContext.Provider>;
}

const useText = () => useContext(TextContext);


export { Text, useText };
