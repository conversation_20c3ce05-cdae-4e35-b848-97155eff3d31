.resource-container {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .resource-categories {
    display: flex;
    gap: 16px;
  }

  .resource-tabs {
    .ant-tabs-nav {
      margin: 0;

      .ant-tabs-nav-wrap .ant-tabs-nav-list {
        .ant-tabs-ink-bar {
          display: none;
        }

        .ant-tabs-tab {
          padding: 0;


          & + .ant-tabs-tab {
            margin: 0 0 0 40px;
          }

          .resource-tabs-tab {
            display: flex;
            flex-direction: row;
            gap: 8px;
            color: var(--typo-colours-support-blue-light);
            position: relative;
            padding-bottom: 5px;

            &.resource-tab__active {
              &.resource-tab__video {
                color: var(--support-colours-red);

                &:after {
                  background: var(--support-colours-red);
                }
              }

              &.resource-tab__audio {
                color: var(--support-colours-green-dark);

                &:after {
                  background: var(--support-colours-green-dark);
                }
              }

              &.resource-tab__image {
                color: var(--primary-colours-blue);

                &:after {
                  background: var(--primary-colours-blue);
                }
              }

              &.resource-tab__document {
                color: var(--typo-colours-support-yellow);

                &:after {
                  background: var(--typo-colours-support-yellow);
                }
              }
            }

            .resource-tabs-tab__icon {
              display: flex;
              align-items: center;
            }

            &:after {
              content: '';
              height: 1px;
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
            }
          }
        }
      }
    }
  }

  .resource__search {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 24px;

    .resource__add-new {
      grid-column: span 2/span 2;
      display: flex;
      justify-content: end;
    }
  }
}