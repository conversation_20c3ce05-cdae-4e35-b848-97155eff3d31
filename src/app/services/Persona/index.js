
import { createBase, deleteBase, getAllBase, getAllPaginationBase, getDetailBase, updateBase } from "@services/Base";
import { API } from "@api";

export const getPaginationPersona = (paging, query) => {
  return getAllPaginationBase(API.PERSONA, paging, query, ['code']);
}

export const getAllPersona = () => {
  return getAllBase(API.PERSONA);
}

export const deletePersona = (id) => {
  return deleteBase(API.PERSONA_ID, id);
}
export const createPersona = (data) => {
  return createBase(API.PERSONA, data);
}

export const getPersona = (id, populate = []) => {
  return getDetailBase(API.PERSONA_ID, id, populate);
}

export const updatePersona = (data) => {
  return updateBase(API.PERSONA_ID, data)
}