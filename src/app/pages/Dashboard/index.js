import React, { useEffect, useState, useMemo  } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { connect } from "react-redux";
import { LINK } from "@link";

import "./Dashboard.scss";

import { convertQueryToObject } from "@common/functionCommons";

import {
  dashboardGetProject,
  dashboardGetToolSubmitted,
  dashboardGetToolsUsed,
  dashboardGetToolUsed,
  orgDashboardGetToolsUsed,
  orgDashboardGetToolSubmitted,
  orgDashboardGetProject,
  orgDashboardGetToolUsed
} from "@services/Dashboard";
import ChartTotalSubmit from "@app/pages/Dashboard/ChartTotalSubmit";
import ChartProjectDashboard from "@app/pages/Dashboard/ChartProjectDashboard";
import ChartStaticUsage from "@app/pages/Dashboard/ChartStaticUsage";
import ChartTopToolUsage from "@app/pages/Dashboard/ChartTopToolUsage";
import Filter from "./Filter";

DashboardPage.propTypes = {};

function DashboardPage({ user }) {
  const { t } = useTranslation();
  const { pathname } = useLocation();

  const [toolUsed, setToolUsed] = useState([]);
  const [toolSubmitted, setToolSubmitted] = useState([]);
  const [project, setProject] = useState([]);
  const [topToolUsed, setTopToolUsed] = useState([]);

  const isOrgDashboard = useMemo(() => {
    return pathname === LINK.ORG_DASHBOARD;
  }, [pathname]);

  useEffect(() => {
    getAPIDashboard();
  }, [location.search]);

  const parseSearchParamsToQuery = (queryString) => {
    const queryObj = convertQueryToObject(queryString);
    const newQueryObj = {
      ...queryObj,
      ...queryObj?.time ? { time: queryObj?.time } : { time: 'month' },  //set default time search = month
    }
    return newQueryObj;
  }
  const getAPIDashboard = async () => {
    const query = parseSearchParamsToQuery(location.search);
    const allRequest = isOrgDashboard ? [
      orgDashboardGetToolsUsed(query),
      orgDashboardGetProject(query),
      orgDashboardGetToolSubmitted(query),
      orgDashboardGetToolUsed(query),
    ] : [
      dashboardGetToolsUsed(query),
      dashboardGetProject(query),
      dashboardGetToolSubmitted(query),
      dashboardGetToolUsed(query),
    ]

    const [apiToolResponse, apiProjectResponse, apiToolSubmittedResponse, apiTopToolUsedResponse] = await Promise.all(allRequest);
    if (apiToolResponse) setToolUsed(apiToolResponse);
    if (apiProjectResponse) setProject(apiProjectResponse);
    if (apiToolSubmittedResponse) setToolSubmitted(apiToolSubmittedResponse);
    if (apiTopToolUsedResponse) setTopToolUsed(apiTopToolUsedResponse);
  };

  return (
    <div className="dashboard-page-container">
      <div className="dashboard-page-filter-container">
        {/* <div className="dashboard-page-filter__tabs">
          {user?.role === CONSTANT.ADMIN && user?.organizationId && (
            <>
              <AntButton
                type={role === CONSTANT.PERSONAL ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
                size={"small"}
                onClick={() => onChangeTabs(CONSTANT.PERSONAL)}
              >
                {t("PERSONAL")}
              </AntButton>
              <AntButton
                type={role === CONSTANT.ORGANIZATION ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
                size={"small"}
                onClick={() => onChangeTabs(CONSTANT.ORGANIZATION)}
              >
                {t("ORGANIZATION")}
              </AntButton>
            </>
          )}
        </div> */}
        <Filter isOrgDashboard={isOrgDashboard} />
      </div>
      <div className="dashboard-page-chart-remaning-container">
        <div className="dashboard-page-chart-remaning-item-column">
          <div className="dashboard-page-chart-remaning-info__title">{t("TOTAL_NUMBER_OF_PROJECT")}</div>
          <ChartProjectDashboard data={project} />
        </div>
        <div className="dashboard-page-chart-remaning-item-column">
          <div className="dashboard-page-chart-remaning-info__title">{t("STATISTICS_OF_SUBMISSIONS_BY_TOOL")}</div>
          <ChartStaticUsage data={toolSubmitted} />
        </div>
      </div>
      <div className="dashboard-page-chart-top-tool-container">
        <div className="dashboard-page-chart-top-tool">
          <div className="dashboard-page-chart-top-tool__title">{t("STATISTICS_ON_TOOL_USAGE")}</div>
          <ChartTotalSubmit data={toolUsed} />
        </div>
        <div className="dashboard-page-chart-top-tool">
          <div className="dashboard-page-chart-top-tool__title">{t("TOP_10_MOST_USED_TOOLS")}</div>
          <ChartTopToolUsage data={topToolUsed} />
        </div>
      </div>
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(DashboardPage);
