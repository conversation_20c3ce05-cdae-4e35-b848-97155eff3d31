import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { connect } from "react-redux";
import { Card, Col, Form, Input, Row, Select, Tag, Tooltip, DatePicker } from "antd";
import { useTranslation } from "react-i18next";
import { EyeOutlined, SearchOutlined, CheckOutlined, DeleteOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

import Loading from "@src/app/component/Loading";
import TableAdmin from "@src/app/component/TableAdmin";
import AntButton from "@src/app/component/AntButton";

import { toast } from "@src/app/component/ToastProvider";
import { paginationConfig, handleSearchParams, formatDate, handleReplaceUrlSearch, cloneObj, convertObjectToQuery, getColumnSortOrder } from "@src/common/functionCommons";

import { BUTTON, PAGINATION_INIT } from "@constant";
import { handlePagingData } from "@src/common/dataConverter";

import { getAllErrorReports, updateErrorReport, deleteErrorReport } from "@services/ErrorReport";
import ErrorReportDetail from "./ErrorReportDetail";
import { AntForm } from "@src/app/component/AntForm";
import { confirm } from "@component/ConfirmProvider";

import "./ErrorReports.scss";

const { RangePicker } = DatePicker;

function ErrorReports() {
  const location = useLocation();
  const [form] = Form.useForm();
  const { t, i18n } = useTranslation();

  const [errorReportsData, setErrorReportsData] = useState(PAGINATION_INIT);
  const [detailModalState, setDetailModalState] = useState({
    open: false,
    errorReport: null,
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const { query, paging } = handleSearchParams(location.search);
    getErrorReportsData(query, paging);
    form.setFieldsValue(query);
  }, [location.search]);

  const getErrorReportsData = async (query, paging) => {
    setIsLoading(true);
    const searchFields = ["description", "errorUrl"];
    const apiResponse = await getAllErrorReports(paging, query, searchFields);
    if (apiResponse) {
      setErrorReportsData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  };

  const handleViewDetail = (errorReport) => {
    setDetailModalState({ open: true, errorReport });
  };

  const handleCloseDetail = () => {
    setDetailModalState({ open: false, errorReport: null });
  };

  const handleMarkAsNotified = async (record) => {
    setIsLoading(true);
    const updateResponse = await updateErrorReport({
      _id: record._id,
      notified: !record.notified
    });

    if (updateResponse) {
      const rows = errorReportsData.rows.map(item =>
        item._id === record._id ? { ...item, notified: !record.notified } : item
      );
      setErrorReportsData({ ...errorReportsData, rows });
      toast.success(t("ERROR_REPORT_UPDATED_SUCCESS"));
    }
    setIsLoading(false);
  };

  const handleDelete = async (id, description) => {
    confirm.delete({
      title: t("DELETE_ERROR_REPORT"),
      content: t("DELETE_ERROR_REPORT_CONFIRM", { description: description.substring(0, 50) + "..." }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setIsLoading(true);
        const apiResponse = await deleteErrorReport(id);
        if (apiResponse) {
          toast.success(t("DELETE_ERROR_REPORT_SUCCESS"));
          getErrorReportsData(errorReportsData.query, errorReportsData.paging);
        } else {
          toast.error(t("DELETE_ERROR_REPORT_ERROR"));
          setIsLoading(false);
        }
      },
    });
  };

  const getImpactLevelColor = (level) => {
    switch (level) {
      case "high": return "red";
      case "medium": return "orange";
      case "low": return "green";
      default: return "default";
    }
  };

  const columns = [
    {
      title: t("ERROR_ID"),
      dataIndex: "_id",
      width: 120,
      render: (text) => <span className="error-id">{text.slice(-8)}</span>,
    },
    {
      title: t("USER"),
      dataIndex: ["userId", "email"],
      width: 200,
      render: (email, record) => (
        <div>
          <div>{email}</div>
          <small className="text-muted">{record.userId?.fullName}</small>
        </div>
      ),
    },
    {
      title: t("DESCRIPTION"),
      dataIndex: "description",
      width: 300,
      render: (text) => (
        <div className="error-description">
          {text.length > 100 ? text.substring(0, 100) + "..." : text}
        </div>
      ),
    },
    {
      title: t("IMPACT_LEVEL"),
      dataIndex: "impactLevel",
      width: 120,
      align: "center",
      render: (level) => (
        <Tag color={getImpactLevelColor(level)}>
          {t(`IMPACT_${level?.toUpperCase()}`)}
        </Tag>
      ),
    },
    {
      title: t("ERROR_URL"),
      dataIndex: "errorUrl",
      width: 200,
      render: (url) => (
        <a href={url} target="_blank" rel="noopener noreferrer" className="error-url">
          {url.length > 30 ? url.substring(0, 30) + "..." : url}
        </a>
      ),
    },
    {
      title: t("NOTIFIED"),
      dataIndex: "notified",
      width: 100,
      align: "center",
      render: (value) => (value ? <CheckOutlined style={{ color: "green" }} /> : null),
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (value) => formatDate(value),
      sortOrder: getColumnSortOrder("createdAt", errorReportsData?.query),
      sorter: true,
    },
    {
      title: t("ACTIONS"),
      align: "center",
      width: 150,
      render: (_, record) => (
        <div className="error-actions">
          <Tooltip title={t("VIEW_DETAILS")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title={record.notified ? t("MARK_AS_NOT_NOTIFIED") : t("MARK_AS_NOTIFIED")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              icon={<CheckOutlined />}
              onClick={() => handleMarkAsNotified(record)}
              className={record.notified ? "notified" : "not-notified"}
            />
          </Tooltip>
          <Tooltip title={t("DELETE_ERROR_REPORT")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record._id, record.description)}
              className="btn-delete"
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(errorReportsData.paging, errorReportsData.query, i18n.language);

  const submitFormFilter = (values) => {
    const filterValues = { ...values };

    // Handle date range
    if (values.dateRange && values.dateRange.length === 2) {
      filterValues.createdAtFrom = values.dateRange[0].startOf('day').toISOString();
      filterValues.createdAtTo = values.dateRange[1].endOf('day').toISOString();
      delete filterValues.dateRange;
    }

    handleReplaceUrlSearch(1, errorReportsData.paging.pageSize, filterValues);
  };

  const onClearFilter = () => {
    form.resetFields();
    submitFormFilter({});
  };

  return (
    <Loading active={isLoading} transparent>
      <div className="error-reports-container">
        <Card className="error-reports-info-card">
          <div className="error-reports-info-header">
            <div>
              <h1 className="error-reports-title">{t("ERROR_REPORTS_MANAGEMENT")}</h1>
              <p className="error-reports-description">{t("ERROR_REPORTS_MANAGEMENT_DESCRIPTION")}</p>
            </div>
          </div>
        </Card>

        <Card className="error-reports-search-card">
          <AntForm form={form} layout="horizontal" size={"large"} className="form-filter" onFinish={submitFormFilter}>
            <Row gutter={24} align="middle">
              <Col xs={24} md={6} lg={6}>
                <AntForm.Item name="description" className="search-form-item">
                  <Input
                    placeholder={t("SEARCH_DESCRIPTION_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={6} lg={6}>
                <AntForm.Item name="impactLevel" className="search-form-item">
                  <Select placeholder={t("FILTER_BY_IMPACT_LEVEL")} allowClear>
                    <Select.Option value="low">{t("IMPACT_LOW")}</Select.Option>
                    <Select.Option value="medium">{t("IMPACT_MEDIUM")}</Select.Option>
                    <Select.Option value="high">{t("IMPACT_HIGH")}</Select.Option>
                  </Select>
                </AntForm.Item>
              </Col>
              <Col xs={24} md={6} lg={6}>
                <AntForm.Item name="notified" className="search-form-item">
                  <Select placeholder={t("FILTER_BY_NOTIFICATION_STATUS")} allowClear>
                    <Select.Option value="true">{t("NOTIFIED")}</Select.Option>
                    <Select.Option value="false">{t("NOT_NOTIFIED")}</Select.Option>
                  </Select>
                </AntForm.Item>
              </Col>
              <Col xs={24} md={6} lg={6}>
                <AntForm.Item name="dateRange" className="search-form-item">
                  <RangePicker
                    placeholder={[t("FROM_DATE"), t("TO_DATE")]}
                    format="DD/MM/YYYY"
                    style={{ width: "100%" }}
                  />
                </AntForm.Item>
              </Col>
            </Row>
            <Row>
              <Col span={24} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton size={"large"} type={BUTTON.GHOST_WHITE} onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton size={"large"} htmlType={"submit"} type={BUTTON.DEEP_NAVY}>
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="error-reports-table-card">
          <TableAdmin
            columns={columns}
            dataSource={errorReportsData.rows}
            scroll={{ x: 1200 }}
            pagination={pagination}
            rowKey="_id"
            className="error-reports-table"
            locale={{ emptyText: t("NO_ERROR_REPORTS_FOUND") }}
          />
        </Card>

        <ErrorReportDetail
          open={detailModalState.open}
          errorReport={detailModalState.errorReport}
          onClose={handleCloseDetail}
          onUpdate={(updatedReport) => {
            const rows = errorReportsData.rows.map(item =>
              item._id === updatedReport._id ? updatedReport : item
            );
            setErrorReportsData({ ...errorReportsData, rows });
          }}
        />
      </div>
    </Loading>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(ErrorReports);
