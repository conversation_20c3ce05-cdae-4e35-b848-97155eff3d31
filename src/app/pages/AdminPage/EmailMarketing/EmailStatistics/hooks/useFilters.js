import {useState, useEffect, useCallback} from "react";
import {useLocation, useNavigate} from "react-router-dom";
import {Form} from "antd";
import dayjs from "dayjs";
import {convertQueryToObject} from "@common/functionCommons";
import {getPaginationCampaigns} from "@services/EmailMarketing";

// Time range options
export const TIME_RANGES = [
  {value: "week", label: "THIS_WEEK"},
  {value: "month", label: "THIS_MONTH"},
  {value: "custom", label: "CUSTOM"}
];

export const useFilters = (t) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [formFilter] = Form.useForm();

  const [campaigns, setCampaigns] = useState([]);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);


  // Load campaigns from API
  const loadCampaigns = useCallback(async () => {
    try {
      const response = await getPaginationCampaigns({page: 1, pageSize: 100}, {});
      if (response && response.rows) {
        const campaignOptions = [
          {value: "all", label: t("ALL_CAMPAIGNS")},
          ...response.rows.map(campaign => ({
            value: campaign._id,
            label: campaign.name
          }))
        ];
        setCampaigns(campaignOptions);
      }
    } catch (error) {
      console.error("Error loading campaigns:", error);
      // Fallback to default options
      setCampaigns([{value: "all", label: t("ALL_CAMPAIGNS")}]);
    }
  }, [t]);


  // Update URL with query parameters
  const updateUrlQuery = useCallback((dataSearch = {}) => {
    let searchParams = new URLSearchParams();
    Object.entries(dataSearch).forEach(([key, value]) => {
      if (value) searchParams.append(key, value);
    });
    navigate(`?${searchParams.toString()}`, {replace: true});
  }, [navigate]);

  // Submit filter form
  const onSubmitFilter = useCallback((values) => {
    try {
      const {fromDate, toDate} = values;
      const preparedValues = {
        ...values,
        ...fromDate ? {fromDate: dayjs(fromDate).startOf("day").unix()} : {},
        ...toDate ? {toDate: dayjs(toDate).endOf("day").unix()} : {},
      };
      updateUrlQuery(preparedValues);
    } catch (error) {
      console.error("Error submitting filter:", error);
    }
  }, [updateUrlQuery]);

  // Clear filter form
  const onClearFilter = useCallback(() => {
    setShowDatePicker(false);
    formFilter.resetFields();
    formFilter.setFieldsValue({
      campaign: "all",
      time: null
    });
    navigate(location.pathname, {replace: true});
  }, [formFilter, navigate, location.pathname]);

  // Handle time range change
  const handleTimeRangeChange = useCallback((value) => {
    if (value === "custom") {
      setShowDatePicker(true);
    } else {
      setShowDatePicker(false);
      formFilter.setFieldsValue({fromDate: null, toDate: null});
    }
  }, [formFilter]);

  // Get current filter values
  const getCurrentFilters = useCallback(() => {
    try {
      const values = formFilter.getFieldsValue(true);
      // Convert dates back to unix timestamps for API
      const {fromDate, toDate, ...otherValues} = values;
      return {
        ...otherValues,
        ...fromDate ? {fromDate: dayjs(fromDate).startOf("day").unix()} : {},
        ...toDate ? {toDate: dayjs(toDate).endOf("day").unix()} : {},
      };
    } catch (error) {
      console.error("Error getting current filters:", error);
      return {};
    }
  }, [formFilter]);

  // Initialize filters on mount
  useEffect(() => {
    loadCampaigns();
  }, [loadCampaigns]);

  // Handle query params on mount and when search changes
  useEffect(() => {
    const queryParams = convertQueryToObject(location.search);
    const {time, fromDate, toDate} = queryParams;

    // Safely handle date conversion
    let parsedFromDate = null;
    let parsedToDate = null;

    try {
      if (fromDate && !isNaN(fromDate)) {
        parsedFromDate = dayjs(fromDate * 1000);
      }
      if (toDate && !isNaN(toDate)) {
        parsedToDate = dayjs(toDate * 1000);
      }
    } catch (error) {
      console.warn("Error parsing dates from URL:", error);
    }

    const newQuery = {
      ...queryParams,
      ...parsedFromDate ? {fromDate: parsedFromDate} : {},
      ...parsedToDate ? {toDate: parsedToDate} : {},
    };

    // Set date picker visibility
    setShowDatePicker(time === "custom");

    // Set min/max dates for validation
    setMinDate(parsedFromDate);
    setMaxDate(parsedToDate);
    // Update form values
    formFilter.setFieldsValue(newQuery);
  }, [location.search, formFilter]);

  return {
    // Form
    formFilter,

    // Data
    campaigns,
    showDatePicker,
    minDate,
    maxDate,

    // Actions
    onSubmitFilter,
    onClearFilter,
    handleTimeRangeChange,
    setMinDate,
    setMaxDate,
    getCurrentFilters,

    // Utils
    TIME_RANGES
  };
};
