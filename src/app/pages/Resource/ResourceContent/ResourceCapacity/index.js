import React, { useMemo } from "react";
import { Bullet } from "@ant-design/plots";
import { useTranslation } from "react-i18next";

import { RESOURCE_LEGEND } from "@app/pages/Resource/ResourceContent/ResoureceConstant";

import "./ResourceCapacity.scss";


function ResourceCapacity({ video, audio, image, document, quota, ...props }) {
  const { t } = useTranslation();
  
  const chartData = useMemo(() => {
    function calPercent(value) {
      if (!quota) return 0;
      return Math.round(value / quota * 100);
    }
    
    const videoPercent = calPercent(video);
    const audioPercent = calPercent(audio);
    const imagePercent = calPercent(image);
    const documentPercent = calPercent(document);
    const emptyPercent = 100 - videoPercent - audioPercent - imagePercent - documentPercent;
    
    
    const ranges = [
      videoPercent,
      videoPercent + audioPercent,
      videoPercent + audioPercent + imagePercent,
      videoPercent + audioPercent + imagePercent + documentPercent,
      videoPercent + audioPercent + imagePercent + documentPercent + emptyPercent,
    ];
    
    return [
      {
        ranges,
        measures: [videoPercent, audioPercent, imagePercent, documentPercent, emptyPercent],
        target: null,
      },
    ];
  }, [video, audio, image, document, quota]);
  
  const chartColor = useMemo(() => {
    return RESOURCE_LEGEND.map((item) => item.color);
  }, []);
  
  const config = {
    data: chartData,
    measureField: "measures",
    rangeField: "ranges",
    targetField: "target",
    color: {
      range: chartColor,
      measure: chartColor,
    },
    label: false,
    xAxis: false,
    yAxis: false,
    tooltip: false,
    legend: false,
    target: false,
  };
  
  return <div className="resource-capacity">
    <div className="resource-capacity__chart">
      <Bullet {...config} />
    </div>
    <div className="resource-capacity__legend">
      {RESOURCE_LEGEND.map((item) => {
        const legendClassName = `resource-capacity__legend-item resource-capacity__legend-${item.key.toLowerCase()}`;
        return <div className={legendClassName} key={item.key}>
          <div className="resource-capacity__legend-item__dot" />
          <div className="resource-capacity__legend-item__text">{t(item.lang)}</div>
        </div>;
      })}
    </div>
  </div>;
}

export default ResourceCapacity;