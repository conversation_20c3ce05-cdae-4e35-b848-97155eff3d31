import React, { useEffect, useState } from "react";
import { Dropdown, Table } from "antd";
import { useTranslation } from "react-i18next";

import { useResource } from "@app/pages/Resource";

import NoData from "@component/NoData";
import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";

import { formatTimeDate, getFileExtension } from "@common/functionCommons";

import Trash from "@component/SvgIcons/Trash";
import MoreVertical from "@component/SvgIcons/MoreVertical";

import "./ResourceDocument.scss";

function ResourceDocument() {
  const { t } = useTranslation();
  
  const { resourceData, resourceTypeActive, resourceSearchValue, handleDeleteResource } = useResource();
  const [documentList, setDocumentList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10; // Number of items per page
  
  useEffect(() => {
    if (resourceTypeActive === CONSTANT.DOCUMENT && currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [resourceSearchValue]);
  
  useEffect(() => {
    setDocumentList(() => {
      if (!resourceData?.[CONSTANT.DOCUMENT]) return [];
      if (!resourceSearchValue) return resourceData[CONSTANT.DOCUMENT];
      return resourceData[CONSTANT.DOCUMENT]?.filter(x => x?.name?.toLowerCase()?.includes(resourceSearchValue?.toLowerCase()));
    });
  }, [resourceData, resourceSearchValue]);

  useEffect(() => {
    if (documentList.length && (documentList.length / pageSize) === (currentPage - 1)) {
      setCurrentPage(currentPage - 1);
    }
  }, [documentList]);

  const columns = [
    {
      title: t("ORDER"),
      width: 90,
      render: (value, row, index) => (currentPage - 1) * pageSize + index + 1,
    },
    {
      title: t("FILE_NAME"),
      dataIndex: "name",
      render: value => <div style={{ lineBreak: "anywhere", maxWidth: "400px" }}>{value}</div>,
    },
    {
      title: t("TYPE"),
      render: value => getFileExtension(value.fileId?.displayName || value.imageId?.name),
      width: 100,
    },
    {
      title: t("DATE_MODIFIED"),
      dataIndex: ["updatedAt"],
      render: formatTimeDate,
      width: 180,
    },
    
    {
      title: t("ACTION"),
      width: 130,
      render: (value) => {
        return <div className="resource-document-action">
          <Dropdown
            menu={{
              items: [{
                key: "DELETE",
                label: t("DELETE"),
                icon: <Trash />,
                onClick: () => handleDeleteResource(value),
              }],
              className: "action-dropdown-menu",
            }}
            trigger={["click"]}
            placement="bottomRight"
          >
            <AntButton
              size="small"
              type={BUTTON.GHOST_WHITE}
              icon={<MoreVertical />}
              onClick={(e) => e.stopPropagation()}
            />
          </Dropdown>
        </div>;
      },
    },
  ];
  
  
  if (resourceTypeActive !== CONSTANT.DOCUMENT) return null;
  if (!documentList?.length) return <NoData />;
  return <>
    <div className="resource-document-container">
      <Table
        scroll={{ x: "max-content" }}
        dataSource={documentList.slice((currentPage - 1) * pageSize, currentPage * pageSize)}
        columns={columns}
        pagination={{
          current: currentPage,
          total: documentList.length,
          pageSize: pageSize,
          onChange: setCurrentPage,
        }}
      />
    </div>
  
  
  </>;
}

export default ResourceDocument;