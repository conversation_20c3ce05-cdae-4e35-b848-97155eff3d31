import { useState, useEffect, useMemo } from 'react';
import { useTranslation } from "react-i18next";
import { Form, Input } from 'antd';
import jsPDF from 'jspdf';

import AntModal from '@src/app/component/AntModal';
import Loading from '@src/app/component/Loading';
import { AntForm } from '@src/app/component/AntForm';
import AntButton from "@component/AntButton";

import Text from "@component/SvgIcons/Text";
import Camera from "@component/SvgIcons/Camera";
import File from "@component/SvgIcons/File";

import { BUTTON, CONSTANT, MAX_PAGE_RANGER, UPLOAD_STATUS } from '@constant';

import { getImageDetail } from "@services/Image";
import { cloneObj, stringSplit } from "@common/functionCommons";
import { getFileById, uploadFile } from "@services/File";
import { createInput, updateInputMarkTest } from "@services/Input";
import { toast } from "@component/ToastProvider";

import { useMarkExam } from '../..';
import { useProject } from '@src/app/pages/Project';

import ImagesContent from './ImagesContent';
import PdfContent from './PdfContent';

import './ModalSubmission.scss';

const ModalSubmission = (props) => {
  const { t } = useTranslation();
  const { stateSubmission, handleShowSubmission, toolInfo, instructionId } = props;
  const { setFileUploadStatus, setFilesUpload } = props

  const [formAddSubmission] = Form.useForm();

  const { contentData, examSubData } = useMarkExam();
  const { projectData, setProjectContentData, setSubSelectedIndex } = useProject();

  const [inputType, setInputType] = useState(CONSTANT.TEXT);
  const [isLoadingModal, setLoadingModal] = useState(false);

  const [imageList, setImageList] = useState([]);
  const [isProcessing, setProcessing] = useState(false);

  const [pdfFile, setPdfFile] = useState(null);
  const [pageRange, setPageRange] = useState({ startPage: 0, endPage: 0 });
  const [totalPages, setTotalPages] = useState(0);

  useEffect(() => {
    if (stateSubmission.isShowModal) {
      formAddSubmission.resetFields();
      setImageList([]);
      setPdfFile(null);
      setPageRange({ startPage: 0, endPage: 0 });
      setTotalPages(0);

      if (stateSubmission.submissionSelected) {
        formAddSubmission.setFieldsValue(stateSubmission.submissionSelected.inputData);
        (async () => {
          const { text, fileId, imageId, startPage, endPage } = stateSubmission.submissionSelected.inputData;
          if (text) {
            setInputType(CONSTANT.TEXT);
          }
          else if (imageId) {
            setInputType(CONSTANT.IMAGE);
            setLoadingModal(true);
            const imageData = await getImageDetail(imageId);
            if (imageData) {
              setImageList([imageData]);
            }
            setLoadingModal(false);
          }
          else if (fileId) {
            setInputType(CONSTANT.FILE);
            setLoadingModal(true);
            setPageRange({ startPage, endPage });
            const apiResponse = await getFileById(fileId);
            if (apiResponse) {
              setPdfFile(apiResponse);
            }
            setLoadingModal(false);
          }
        })();
      } else {
        setInputType(CONSTANT.TEXT);
      }
    }
  }, [stateSubmission.isShowModal]);

  const validatePageRange = useMemo(() => {
    const { startPage, endPage } = pageRange;
    if (!startPage || !endPage || startPage < 1 || endPage > totalPages || startPage > endPage) {
      return { isError: true, errorMsg: t("PAGE_NUMBER_INVALID") };
    } else if (endPage - startPage >= MAX_PAGE_RANGER) {
      return { isError: true, errorMsg: t("PAGE_NUMBER_LIMIT") };
    }
    return { isError: false, errorMsg: "" };
  }, [pageRange, totalPages]);

  function onSaveSubmission(values) {
    const isValidateError = validateInputType();
    if (isValidateError) return;

    const isCreate = !stateSubmission.submissionSelected;
    if (isCreate) {
      handleAddSubmission(values);
    } else {
      handleUpdateSubmission(values);
    }
  }

  async function handleUpdateAndMark() {
    const { errorFields } = await formAddSubmission.validateFields().catch((err) => err);
    if (!!errorFields?.length) return;

    const values = formAddSubmission.getFieldsValue();
    handleUpdateSubmission(values, true);
  }

  const calculateImageDimensions = (imgWidth, imgHeight) => {
    const aspectRatio = imgWidth / imgHeight;
    //set pdf width, height after padding 10px.
    const pageWidth = 190;
    const pageHeight = 277;

    let newWidth, newHeight;

    // So sánh tỷ lệ giữa chiều rộng và chiều cao của trang để giữ đúng tỷ lệ
    if (imgWidth > imgHeight) {
      newWidth = Math.min(pageWidth, imgWidth);
      newHeight = newWidth / aspectRatio;
      if (newHeight > pageHeight) {
        newHeight = pageHeight;
        newWidth = newHeight * aspectRatio;
      }
    } else {
      newHeight = Math.min(pageHeight, imgHeight);
      newWidth = newHeight * aspectRatio;
      if (newWidth > pageWidth) {
        newWidth = pageWidth;
        newHeight = newWidth / aspectRatio;
      }
    }

    // Căn giữa hình ảnh
    const xPos = (pageWidth + 20 - newWidth) / 2;
    const yPos = (pageHeight + 20 - newHeight) / 2;

    return { xPos, yPos, newWidth, newHeight };
  };

  const loadImage = async (src) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = src;

      img.onload = () => resolve(img);
      img.onerror = reject;
    });
  };

  const convertImagesToPdf = async (images) => {
    const pdf = new jsPDF();
    const imagesCount = images.length;
    let fileReturn, totalPages;

    for (let i = 0; i < imagesCount; i++) {
      try {
        const imageUrl = URL.createObjectURL(images[i]);
        const imageExtension = images[i].name.split('.').pop();
        const addFormat = imageExtension.toUpperCase() === 'PNG' ? 'PNG' : 'JPEG';

        const img = await loadImage(imageUrl);

        const { xPos, yPos, newWidth, newHeight } = calculateImageDimensions(img.width, img.height);

        pdf.addImage(img, addFormat, xPos, yPos, newWidth, newHeight);

        if (i < imagesCount - 1) {
          pdf.addPage();
        } else {
          const fileBlob = pdf.output('blob');
          setProcessing(false);
          totalPages = pdf.getNumberOfPages();
          fileReturn = fileBlob;
        }
      } catch (error) {
        toast.success(t("CONVERT_IMAGE_ERROR").format(i + 1));
        fileReturn = null;
        break;
      }
    };

    return { fileReturn, totalPages };
  }

  const validateInputType = () => {
    let isError = false;
    if (inputType === CONSTANT.IMAGE) {
      if (!imageList.length) {
        toast.warning({ description: t("UPLOAD_IMAGES_WARNING") });
        isError = true;
      } else if (imageList.length > MAX_PAGE_RANGER) {
        isError = true;
      }

    } else if (inputType === CONSTANT.FILE) {
      if (!pdfFile) {
        toast.warning({ description: t("UPLOAD_PDF_WARNING") });
        isError = true;
      } else return validatePageRange.isError;
    }
    return isError;
  }

  async function handleAddSubmission(values) {

    setLoadingModal(true);
    let fileUpload = pdfFile;
    let { startPage, endPage } = { ...pageRange };
    if (inputType === CONSTANT.IMAGE) {
      const dataConvert = await convertImagesToPdf(imageList);

      if (!dataConvert?.fileReturn) return;
      fileUpload = dataConvert.fileReturn;
      startPage = 1;
      endPage = dataConvert.totalPages;
    }

    const isText = inputType === CONSTANT.TEXT;
    const apiRequest = {
      contentId: contentData._id,
      toolId: toolInfo._id,
      inputData: {
        instructionId: instructionId._id || instructionId,
        studentName: values.studentName,
        ...isText ? { text: values.text, markTestType: 'text' } : {}
      },
      inputType: toolInfo.inputType,
    };

    const apiResponse = await createInput(apiRequest);

    if (apiResponse) {
      setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        if (newState?.[0]) {
          newState[0] ||= {};
          newState[0].inputs ||= [];
          newState[0].inputs.push(apiResponse);
        }
        return newState;
      });
      setSubSelectedIndex(examSubData.length);

      handleShowSubmission(false);
      toast.success("ADD_SUBMISSION_SUCCESS");
      if (!isText) handleUploadFile(apiResponse, fileUpload, startPage, endPage);
    }
    setLoadingModal(false);
  }

  async function handleUpdateSubmission(values, regen = false) {

    setLoadingModal(true);
    let fileUpload = pdfFile;
    let { startPage, endPage } = { ...pageRange };
    if (inputType === CONSTANT.IMAGE && imageList.length && !stateSubmission.submissionSelected?.inputData?.imageId) {
      const dataConvert = await convertImagesToPdf(imageList);

      if (!dataConvert?.fileReturn) return;
      fileUpload = dataConvert.fileReturn;
      startPage = 1;
      endPage = dataConvert.totalPages;
    } else if (inputType === CONSTANT.FILE && !pdfFile.name) {
      fileUpload = null;
    }

    const apiRequest = {
      _id: stateSubmission.submissionSelected._id,
      inputData: {
        instructionId: stateSubmission.submissionSelected.inputData.instructionId,
        studentName: values.studentName,
      }
    };
    if (inputType === CONSTANT.TEXT) {
      apiRequest.inputData.text = values.text;
      apiRequest.inputData.markTestType = 'text';
    } else if (inputType === CONSTANT.FILE && !fileUpload) {
      apiRequest.inputData.startPage = startPage;
      apiRequest.inputData.endPage = endPage;
      apiRequest.inputData.markTestType = 'file';
      apiRequest.inputData.fileId = stateSubmission.submissionSelected.inputData.fileId;
      apiRequest.inputData.totalPages = stateSubmission.submissionSelected.inputData.totalPages;
    } else if (inputType === CONSTANT.IMAGE && stateSubmission.submissionSelected?.inputData?.imageId) {
      apiRequest.inputData.imageId = stateSubmission.submissionSelected.inputData.imageId;
      apiRequest.inputData.markTestType = stateSubmission.submissionSelected.inputData?.markTestType;
    }

    const apiResponse = await updateInputMarkTest(apiRequest);
    if (apiResponse) {
      setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState[0].inputs ||= [];
        newState[0].inputs = newState[0].inputs.map(input => {
          return input._id === apiResponse._id ? apiResponse : input;
        });
        return newState;
      });

      if (fileUpload) await handleUploadFile(apiResponse, fileUpload, startPage, endPage);

      if (regen) {
        document.getElementById("js-on-mark-one")?.click();
      }

      handleShowSubmission(false);
      if (!regen) {
        // khong show toast khi luu va cham - Quyet
        toast.success("UPDATE_SUBMISSION_SUCCESS");
      }
    }
    setLoadingModal(false);
  }

  async function handleUploadFile(subData, file, startPage, endPage) {

    const onUploadProgress = (progressEvent, index) => {
      const percent = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
      setUploadStatus(subData._id, UPLOAD_STATUS.UPLOADING, Math.min(percent, 99));
    };

    updateFilesUpload(subData._id, file, startPage, endPage);
    setUploadStatus(subData._id, UPLOAD_STATUS.UPLOADING, 0);
    const { inputData } = subData;
    inputData.markTestType = "file";

    const formData = { workspaceId: projectData.workspaceId, folder: "office" };
    const axiosConfig = { onUploadProgress, hideNoti: true };

    const fileResponse = await uploadFile(file, formData, axiosConfig);
    if (fileResponse) {
      inputData.fileId = fileResponse?._id;
      inputData.totalPages = inputType === CONSTANT.IMAGE ? endPage : totalPages;
      inputData.startPage = startPage;
      inputData.endPage = endPage;
    } else {
      setUploadStatus(subData._id, UPLOAD_STATUS.ERROR);
      return;
    }

    const inputResponse = await updateInputMarkTest({ _id: subData._id, inputData });
    if (inputResponse) {
      setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState[0].inputs ||= [];
        newState[0].inputs = newState[0].inputs.map(input => {
          return input._id === inputResponse._id ? inputResponse : input;
        });
        return newState;
      });
    }
    setUploadStatus(subData._id, inputResponse ? UPLOAD_STATUS.SUCCESS : UPLOAD_STATUS.ERROR, 100);
  }

  function setUploadStatus(submissionId, status, percent = undefined) {
    setFileUploadStatus(prevState => {
      if (percent === 99 && prevState[submissionId].percent === 100) {
        return prevState;
      }
      const newState = { ...prevState };
      newState[submissionId] ||= {};
      newState[submissionId].status = status;
      if (percent !== undefined) newState[submissionId].percent = percent;

      return newState;
    });
  }

  const updateFilesUpload = (submissionId, file, startPage, endPage) => {
    setFilesUpload((pre => {
      const newState = { ...pre };
      newState[submissionId] ||= {};
      newState[submissionId].file = file;
      newState[submissionId].startPage = startPage;
      newState[submissionId].endPage = endPage;
      return newState
    }))
  }

  const onChangeInputType = (type) => {
    setInputType(type);
  }

  const clearDataOtherTab = () => {
    if (inputType === CONSTANT.TEXT) {
      if (pdfFile) {
        setPdfFile(null);
        setPageRange({ startPage: 0, endPage: 0 });
        setTotalPages(0);
      }
      imageList.length && setImageList([]);
    } else if (inputType === CONSTANT.FILE) {
      imageList.length && setImageList([]);
      formAddSubmission.setFieldsValue({ text: '' });
    } else if (inputType === CONSTANT.IMAGE) {
      if (pdfFile) {
        setPdfFile(null);
        setPageRange({ startPage: 0, endPage: 0 });
        setTotalPages(0);
      }
      formAddSubmission.setFieldsValue({ text: '' });
    }
  }

  return (
    <AntModal
      footerless
      className="exam-submission-modal"
      open={stateSubmission.isShowModal}
      onCancel={() => handleShowSubmission(false)}
      title={t(stateSubmission.submissionSelected ? "EDIT_SUBMISSION" : "ADD_SUBMISSION")}
      confirmLoading={isLoadingModal}
    >
      <Loading active={isLoadingModal}>
        <AntForm
          form={formAddSubmission}
          layout="vertical"
          size={"large"}
          className="form-add-submission"
          id="form-add-submission"
          onFinish={onSaveSubmission}
        >
          <AntForm.Item
            label={t("STUDENT_NAME")}
            name="studentName"
            rules={[{ required: true, lang: "STUDENT_NAME_VALIDATION_MESSAGE" }]}>
            <Input placeholder={t("ENTER_STUDENT_NAME")} />
          </AntForm.Item>

          <div className="form-add-submission__content">
            <div className="content-title">{t("CONTENT")}</div>

            <div className="content-type">
              <AntButton
                icon={<Text />}
                size="compact"
                type={inputType === CONSTANT.TEXT ? BUTTON.DEEP_NAVY : BUTTON.LIGHT_NAVY}
                onClick={() => onChangeInputType(CONSTANT.TEXT)}
              >
                {t("ENTER_TEXT")}
              </AntButton>
              <AntButton
                icon={<Camera />}
                size="compact"
                type={inputType === CONSTANT.IMAGE ? BUTTON.DEEP_GREEN : BUTTON.LIGHT_GREEN}
                onClick={() => onChangeInputType(CONSTANT.IMAGE)}
              >
                {t("UPLOAD_IMAGE")}
              </AntButton>
              <AntButton
                icon={<File />}
                size="compact"
                type={inputType === CONSTANT.FILE ? BUTTON.DEEP_PURPLE : BUTTON.LIGHT_PURPLE}
                onClick={() => onChangeInputType(CONSTANT.FILE)}
              >
                {t("UPLOAD_PDF")}
              </AntButton>
            </div>

            {inputType === CONSTANT.TEXT && <AntForm.Item
              rules={[
                { required: true, lang: "SUBMISSION_CONTENT_VALIDATION_MESSAGE" },
                () => ({
                  validator(_, value) {
                    if (value && stringSplit(value).length > 3000) {
                      return Promise.reject(t("PLEASE_ENTER_NO_MORE_THAN_WORDS").format(3000));
                    }
                    return Promise.resolve();
                  },
                })]}
              name="text">
              <Input.TextArea
                className="submission-content-textarea"
                autoSize={{ minRows: 3 }}
                count={{
                  show: true, max: 3000,
                  strategy: (txt) => stringSplit(txt).length,
                }}
                placeholder={t("ENTER_CONTENT")}
                onChange={() => clearDataOtherTab()}
              />
            </AntForm.Item>}

            <ImagesContent
              imageList={imageList}
              setImageList={setImageList}
              inputType={inputType}
              clearDataOtherTab={clearDataOtherTab}
            />

            <PdfContent
              pdfFile={pdfFile} setPdfFile={setPdfFile}
              inputType={inputType}
              pageRange={pageRange}
              setPageRange={setPageRange}
              clearDataOtherTab={clearDataOtherTab}
              validatePageRange={validatePageRange}
              totalPages={totalPages}
              setTotalPages={setTotalPages}
            />
          </div>

        </AntForm>
      </Loading>
      <div className="exam-submission-modal-actions">
        <AntButton
          size="large"
          onClick={() => handleShowSubmission(false)}
          type={BUTTON.WHITE}
          disabled={isLoadingModal}
        >
          {t("CANCEL")}
        </AntButton>
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          htmlType="submit"
          form="form-add-submission"
          disabled={isLoadingModal}
        >
          {t(stateSubmission.submissionSelected ? "SAVE" : "ADD_SUB")}
        </AntButton>

        {stateSubmission.submissionSelected && <AntButton
          size="large"
          type={BUTTON.LIGHT_NAVY}
          disabled={isLoadingModal}
          onClick={handleUpdateAndMark}
        >
          {t("SAVE_AND_MARK")}
        </AntButton>}
      </div>
    </AntModal>
  );
};
export default ModalSubmission;