import { Form, Input, Switch } from 'antd';
import clsx from 'clsx';
import { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { connect, useDispatch } from 'react-redux';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import AntButton from '@src/app/component/AntButton';
import { AntForm } from '@src/app/component/AntForm';
import OutputLanguage from '@src/app/component/OutputLanguage';
import { SelectCommon } from '@src/app/component';
import HelperContainer, {
  FloatingHelpButton,
  GeneratedIdeaContent,
  GeneratedUnderstandingContent,
  VocabularyContent,
} from './HelperContainer';
import ImageTopic from './ImageInput';
import ModalUploadFile from './ModalUploadFile';
import PreviewImageEssay from './PreviewImageEssay';
import PreviewPdfEssay from './PreviewPdfEssay';

import DeleteIcon from '@component/SvgIcons/DeleteIcon';
import TwinStars25 from '@component/SvgIcons/TwinStars/TwinStars25';
import SpeakingUpload from '@component/SvgIcons/Upload/SpeakingUpload';
import StudentProjectName from '@src/app/component/StudentProjectName';
import ROLLING from '@src/asset/gif/rolling.gif';
import ARROW_ICON from '@src/asset/icon/arrow/arrow-top-right.svg';
import INFO_CIRCLE_ERROR from '@src/asset/icon/info/info-circle-error.svg';
import SUBMITTING_SPEAKING from '@src/asset/icon/submitting-speaking.svg';
import HEADER_ICON from '@src/asset/icon/writing-feedback.svg';
import AI_GEN_STAR from '@src/assets/icons/ai-gen-star.svg';
import DOUBLE_ARROW from '@src/assets/icons/double-arrow.svg';
import REMOVE_ICON from '@src/assets/icons/remove.svg';

import { BUTTON, CONSTANT, LANGUAGE, TYPE_OF_TOOL } from '@constant';
import { LINK } from '@link';
import { SECTION_TYPES, TASK_TYPES } from '@src/constants';

console.log('CONSTANTS:', { SECTION_TYPES, TASK_TYPES });

import { deleteFile, deleteImage } from '@src/app/services/File';
import { updateProject } from '@src/app/services/Project';
import {
  createWritingStudent,
  generateIdea,
  helpMeUnderstand,
  helpMeWrite,
  submitWritingStudent,
} from '@src/app/services/Writing';
import { stringSplit } from '@src/common/functionCommons';

import RULE from '@rule';
import GenerateTopicPopover from '@src/app/component/GenerateTopicPopover';
import { ACTION_STATUS, actions, PARAM_CATEGORIES, paramsCreators, TRACKING_ACTIONS } from '@src/ducks/tracking.duck';
import './Writing.scss';

export const CreateWritingContext = createContext();

// Constants
const DEFAULT_PROJECT_NAME = 'Untitled essay';
const DEFAULT_LANGUAGE = 'Vietnamese';
const EMPTY_TEXT = '';
const TEST_MODE_MINUTES = {
  TASK1: 20,
  TASK2: 40,
};
const TEST_MODE_SECONDS = testTimer => testTimer * 60;
const LINE_BREAK = '\n\n';
const DEFAULT_TAB = 'topic';

// Tab names
const TABS = {
  TOPIC: 'topic',
  INTRODUCTION: 'introduction',
  BODY: 'body',
  CONCLUSION: 'conclusion',
  OVERVIEW: 'overview',
};

// Section display names
const SECTION_DISPLAY = {
  INTRODUCTION: 'INTRODUCTION',
  BODY: 'BODY_PARAGRAPHS',
  CONCLUSION: 'CONCLUSION',
  OVERVIEW: 'OVERVIEW',
};

// Section placeholders
const SECTION_PLACEHOLDERS = {
  INTRODUCTION: 'WRITE_YOUR_INTRODUCTION',
  BODY: 'WRITE_YOUR_BODY_PARAGRAPHS',
  CONCLUSION: 'WRITE_YOUR_CONCLUSION',
  OVERVIEW: 'WRITE_YOUR_OVERVIEW',
};

// Initial State values
const INITIAL_PAGE_RANGE = {
  startPage: 0,
  endPage: 0,
};

const INITIAL_GENERATING_SECTIONS = {
  introduction: false,
  body: false,
  conclusion: false,
};

const INITIAL_TAB_CONTENTS = {
  topic: null,
  introduction: null,
  body: null,
  conclusion: null,
};

const CreateWriting = ({ availableWorkspaces, studentTools, props }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [formEssay] = Form.useForm();
  const { writing: writingTools } = studentTools || {};
  const dispatch = useDispatch();

  const [toolId, setToolId] = useState('');
  const [isPracticeMode, setIsPracticeMode] = useState(false);
  const [isTestMode, setIsTestMode] = useState(false);
  const [testTimer, setTestTimer] = useState(0);
  const [timerActive, setTimerActive] = useState(false);

  const [imageTopicId, setImageTopicId] = useState(EMPTY_TEXT);

  const [imageEssayIds, setImageEssayIds] = useState([]);

  const [fileEssayId, setFileEssayId] = useState(EMPTY_TEXT);
  const [pageRange, setPageRange] = useState(INITIAL_PAGE_RANGE);
  const [totalPages, setTotalPages] = useState(0);

  const [checkImageTopic, setCheckImageTopic] = useState(false);

  const [isSubmitting, setSubmitting] = useState(false);
  const [isError, setError] = useState(false);

  const [introText, setIntroText] = useState(EMPTY_TEXT);
  const [bodyText, setBodyText] = useState(EMPTY_TEXT);
  const [conclusionText, setConclusionText] = useState(EMPTY_TEXT);
  const [overviewText, setOverviewText] = useState(EMPTY_TEXT);

  const [essayFocused, setEssayFocused] = useState(false);
  // console.log(essayFocused)

  const workspaceId = availableWorkspaces?.[0]?._id || null;

  const [activeTab, setActiveTab] = useState(DEFAULT_TAB);
  const [helperContent, setHelperContent] = useState(EMPTY_TEXT);
  const [isHelperCollapsed, setIsHelperCollapsed] = useState(true);
  const [isGeneratingIdea, setIsGeneratingIdea] = useState(false);
  const [generatedIdeaContent, setGeneratedIdeaContent] = useState(EMPTY_TEXT);
  const [isShowingIdeaPanel, setIsShowingIdeaPanel] = useState(false);
  const [isShowingVocabPanel, setIsShowingVocabPanel] = useState(false);
  const [isAnalyzingTopic, setIsAnalyzingTopic] = useState(false);

  const [isHelpingWrite, setIsHelpingWrite] = useState(false);

  // Giữ lại state hiển thị của các button
  const [isHelpButtonVisible, setIsHelpButtonVisible] = useState(false);
  const [isIdeaButtonVisible, setIsIdeaButtonVisible] = useState(false);
  const [isVocabButtonVisible, setIsVocabButtonVisible] = useState(false);

  // Thêm state tracking cho từng loại section đang được tạo
  const [generatingSections, setGeneratingSections] = useState(INITIAL_GENERATING_SECTIONS);

  // Thêm state để lưu trữ dữ liệu từng tab
  const [tabContents, setTabContents] = useState(INITIAL_TAB_CONTENTS);

  // Thêm ref để theo dõi lần mount đầu tiên
  const isInitialMountRef = useRef(true);

  // Thêm ref để lưu trữ giá trị trước đó của topic
  const prevTopicRef = useRef(EMPTY_TEXT);

  // Thêm ref để lưu trữ giá trị trước đó của imageTopicId
  const prevImageTopicIdRef = useRef(EMPTY_TEXT);

  // Thêm state để lưu trữ topic hiện tại
  const [currentTopic, setCurrentTopic] = useState(EMPTY_TEXT);

  useEffect(() => {
    if (imageEssayIds.length) {
      setFileEssayId('');
      formEssay.setFieldsValue({
        text: '',
      });
    }
  }, [imageEssayIds]);

  useEffect(() => {
    if (fileEssayId) {
      setImageEssayIds([]);
      formEssay.setFieldsValue({
        text: '',
      });
    }
  }, [fileEssayId]);

  useEffect(() => {
    const { projectData } = location.state || {};
    if (projectData) {
      handleGoBack(projectData);
    } else {
      formEssay.setFieldsValue({
        projectName: DEFAULT_PROJECT_NAME,
        language: DEFAULT_LANGUAGE,
        topic: EMPTY_TEXT,
        text: EMPTY_TEXT,
      });
    }
  }, [location.state]);

  useEffect(() => {
    if (isPracticeMode) {
      setHelperContent(<GeneratedUnderstandingContent content={t('DEFAULT_HELPER_MESSAGE')} />);
    }
  }, [isPracticeMode, t]);

  const [isShowModal, setShowModal] = useState(false);

  const toolInfo = useMemo(() => {
    const toolData = writingTools?.find(tool => tool._id === toolId);
    return toolData;
  }, [writingTools, toolId]);

  const isTask1 = useMemo(() => {
    return toolInfo?.inputType === TYPE_OF_TOOL.MARK_TEST_TASK_1 || toolInfo?.inputType === TYPE_OF_TOOL.STUDENT_TASK_1;
  }, [toolInfo]);

  useEffect(() => {
    if (!location.state && writingTools?.length) {
      setToolId(writingTools[0]._id);
    }
  }, [writingTools, location.state]);

  useEffect(() => {
    let interval;
    if (isTestMode && timerActive && testTimer > 0 && essayFocused) {
      interval = setInterval(() => {
        setTestTimer(prev => prev - 1);
      }, 1000);
    } else if (testTimer === 0) {
      setTimerActive(false);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isTestMode, timerActive, testTimer, essayFocused]);

  useEffect(() => {
    if (!isTestMode) {
      setEssayFocused(false);
    }
  }, [isTestMode]);

  useEffect(() => {
    if (isPracticeMode) {
      formEssay.setFieldsValue({ text: combinedText });
    }
  }, [introText, bodyText, conclusionText, overviewText, isPracticeMode, formEssay]);

  const combinedText = isTask1 ? `${introText}${LINE_BREAK}${overviewText}${LINE_BREAK}${bodyText}` : `${introText}${LINE_BREAK}${bodyText}${LINE_BREAK}${conclusionText}`;

  // Lắng nghe sự thay đổi của topic
  useEffect(() => {
    const topicField = formEssay.getFieldValue('topic') || EMPTY_TEXT;
    setCurrentTopic(topicField);
    // Lần đầu tiên mount, cập nhật các giá trị tham chiếu mà không reset
    if (isInitialMountRef.current) {
      prevTopicRef.current = topicField;
      prevImageTopicIdRef.current = imageTopicId;
      isInitialMountRef.current = false;
    }
  }, []); // Chỉ chạy một lần khi component mount

  // Theo dõi sự thay đổi của current topic hoặc imageTopicId
  useEffect(() => {
    // Bỏ qua lần đầu tiên mount
    if (!isInitialMountRef.current && (currentTopic !== prevTopicRef.current || imageTopicId !== prevImageTopicIdRef.current)) {
      // Reset tất cả nội dung của HelperContent
      setTabContents(INITIAL_TAB_CONTENTS);
      setGeneratedIdeaContent(EMPTY_TEXT);
      setHelperContent(<GeneratedUnderstandingContent content={t('TOPIC_HELPER_MESSAGE')} />);
      setActiveTab(TABS.TOPIC);

      // Reset các trạng thái tạo nội dung
      setGeneratingSections(INITIAL_GENERATING_SECTIONS);
      setIsAnalyzingTopic(false);
      setIsHelpingWrite(false);
      setIsGeneratingIdea(false);

      // Reset trạng thái hiển thị panel
      setIsHelperCollapsed(true);
      setIsShowingIdeaPanel(false);
      setIsShowingVocabPanel(false);

      // Reset trạng thái hiển thị button
      setIsHelpButtonVisible(false);
      setIsIdeaButtonVisible(false);
      setIsVocabButtonVisible(false);

      // Cập nhật giá trị tham chiếu
      prevTopicRef.current = currentTopic;
      prevImageTopicIdRef.current = imageTopicId;
    }
  }, [currentTopic, imageTopicId, t, isTask1]);

  async function handleGoBack(projectData) {
    const input = projectData.content[0]?.responses?.find(response => response.isActivate)?.inputId;
    const projectName = projectData.projectName;
    const {
      language = 'Vietnamese',
      text = '',
      topic = '',
      topicImageId = '',
      imageIds = [],
      fileId = '',
      startPage = 0,
      endPage = 0,
      totalPages = 0,
    } = input?.inputData;

    setToolId(input?.toolId);
    setImageTopicId(topicImageId);
    setImageEssayIds(imageIds);
    setFileEssayId(fileId);
    setPageRange({ startPage, endPage });
    setTotalPages(totalPages);
    formEssay.setFieldsValue({ text, topic, projectName, language });
  }

  function getToolNameById(id) {
    const toolData = writingTools?.find(tool => tool._id === id);
    return toolData.name || id;
  }

  const handleChangeTool = id => {
    setToolId(id);
    setCheckImageTopic(false);
    formEssay.setFieldsValue({ text: '', topic: '', language: 'Vietnamese', projectName: 'Untitled essay' });
    setImageTopicId('');
    setImageEssayIds([]);
    setFileEssayId('');
    setPageRange({
      startPage: 0,
      endPage: 0,
    });

    setEssayFocused(false)
    const timerValue = isTask1 ? TEST_MODE_MINUTES.TASK1 : TEST_MODE_MINUTES.TASK2;
    setTestTimer(TEST_MODE_SECONDS(timerValue));
    setTimerActive(true)

    // Kiểm tra loại task và điều chỉnh activeTab
    const selectedTool = writingTools?.find(tool => tool._id === id);
    const isSelectedToolTask1 = selectedTool?.inputType === TYPE_OF_TOOL.MARK_TEST_TASK_1 || selectedTool?.inputType === TYPE_OF_TOOL.STUDENT_TASK_1;

    if (isPracticeMode) {
      // Nếu đang ở tab conclusion và chuyển sang task 1, chuyển về tab body
      if (activeTab === TABS.CONCLUSION && isSelectedToolTask1) {
        setActiveTab(TABS.BODY);
      }
      // Nếu đang ở tab overview và chuyển sang task 2, chuyển về tab body
      else if (activeTab === TABS.OVERVIEW && !isSelectedToolTask1) {
        setActiveTab(TABS.BODY);
      }
    }

    dispatch(actions.trackCustomClickLabel(TRACKING_ACTIONS.SELECT_TEST_TYPE, getToolNameById(id)));
  };

  function handleToggleModal() {
    setShowModal(pre => !pre);
  }

  const onBlurPropjectName = e => {
    let newProjectName = e.target.value;
    if (!newProjectName || !newProjectName.trim()) {
      formEssay.setFieldsValue({ projectName: 'Untitled essay' });
    }
  };

  const onDeleteEssayFile = () => {
    setImageEssayIds([]);
    setFileEssayId('');
  };

  const onChangeOutputLanguage = key => {
    const newLanguage = key === LANGUAGE.EN ? 'English' : 'Vietnamese';
    formEssay.setFieldsValue({ language: newLanguage });
    dispatch(actions.trackSelectLanguageResult(newLanguage, toolInfo?.name));
  };

  const repareProjectname = (projectName, topic) => {
    if (projectName === 'Untitled essay') {
      const firstLineTopic = topic?.split('\n').find(line => line.trim());
      return firstLineTopic;
    }
    return projectName;
  };

  function handleFileChanged(oldData = {}, newData = {}) {
    const oldImageIds = oldData.imageIds || [];
    const oldFileId = oldData.fileId || null;
    const newImageIds = newData.imageIds || [];
    const newFileId = newData.fileId || null;

    // Tìm các ID ảnh trong dữ liệu cũ không tồn tại trong dữ liệu mới
    const removedImageIds = oldImageIds.filter(id => !newImageIds.includes(id));

    // Kiểm tra xem fileId trong dữ liệu cũ có nằm trong dữ liệu mới hay không
    const removedFileId = oldFileId && oldFileId !== newFileId ? oldFileId : null;

    removedImageIds?.length && removedImageIds.forEach(imageId => deleteImage(imageId));
    removedFileId && deleteFile(removedFileId);
  }

  const handleGenerateIdea = async () => {
    if (isTask1 && !imageTopicId) {
      setCheckImageTopic(true);
      return;
    }
    setIsGeneratingIdea(true);

    try {
      const topic = formEssay.getFieldValue('topic');
      if (!topic || !topic.trim()) {
        setGeneratedIdeaContent(t('EMPTY_TOPIC_IDEA_MESSAGE'));
        openIdeaPanel();
        return;
      }

      const data = {
        topic: topic,
        taskType: isTask1 ? 'task1' : 'task2',
        ...(isTask1 && imageTopicId ? { topicImageId: imageTopicId } : {}),
      };

      const response = await generateIdea(data);

      if (response && response.success) {
        setGeneratedIdeaContent(response.result);
      } else {
        setGeneratedIdeaContent(t('FAILED_IDEA_MESSAGE'));
      }

      openIdeaPanel();
    } catch (error) {
      console.error('Error generating idea:', error);
      setGeneratedIdeaContent(t('ERROR_GENERATING_IDEA'));
      openIdeaPanel();
    } finally {
      setIsGeneratingIdea(false);
      dispatch(actions.trackCustomClick(TRACKING_ACTIONS.GENERATE_IDEA));
    }
  };

  const handleFindVocab = () => {
    if (isTask1 && !imageTopicId) {
      setCheckImageTopic(true);
      return;
    }
    openVocabPanel();
    dispatch(actions.trackCustomClick(TRACKING_ACTIONS.FIND_VOCAB));
  };

  // Thay đổi các hàm toggle
  const toggleHelperCollapse = () => {
    const nextCollapsedState = !isHelperCollapsed;
    setIsHelperCollapsed(nextCollapsedState);
    if (!nextCollapsedState) {
      // Khi mở panel chính, đóng các panel khác
      setIsShowingIdeaPanel(false);
      setIsShowingVocabPanel(false);
      // Nếu đang ở practice mode và panel chính (helper) được mở,
      // gọi handleTabChange để làm mới nội dung cho activeTab hiện tại.
      if (isPracticeMode) {
        handleTabChange(activeTab);
      }
    }
    setIsHelpButtonVisible(true);
  };

  const openHelperPanel = () => {
    setIsHelperCollapsed(false);
    setIsShowingIdeaPanel(false);
    setIsShowingVocabPanel(false);
    setIsHelpButtonVisible(true);
    handleTabChange(activeTab);
  }

  const toggleIdeaPanel = () => {
    const nextShowState = !isShowingIdeaPanel;
    setIsShowingIdeaPanel(nextShowState);
    if (nextShowState) {
      // Khi mở panel idea, đóng các panel khác
      setIsHelperCollapsed(true);
      setIsShowingVocabPanel(false);
    }
    setIsIdeaButtonVisible(true);
  };

  const openIdeaPanel = () => {
    setIsHelperCollapsed(true);
    setIsShowingIdeaPanel(true);
    setIsShowingVocabPanel(false);
    setIsIdeaButtonVisible(true);
  }

  const toggleVocabPanel = () => {
    const nextShowState = !isShowingVocabPanel;
    setIsShowingVocabPanel(nextShowState);
    if (nextShowState) {
      // Khi mở panel vocab, đóng các panel khác
      setIsHelperCollapsed(true);
      setIsShowingIdeaPanel(false);
    }
    setIsVocabButtonVisible(true);
  };

  const openVocabPanel = () => {
    setIsHelperCollapsed(true);
    setIsShowingIdeaPanel(false);
    setIsShowingVocabPanel(true);
    setIsVocabButtonVisible(true);
  }

  const onSubmit = async values => {
    const { projectName, topic, language } = values;
    let essayText;

    if (isPracticeMode) {
      essayText = combinedText;
    } else {
      essayText = values.text;
    }

    const repareProjectName = repareProjectname(projectName, topic);
    setCheckImageTopic(true);
    if ((isTask1 && !imageTopicId) || isSubmitting) return;

    const dataRequest = {
      toolId,
      workspaceId,
      projectName: repareProjectName,
    };

    setSubmitting(true);
    setError(false);

    let projectResponse;
    if (location?.state?.projectData) {
      dataRequest._id = location?.state?.projectData?._id;
      projectResponse = await updateProject(dataRequest, true, { workspaceId });
      projectResponse.contentId = location?.state?.projectData?.contentId;
    } else {
      projectResponse = await createWritingStudent(dataRequest);
    }

    if (projectResponse) {
      const dataSubmit = {
        workspaceId,
        contentId: projectResponse.contentId,
        inputType: toolInfo.inputType,
        inputData: {
          topic,
          text: essayText,
          language,
          markTestType: imageEssayIds.length ? 'image' : fileEssayId ? 'file' : 'text',
          ...(imageEssayIds.length ? { imageIds: imageEssayIds } : {}),
          ...(fileEssayId
            ? {
              fileId: fileEssayId,
              ...pageRange,
              totalPages,
            }
            : {}),
          instructionId: toolInfo.instructionIds?.[0]?._id,
          ...(isTask1 ? { topicImageId: imageTopicId } : {}),
        },
      };
      if (imageEssayIds.length || fileEssayId) {
        delete dataSubmit.inputData.text;
      }
      const submitResponse = await submitWritingStudent(dataSubmit);
      if (submitResponse) {
        const oldInputData = location?.state?.projectData?.content[0]?.responses?.find(response => response.isActivate)
          ?.inputId?.inputData;
        const newInputData = submitResponse.inputId.inputData;
        handleFileChanged(oldInputData, newInputData);

        const stateData = {
          ...projectResponse,
          content: [
            {
              _id: projectResponse.contentId,
              inputs: [{ ...submitResponse.inputId }],
              projectId: projectResponse._id,
              responses: [{ ...submitResponse }],
            },
          ],
          checkFeedback: true,
        };
        navigate(LINK.WRITING_RESULT.format(projectResponse._id), { state: { data: stateData } });
        setSubmitting(false);
      } else {
        setError(true);
        setSubmitting(false);
      }
    } else {
      setError(true);
      setSubmitting(false);
    }
  };

  const handleTopicGenerated = topic => {
    formEssay.setFieldsValue({ topic });
    setCurrentTopic(topic);
  };

  const handleTogglePracticeMode = checked => {
    setIsPracticeMode(checked);
    if (checked) {
      setIsTestMode(false);
      setTimerActive(false);
      setIsHelperCollapsed(true);
      setIsShowingIdeaPanel(false);
      setIsShowingVocabPanel(false);

      // Nếu đang ở tab conclusion nhưng đang dùng task 1, hoặc đang ở tab overview nhưng dùng task 2,
      // chuyển về tab body để tránh hiển thị tab không phù hợp
      if ((activeTab === TABS.CONCLUSION && isTask1) || (activeTab === TABS.OVERVIEW && !isTask1)) {
        setActiveTab(TABS.BODY);
      }
    }
  };

  const handleToggleTestMode = checked => {
    setIsTestMode(checked);
    if (checked) {
      setIsPracticeMode(false);
      const timerValue = isTask1 ? TEST_MODE_MINUTES.TASK1 : TEST_MODE_MINUTES.TASK2;
      setTestTimer(TEST_MODE_SECONDS(timerValue));
      setTimerActive(true);
    } else {
      setTimerActive(false);
    }
  };

  const formatTime = seconds => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleIntroChange = e => {
    setIntroText(e.target.value);
  };

  const handleBodyChange = e => {
    setBodyText(e.target.value);
  };

  const handleConclusionChange = e => {
    setConclusionText(e.target.value);
  };

  const handleOverviewChange = e => {
    setOverviewText(e.target.value);
  };

  const handleTabChange = tab => {
    console.log('tabContents', tabContents);
    // Hiển thị nội dung đã lưu nếu có
    if (tabContents[tab]) {
      setHelperContent(<GeneratedUnderstandingContent content={tabContents[tab]} />);
      return;
    }

    // Nếu chưa có dữ liệu đã lưu, hiển thị nội dung mặc định
    switch (tab) {
      case TABS.TOPIC: {
        // Nếu đang ở tab topic, hiển thị GeneratedUnderstandingContent với nội dung mặc định
        let topic = formEssay.getFieldValue('topic');
        if (topic) {
          // Nếu đã có topic, hiển thị GeneratedUnderstandingContent
          setHelperContent(<GeneratedUnderstandingContent content={t('TOPIC_HELPER_MESSAGE')} />);
        } else {
          // Nếu chưa có topic, hiển thị hướng dẫn
          setHelperContent(<GeneratedUnderstandingContent content={t('TOPIC_EMPTY_HELPER_MESSAGE')} />);
        }
        break;
      }
      case TABS.INTRODUCTION:
      case TABS.BODY:
      case TABS.OVERVIEW:
      case TABS.CONCLUSION:
        setHelperContent(<GeneratedUnderstandingContent content={t('SECTION_HELPER_MESSAGE')} />);
        break;
      default:
        setHelperContent(EMPTY_TEXT);
    }
  };

  useEffect(() => {
    handleTabChange(activeTab);
  }, [activeTab]);

  const handleHelpMeUnderstand = async () => {
    if (isTask1 && !imageTopicId) {
      setCheckImageTopic(true);
      return;
    }
    try {
      openHelperPanel();
      // Lấy topic từ form
      const topic = formEssay.getFieldValue('topic');
      if (!topic) {
        return;
      }

      // Mở helper nếu đang đóng
      if (isHelperCollapsed) {
        setIsHelperCollapsed(false);
      }

      // Chuyển tab về topic
      setActiveTab(TABS.TOPIC);

      // Bắt đầu phân tích
      setIsAnalyzingTopic(true);

      // Hiển thị loading
      setHelperContent(<GeneratedUnderstandingContent isLoading={true} />);

      // Gọi API helpMeUnderstand với topic và taskType
      const data = {
        topic,
        taskType: isTask1 ? TASK_TYPES.TASK1 : TASK_TYPES.TASK2,
      };

      // Thêm topicImageId nếu có
      if (isTask1 && imageTopicId) {
        data.topicImageId = imageTopicId;
      }

      const response = await helpMeUnderstand(data);

      // Hiển thị kết quả nếu thành công
      if (response?.success && response?.result) {
        // Lưu nội dung vào state
        setTabContents(prev => ({
          ...prev,
          [TABS.TOPIC]: response.result,
        }));

        setHelperContent(<GeneratedUnderstandingContent content={response.result} />);

        // Không cần cập nhật các text field trong topic tab
      } else {
        setHelperContent(<GeneratedUnderstandingContent content={t('ERROR_GENERATING_CONTENT')} />);
      }
      setIsAnalyzingTopic(false);
    } catch (error) {
      console.error('Error in handleHelpMeUnderstand:', error);
      setHelperContent(<GeneratedUnderstandingContent content={t('ERROR_ANALYZING_TOPIC')} />);
      setIsAnalyzingTopic(false);
    }
  };

  const handleHelpMeWrite = async section => {
    if (tabContents[section]) {
      openHelperPanel();
      setActiveTab(section);
      return;
    }

    if (isTask1 && !imageTopicId) {
      setCheckImageTopic(true);
      return;
    }
    try {
      openHelperPanel();
      // Lấy topic từ form
      const topic = formEssay.getFieldValue('topic');
      if (!topic) {
        return;
      }

      // Mở helper nếu đang đóng
      if (isHelperCollapsed) {
        setIsHelperCollapsed(false);
      }

      // Chuyển tab về section tương ứng
      setActiveTab(section);

      // Bắt đầu tạo nội dung
      setIsHelpingWrite(true);

      // Cập nhật trạng thái đang tạo cho section cụ thể
      setGeneratingSections(prev => ({
        ...prev,
        [section]: true,
      }));

      // Hiển thị loading
      setHelperContent(<GeneratedUnderstandingContent isLoading={true} />);

      // Xác định section type dựa trên section param
      let sectionType;
      switch (section) {
        case TABS.INTRODUCTION:
          sectionType = SECTION_TYPES.INTRODUCTION;
          break;
        case TABS.OVERVIEW:
          sectionType = SECTION_TYPES.OVERVIEW;
          break;
        case TABS.BODY:
          sectionType = SECTION_TYPES.BODY;
          break;
        case TABS.CONCLUSION:
          sectionType = SECTION_TYPES.CONCLUSION;
          break;
        case TABS.TOPIC:
          sectionType = SECTION_TYPES.TOPIC;
          break;
        default:
          sectionType = SECTION_TYPES.INTRODUCTION; // Mặc định là introduction thay vì FULL
      }

      // Gọi API helpMeWrite với topic, taskType và section và practiceContent
      const data = {
        topic,
        taskType: isTask1 ? TASK_TYPES.TASK1 : TASK_TYPES.TASK2,
        section: sectionType,
        practiceContent: {
          introduction: introText,
          body: bodyText,
          conclusion: conclusionText,
          overview: overviewText,
        },
      };

      // Thêm topicImageId nếu có
      if (isTask1 && imageTopicId) {
        data.topicImageId = imageTopicId;
      }

      const response = await helpMeWrite(data);

      // Hiển thị kết quả nếu thành công
      if (response?.success && response?.result) {
        // Lấy trực tiếp nội dung từ response mà không cần kiểm tra hay format
        const content = response.result;

        // Lưu nội dung vào state cho tab tương ứng
        setTabContents(prev => ({
          ...prev,
          [section]: content,
        }));
        setHelperContent(<GeneratedUnderstandingContent content={content} />);
      } else {
        setHelperContent(<GeneratedUnderstandingContent content={t('ERROR_GENERATING_CONTENT')} />);
      }
      setIsHelpingWrite(false);

      // Đặt lại trạng thái đang tạo cho section
      setGeneratingSections(prev => ({
        ...prev,
        [section]: false,
      }));
    } catch (error) {
      console.error('Error in handleHelpMeWrite:', error);
      setHelperContent(<GeneratedUnderstandingContent content={t('ERROR_WRITING')} />);
      setIsHelpingWrite(false);

      // Đặt lại trạng thái đang tạo cho section khi có lỗi
      setGeneratingSections(prev => ({
        ...prev,
        [section]: false,
      }));
    }
  };

  const renderHelpMeWriteButton = (section, label) => {
    // Xác định trạng thái loading
    const isLoading = section === 'topic' ? isAnalyzingTopic : generatingSections[section.toLowerCase()];

    // Xác định nhãn nút dựa trên trạng thái loading
    const buttonLabel = isLoading
      ? section === 'topic'
        ? t('ANALYZING_TOPIC')
        : t('GENERATING_CONTENT')
      : section === 'topic'
        ? t('HELP_ME_UNDERSTAND')
        : t('HELP_ME_WRITE');

    // Xác định trạng thái disabled
    const isDisabled =
      section === 'topic'
        ? isAnalyzingTopic || isHelpingWrite
        : isHelpingWrite || generatingSections[section.toLowerCase()];

    // Thêm class disabled nếu nút đang disabled
    const buttonClass = `help-write-btn ${isDisabled ? 'help-write-btn--disabled' : ''}`;

    return (
      <div
        className={buttonClass}
        onClick={isDisabled ? null : section === 'topic' ? handleHelpMeUnderstand : () => handleHelpMeWrite(section)}
      >
        {isLoading ? (
          // Hiển thị biểu tượng loading
          <div className="loading-icon-small"></div>
        ) : (
          <img src={AI_GEN_STAR} alt="AI Help" className="same-color-as-text" />
        )}
        <span>{buttonLabel}</span>
      </div>
    );
  };

  const renderPracticeSection = (section, title, value, onChange, placeholder) => (
    <AntForm.Item className="form-essay__item">
      <div className="essay__header">
        <label>{t(title)}</label>
        {isPracticeMode && !isSubmitting && renderHelpMeWriteButton(section)}
      </div>
      <AntForm.Item
        name={section}
        noStyle
        rules={[
          { ...RULE.REQUIRED, lang: 'PLEASE_ENTER_TEXT' },
          () => ({
            validator(_, value) {
              if (value && stringSplit(value).length > 500) {
                return Promise.reject(<span className="exceed-word-error"></span>);
              }
              return Promise.resolve();
            },
          }),
        ]}
      >
        <Input.TextArea
          value={value}
          onChange={onChange}
          disabled={isTestMode && !timerActive}
          autoSize={{
            minRows: title === SECTION_DISPLAY.BODY ? 5 : title === SECTION_DISPLAY.INTRODUCTION ? 3 : 2,
            maxRows: title === SECTION_DISPLAY.BODY ? 10 : title === SECTION_DISPLAY.INTRODUCTION ? 5 : 5,
          }}
          placeholder={t(placeholder)}
          count={{
            show: ({ _, count, maxLength }) => {
              let countText = '';
              const isError = count > maxLength;
              if (isError) {
                countText = `${count} ${t('WORDS')} | ${t('NO_MORE_THAN_MAX_WORD').format(maxLength)}`;
              } else {
                countText = `${count} ${t(count > 1 ? 'WORDS' : 'WORD')}`;
              }
              return <div className={clsx('text-area__count', { 'text-area__count-error': isError })}>{countText}</div>;
            },
            max: 500,
            strategy: txt => stringSplit(txt).length,
          }}
        />
      </AntForm.Item>
    </AntForm.Item>
  );

  // Thêm event handler cho sự kiện onChange của topic input
  const handleTopicChange = e => {
    setCurrentTopic(e.target.value);
  };

  // Thêm các hàm xử lý khi ẩn button
  const handleHideHelpButton = () => {
    setIsHelpButtonVisible(false);
  };

  const handleHideIdeaButton = () => {
    setIsIdeaButtonVisible(false);
  };

  const handleHideVocabButton = () => {
    setIsVocabButtonVisible(false);
  };

  // Cập nhật testTimer khi toolInfo thay đổi
  useEffect(() => {
    if (toolInfo) {
      const timerValue = isTask1 ? TEST_MODE_MINUTES.TASK1 : TEST_MODE_MINUTES.TASK2;
      setTestTimer(TEST_MODE_SECONDS(timerValue));
    }
  }, [toolInfo, isTask1]);

  return (
    <CreateWritingContext.Provider
      value={{
        imageTopicId,
        setImageTopicId,
        workspaceId,
        checkImageTopic,
        setCheckImageTopic,
      }}
    >
      <div className="writing-container-wrapper">
        <div className="writing-container">
          <div className="writing-inner">
            <div className="writing__header">
              <img src={HEADER_ICON} alt="" />
              <span className="writing__header__title">{t('WRITING_FEEDBACK')}</span>
              <div className="writing__header__my-essays">
                <span>{t('THE_ARTICLES_CREATED_RECENTLY')}</span>
                <Link
                  to={LINK.WRITING_ESSAYS}
                  onClick={() => dispatch(actions.trackCustomClick(TRACKING_ACTIONS.VIEW_ESSAY_LIST))}
                >
                  <span className="writing__header__my-essays__link">{t('MY_ESSAYS')}</span>
                  <img src={ARROW_ICON} alt="" />
                </Link>
              </div>
            </div>
            <div className="writing__content">
              <SelectCommon
                options={writingTools}
                fieldNames={{ label: 'name', value: '_id' }}
                className="writing__select-tool"
                value={toolId}
                onChange={handleChangeTool}
                disabled={!!location?.state?.projectData}
                onClick={() => {
                  dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_TEST_TYPE_SELECTION));
                }}
              />

              <AntForm form={formEssay} id="form-essay" layout="vertical" onFinish={onSubmit}>
                <AntForm.Item noStyle name="projectName">
                  <StudentProjectName onBlur={onBlurPropjectName} />
                </AntForm.Item>

                <div className="writing__mode-toggles">
                  <div className="mode-toggle">
                    <Switch
                      checked={isPracticeMode}
                      onChange={handleTogglePracticeMode}
                      className={clsx('custom-switch', { 'custom-switch-active': isPracticeMode })}
                    />
                    <span className="mode-toggle__label">{t('PRACTICE_WRITING')}</span>
                  </div>
                  <div className="mode-toggle">
                    <Switch
                      checked={isTestMode}
                      onChange={handleToggleTestMode}
                      className={clsx('custom-switch', { 'custom-switch-active': isTestMode })}
                    />
                    <span className="mode-toggle__label">{t('TEST_MODE')}</span>
                  </div>
                </div>

                {isTestMode && (
                  <div className="writing__test-timer">
                    <div className="test-timer">
                      <span className={`test-timer__label ${!timerActive && testTimer === 0 ? 'test-timer__label--ended' : ''}`}>
                        {!timerActive && testTimer === 0 ? t('TIMES_UP') : t('TIMER')}
                      </span>
                      <span className="test-timer__time">{formatTime(testTimer)}</span>
                    </div>
                  </div>
                )}

                <AntForm.Item className="form-essay__item">
                  <div>
                    <div className="essay__header">
                      <div className="essay__header__left">
                        <label className="ant-form-item-label">{t('TOPIC')}</label>
                        {!isTask1 && !isTestMode && (
                          <GenerateTopicPopover onTopicGenerated={handleTopicGenerated} tool={toolInfo} />
                        )}
                      </div>

                      {isPracticeMode && renderHelpMeWriteButton('topic')}
                    </div>
                    <AntForm.Item name="topic" noStyle rules={[{ ...RULE.REQUIRED, lang: 'PLEASE_ENTER_TOPIC' }]}>
                      <Input.TextArea
                        autoSize={{
                          minRows: 1,
                        }}
                        placeholder={t('YOUR_TOPIC')}
                        onChange={handleTopicChange}
                        onFocus={() =>
                          dispatch(actions.trackCustomClickType(TRACKING_ACTIONS.INPUT_TOPIC, toolInfo?.name))
                        }
                      />
                    </AntForm.Item>
                  </div>
                </AntForm.Item>

                {isTask1 && (
                  <ImageTopic
                    onFileDialogOpen={() => {
                      dispatch(
                        actions.trackCustomClickType(
                          TRACKING_ACTIONS.CLICK_UPLOAD,
                          toolInfo?.name,
                          PARAM_CATEGORIES.UPLOAD_TOPIC,
                        ),
                      );
                    }}
                    onUploadSuccess={() => {
                      dispatch(
                        actions.trackCustomClick(
                          TRACKING_ACTIONS.UPLOAD_STATUS,
                          paramsCreators.uploadStatus(
                            toolInfo?.name,
                            PARAM_CATEGORIES.UPLOAD_TOPIC,
                            ACTION_STATUS.SUCCESS,
                          ),
                        ),
                      );
                    }}
                    onUploadError={error => {
                      dispatch(
                        actions.trackCustomClick(
                          TRACKING_ACTIONS.UPLOAD_STATUS,
                          paramsCreators.uploadStatus(
                            toolInfo?.name,
                            PARAM_CATEGORIES.UPLOAD_TOPIC,
                            ACTION_STATUS.FAILED,
                            error,
                          ),
                        ),
                      );
                    }}
                  />
                )}

                {!isPracticeMode && (
                  <AntForm.Item className={clsx('form-essay__item', { 'form-essay__item--disabled': isTestMode && !timerActive })}>
                    <div>
                      <div className="essay__header">
                        <div className="essay__header__left">
                          <label className="ant-form-item-label">{t('ESSAY')}</label>
                          {!isTestMode && (
                            <>
                              <AntButton
                                size="xsmall"
                                type={BUTTON.DEEP_BLUE}
                                className="generate-idea-btn"
                                onClick={handleGenerateIdea}
                                loading={isGeneratingIdea}
                              >
                                {t('GENERATE_IDEA')}
                              </AntButton>
                              <AntButton
                                size="xsmall"
                                type={BUTTON.DEEP_PINK}
                                className="find-vocab-btn"
                                onClick={handleFindVocab}
                              >
                                {t('FIND_VOCAB')}
                              </AntButton>
                            </>
                          )}
                        </div>

                        <div className="essay__header__right">
                          {(!!imageEssayIds.length || fileEssayId) && !isTestMode && (
                            <AntButton
                              size="xsmall"
                              type={BUTTON.DEEP_PINK}
                              icon={<DeleteIcon />}
                              className={'btn-delete-essay'}
                              onClick={onDeleteEssayFile}
                            />
                          )}
                          {!isTestMode && (
                            <AntButton
                              size="xsmall"
                              type={BUTTON.DEEP_BLUE}
                              icon={<SpeakingUpload />}
                              onClick={() => {
                                handleToggleModal();
                                dispatch(
                                  actions.trackCustomClickType(
                                    TRACKING_ACTIONS.CLICK_UPLOAD,
                                    toolInfo?.name,
                                    PARAM_CATEGORIES.UPLOAD_ESSAY,
                                  ),
                                );
                              }}
                            >
                              {t('UPLOAD_FILE')}
                            </AntButton>
                          )}
                        </div>
                      </div>

                      <PreviewImageEssay imageIds={imageEssayIds} />
                      <PreviewPdfEssay fileId={fileEssayId} pageRange={pageRange} />

                      {!imageEssayIds?.length && !fileEssayId && (
                        <>
                          <div className="essay__notication">
                            {t(isTask1 ? 'WRITING_ESSAY_NOTICE_TASK1' : 'WRITING_ESSAY_NOTICE_TASK2')}
                          </div>
                          <AntForm.Item
                            name="text"
                            noStyle
                            rules={[
                              { ...RULE.REQUIRED, lang: 'PLEASE_ENTER_TEXT' },
                              () => ({
                                validator(_, value) {
                                  if (value && stringSplit(value).length > 500) {
                                    return Promise.reject(<span className="exceed-word-error"></span>);
                                  }
                                  return Promise.resolve();
                                },
                              }),
                            ]}
                          >
                            <Input.TextArea
                              autoSize={{
                                minRows: 3,
                                maxRows: 15,
                              }}
                              count={{
                                show: ({ _, count, maxLength }) => {
                                  let countText = '';
                                  const isError = count > maxLength;
                                  if (isError) {
                                    countText = `${count} ${t('WORDS')} | ${t('NO_MORE_THAN_MAX_WORD').format(
                                      maxLength,
                                    )}`;
                                  } else {
                                    countText = `${count} ${t(count > 1 ? 'WORDS' : 'WORD')}`;
                                  }
                                  return (
                                    <div className={clsx('text-area__count', { 'text-area__count-error': isError })}>
                                      {countText}
                                    </div>
                                  );
                                },
                                max: 500,
                                strategy: txt => stringSplit(txt).length,
                              }}
                              onChange={(e) => {
                                const value = e.target.value;
                                // console.log(value.trim().length)
                                // Nếu người dùng bắt đầu nhập và đồng hồ chưa chạy
                                if (value.trim().length > 0 && isTestMode) {
                                  setEssayFocused(true); // Bắt đầu đếm
                                }
                              }}
                              placeholder={t('YOUR_ESSAY')}
                              disabled={isTestMode && !timerActive}
                              onFocus={() => {
                                dispatch(actions.trackCustomClickType(TRACKING_ACTIONS.INPUT_ESSAY, toolInfo?.name))
                              }}
                            />
                          </AntForm.Item>
                        </>
                      )}
                    </div>
                  </AntForm.Item>
                )}
                {isPracticeMode && (
                  <div className="practice-writing-sections">
                    {renderPracticeSection(
                      TABS.INTRODUCTION,
                      SECTION_DISPLAY.INTRODUCTION,
                      introText,
                      handleIntroChange,
                      SECTION_PLACEHOLDERS.INTRODUCTION,
                    )}
                    {isTask1 && renderPracticeSection(
                      TABS.OVERVIEW,
                      SECTION_DISPLAY.OVERVIEW,
                      overviewText,
                      handleOverviewChange,
                      SECTION_PLACEHOLDERS.OVERVIEW,
                    )}
                    {renderPracticeSection(
                      TABS.BODY,
                      SECTION_DISPLAY.BODY,
                      bodyText,
                      handleBodyChange,
                      SECTION_PLACEHOLDERS.BODY,
                    )}
                    {!isTask1 && renderPracticeSection(
                      TABS.CONCLUSION,
                      SECTION_DISPLAY.CONCLUSION,
                      conclusionText,
                      handleConclusionChange,
                      SECTION_PLACEHOLDERS.CONCLUSION,
                    )}
                  </div>
                )}
                <AntForm.Item
                  noStyle
                  name="language"
                  getValueProps={value => ({ value: value === 'English' ? LANGUAGE.EN : LANGUAGE.VI })}
                >
                  <OutputLanguage onChange={onChangeOutputLanguage} />
                </AntForm.Item>
              </AntForm>
            </div>

            <div className="writing__submit">
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                htmlType="submit"
                className="writing__submit__btn"
                form="form-essay"
                onClick={() => {
                  setCheckImageTopic(true);
                  dispatch(actions.trackCustomClickType(TRACKING_ACTIONS.SUBMIT, toolInfo?.name));
                }}
                icon={
                  isError ? (
                    <img src={INFO_CIRCLE_ERROR} alt="" />
                  ) : isSubmitting ? (
                    <img className="rolling-icon" src={ROLLING} alt="" />
                  ) : (
                    <TwinStars25 />
                  )
                }
                iconLocation={isSubmitting || isError ? CONSTANT.LEFT : CONSTANT.RIGHT}
              >
                {t(isSubmitting ? 'SUBMITTING' : isError ? 'SUBMIT_FAILED' : 'SUBMIT')}
              </AntButton>

              {isSubmitting && (
                <div className="writing-submitting">
                  <img className="writing-submitting__image" src={SUBMITTING_SPEAKING} alt="" />
                  <div className="writing-submitting__text">{`${t('YOUR_FEEDBACK_IS_ON_THE_WAY')},`}</div>
                  <div className="writing-submitting__text">{`${t('PLEASE_WAIT_A_MOMENT').toLowerCase()}...`}</div>
                </div>
              )}
            </div>
          </div>

          <ModalUploadFile
            open={isShowModal}
            onClose={handleToggleModal}
            imageEssayIds={imageEssayIds}
            setImageEssayIds={setImageEssayIds}
            setFileEssayId={setFileEssayId}
            fileEssayId={fileEssayId}
            pageRange={pageRange}
            setPageRange={setPageRange}
            setTotalPages={setTotalPages}
          />
        </div>

        {/* Panel chính */}
        <div style={{ display: ((!isHelperCollapsed)) ? 'block' : 'none' }}>
          <HelperContainer
            activeTab={activeTab}
            helperContent={helperContent}
            handleTabChange={setActiveTab}
            toggleHelperCollapse={toggleHelperCollapse}
            aiGenStarIcon={AI_GEN_STAR}
            doubleArrowIcon={DOUBLE_ARROW}
            isTask1={isTask1}
          />
        </div>

        {/* Panel idea */}
        <div style={{ display: isShowingIdeaPanel ? 'block' : 'none' }}>
          <HelperContainer
            title={t('GENERATE_IDEA')}
            mode="generate-idea"
            helperContent={<GeneratedIdeaContent content={generatedIdeaContent} loading={isGeneratingIdea} />}
            toggleHelperCollapse={toggleIdeaPanel}
            aiGenStarIcon={AI_GEN_STAR}
            doubleArrowIcon={DOUBLE_ARROW}
          />
        </div>

        {/* Panel vocab */}
        <div style={{ display: isShowingVocabPanel ? 'block' : 'none' }}>
          <HelperContainer
            title={t('FIND_VOCAB')}
            mode="find-vocab"
            helperContent={
              <VocabularyContent
                topic={formEssay.getFieldValue('topic')}
                isTask1={isTask1}
                imageTopicId={imageTopicId}
              />
            }
            toggleHelperCollapse={toggleVocabPanel}
            aiGenStarIcon={AI_GEN_STAR}
            doubleArrowIcon={DOUBLE_ARROW}
          />
        </div>
      </div>

      {/* Container cho các FloatingHelpButton */}
      <div className="floating-help-button-container">
        {isHelperCollapsed && isHelpButtonVisible && (
          <FloatingHelpButton
            toggleHelperCollapse={toggleHelperCollapse}
            removeIcon={REMOVE_ICON}
            mode="help"
            onRemove={handleHideHelpButton}
          />
        )}

        {!isShowingIdeaPanel && isIdeaButtonVisible && (
          <FloatingHelpButton
            toggleHelperCollapse={toggleIdeaPanel}
            removeIcon={REMOVE_ICON}
            mode="generate-idea"
            onRemove={handleHideIdeaButton}
          />
        )}

        {!isShowingVocabPanel && isVocabButtonVisible && (
          <FloatingHelpButton
            toggleHelperCollapse={toggleVocabPanel}
            removeIcon={REMOVE_ICON}
            mode="find-vocab"
            onRemove={handleHideVocabButton}
          />
        )}
      </div>
    </CreateWritingContext.Provider>
  );
};

const mapStateToProps = state => ({
  studentTools: state.tool.studentTools,
  availableWorkspaces: state.workspace.availableWorkspaces,
});

const ConnectedCreateWriting = connect(mapStateToProps, {})(CreateWriting);
const useCreateWriting = () => useContext(CreateWritingContext);

export { ConnectedCreateWriting as CreateWriting, useCreateWriting };
