.pricing-package-vinaphone {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  width: 1080px;
  max-width: 100%;
  // justify-content: center;

  .package-vinaphone-item {
    width: 532px;
    max-width: 100%;
    padding: 24px;
    border-radius: 24px;
    gap: 24px;
    background-color: #fff;
    box-shadow: 0px 4px 20px 0px #00000026;
    justify-content: space-between;
    display: flex;
    flex-direction: column;

    &__content {
      display: flex;
      flex-direction: column;
      gap: 16px;

      &__header {
        display: flex;
        flex-direction: column;
        gap: 16px;

        &__title {
          display: flex;
          flex-direction: column;
          gap: 8px;

          &__compose {
            display: flex;
            align-items: center;
            gap: 5px;

            p{
              margin: 0;
              font-size: 16px;
              font-weight: 500;
              line-height: 24px;            
            }

            span {
              font-size: 16px;
              font-weight: 500;
              line-height: 24px; 
              color: #3a18ce;              
            }
          }

          &__price {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .duration {
              font-size: 16px;
              font-weight: 400;
              line-height: 24px;
            }

            &__title {
              display: flex;
              gap: 5px;
              align-items: baseline;

              .price {
                margin: 0;
                font-size: 24px;
                line-height: 100%;
                font-weight: 600;
              }

              .currency {
                font-size: 16px;
                font-weight: 600;
                line-height: 100%;
              }
            }
          }
        }

        &__border {
          width: 100%;
          height: 1px;
          background-color: #e8eaed;
        }
      }

      &__body {
        display: flex;
        flex-direction: column;
        gap: 24px;

        &__item {
          display: flex;
          gap: 16px;

          &__text {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
          }
        }
      }
    }

    .package-vinaphone-item__btn{
      display: flex;
      align-items: center;
      justify-content: center;

      &__deep {
        padding: 8px 24px;
        border-radius: 12px;
        background-color: #3a18ce;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        text-align: center;
      }
    }
  }
}