import { clsx } from "clsx";

import "./TagItem.scss";
const TagItem = ({ tag, onDelete, onSelect }) => {

  const generateClassName = (text = '') => {
    if (!text) return "";
    const firstCharLowerCase = text.charAt(0).toLowerCase();
    const alphabet = 'abcdefghijklmnopqrstuvwxyz'
    const index = alphabet.indexOf(firstCharLowerCase);
    const colors = ['color-1', 'color-2', 'color-3', 'color-4'];
    let className = colors[0];
    if (index !== -1) {
      className = colors[index % 4];
    };
    return className;
  }

  return (
    <div
      className={clsx("tag-item", generateClassName(tag), { "tag-item-has-delete": !!onDelete, "tag-item-has-select": !!onSelect })}
      {...onSelect && { onClick: onSelect }}
    >
      {tag}
      {!!onDelete && <div className="tag-item__delete" onClick={onDelete} />}
    </div>
  );
}

export default TagItem;