import "./PopupUploadAudio.scss";
import { useSpeaking } from "@app/pages/Student/Speaking";
import AntModal from "@component/AntModal";
import { useTranslation } from "react-i18next";
import { CloudUploadOutlined } from "@ant-design/icons";
import SpeakingAudioPlayer from "./SpeakingAudioPlayer";
import ReactDropzone from "@component/ReactDropzone";
import { useCallback, useEffect, useRef, useState } from "react";
import { CONSTANT, RECOGNIZE_STATUS } from "@constant";
import { Form } from "antd";
import { createProjectFromTool, getProjectDetail } from "@services/Project";
import { toast } from "@component/ToastProvider";
import { io } from "socket.io-client";
import { connect, useDispatch } from "react-redux";
import { cloneObj } from "@common/functionCommons";
import { ACTION_STATUS, actions, PARAM_CATEGORIES, paramsCreators, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";

const CHUNK_SIZE = 4096; // <PERSON><PERSON>ch thước khối xử lý


function PopupUploadAudio({ user, studentTools }) {
  const { t } = useTranslation();
  const { projectNameTemp, projectData } = useSpeaking();
  const { isShowUpload, setShowUpload } = useSpeaking();
  const { setAudioFileSelected } = useSpeaking();
  const [audioDuration, setAudioDuration] = useState(0);


  const [, updateState] = useState();
  const forceUpdate = useCallback(() => updateState({}), []);

  const toolId = useRef();
  const instructionId = useRef();
  const localFile = useRef();


  const [formUploadAudio] = Form.useForm();

  useEffect(() => {
    if (!isShowUpload) {
      localFile.current = null;
      forceUpdate();
    }
  }, [isShowUpload]);

  const { speakingForm, setProjectData, handleDataResponse } = useSpeaking();
  const { setRecognizeStatus } = useSpeaking();
  const {
    setRecordResult,
    setText,
    setWordAnalyzed,
    setCountErrors,
    setAudioFileId,
    setAudioUrlDownload,
  } = useSpeaking();
  const { audioInputType, setAudioInputType } = useSpeaking();
  const dispatch = useDispatch();

  const socket = useRef(null);

  async function handleSaveFile() {
    if (!localFile.current) return;
    if (audioDuration <= 0) {
      toast.error(t("audio.too_short"));
      return;
    }

    if (audioDuration >= 150) {
      toast.error('THE_AUDIO_FILE_EXCEEDS_THE_ALLOWED_CAPACITY');
      return;
    }

    const file = localFile.current;
    if (file) {
      setAudioInputType(CONSTANT.FILE);
      setRecognizeStatus(RECOGNIZE_STATUS.RECOGNIZING);
      setRecordResult([]);
      setText("");

      setWordAnalyzed([]);
      setCountErrors({});

      setAudioFileId("");
      setAudioUrlDownload("");

      const chunks = [];
      try {
        const arrayBuffer = await file.arrayBuffer();

        // Sử dụng AudioContext để giải mã dữ liệu
        const audioContext = new AudioContext({ sampleRate: 16000 });
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

        // Lấy dữ liệu từ audioBuffer và chuyển đổi sang buffer PCM
        const channelData = audioBuffer.getChannelData(0); // Lấy dữ liệu từ kênh 0 (mono)
        const pcmInt16Array = new Int16Array(channelData.length);

        // Chuyển đổi từ Float32 (-1.0 đến 1.0) sang Int16 (-32768 đến 32767)
        for (let i = 0; i < channelData.length; i++) {
          pcmInt16Array[i] = Math.max(-1, Math.min(1, channelData[i])) * 0x7FFF; // Chuyển đổi và giới hạn giá trị
        }

        // Tạo Uint8Array từ Int16Array để gửi qua socket
        const pcmUint8Array = new Uint8Array(pcmInt16Array.buffer);

        for (let i = 0; i < pcmUint8Array.length; i += CHUNK_SIZE) {
          chunks.push(pcmUint8Array.slice(i, i + CHUNK_SIZE));
        }
        dispatch(actions.trackCustomClick(TRACKING_ACTIONS.UPLOAD_STATUS, paramsCreators.uploadStatus(undefined, PARAM_CATEGORIES.UPLOAD_TOPIC, ACTION_STATUS.SUCCESS)));
      } catch (e) {
        dispatch(actions.trackCustomClick(TRACKING_ACTIONS.UPLOAD_STATUS, paramsCreators.uploadStatus(undefined, PARAM_CATEGORIES.UPLOAD_TOPIC, ACTION_STATUS.FAILED, e.message)));
        console.log("e", e);
      }

      await sendFileAsChunks(chunks);
    }
  }

  useEffect(() => {
    toolId.current = studentTools?.speaking._id;
    instructionId.current = studentTools?.speaking?.instructionIds?.[0]?._id;
  }, [studentTools]);

  async function sendFileAsChunks(chunks) {
    let projectExist = cloneObj(projectData);
    if (!projectExist) {
      const apiRequest = { toolId: toolId.current };
      if (projectNameTemp) apiRequest.projectName = projectNameTemp;
      const apiResponse = await createProjectFromTool(apiRequest);
      if (!apiResponse?._id) {
        return toast.error("AN_ERROR_OCCURRED");
      }

      const projectResponse = await getProjectDetail(apiResponse._id);
      if (projectResponse.code === 200) {
        projectExist = projectResponse?.data;
        setProjectData(projectResponse?.data);
      }
    }

    if (!projectExist) {
      return toast.error("AN_ERROR_OCCURRED");
    }

    setShowUpload(false);
    setAudioFileSelected(localFile.current);

    const topic = speakingForm.getFieldValue("topic");

    socket.current = io("/student", { transports: ["websocket"], path: "/socket" });

    // Xử lý sự kiện khi nhận tin nhắn
    socket.current.on("connect", () => {
      console.log("Connected to WebSocket server");
      // Không gửi dữ liệu ở đây, chỉ log kết nối thành công
    });

    // Chỉ gửi dữ liệu khi server đã sẵn sàng
    socket.current.on("server_ready", () => {
      console.log("Server is ready to receive audio data", topic, instructionId.current, projectExist._id);

      const inputData = {
        topic,
        instructionId: instructionId.current,
        projectId: projectExist._id,
      };

      chunks.forEach((chunk, index) => {
        socket.current.emit("audio", {
          buffer: chunk,
          inputData,
          userId: user._id,
        });

        //setUploadProgress((index + 1) / chunks.length * 100);
        if (index + 1 === chunks.length) {
          socket.current?.emit("close-recording");
        }
      });
    });

    handleDataResponse(socket.current);
  }

  function onDrop(files) {
    const file = files?.[0];

    const maxSize = 20 * 1024 * 1024;
    if (file.size >= maxSize) {
      localFile.current = null;
      forceUpdate();
      return toast.error("THE_AUDIO_FILE_EXCEEDS_THE_ALLOWED_CAPACITY");
    }


    localFile.current = files?.[0];
    forceUpdate();
  }

  useEffect(() => {
    if (audioDuration > 150) {
      return toast.error('THE_AUDIO_FILE_EXCEEDS_THE_ALLOWED_CAPACITY');
    }
  }, [audioDuration]);

  return <>
    <AntModal
      width={548}
      open={isShowUpload}
      onCancel={() => setShowUpload(false)}
      formId="form-upload-record-audio"
      okText={t("SAVE_FILE")}
      title={t("UPLOAD_FILE")}
      className="upload-record-audio-popup"
      footerAlign={CONSTANT.CENTER}
      destroyOnClose
    >
      <Form
        id="form-upload-record-audio"
        className="upload-record-audio"
        form={formUploadAudio}
        onFinish={handleSaveFile}
      >

        <ReactDropzone
          onDrop={onDrop}
          accept={{
            "audio/mpeg": [".mp3"],
            "audio/wav": [".wav"],
            "audio/ogg": [".ogg"],
            "audio/mp4": [".m4a"],
          }}
        >
          <div className="upload-record-audio__select-audio">
            <div className="select-audio-image">
              <CloudUploadOutlined style={{ color: "#2196F3" }}/>
            </div>
            <div className="select-audio-text">
              {t("UPLOAD_AUDIO")}
            </div>
          </div>
        </ReactDropzone>


        <div className="upload-record-audio__notice">

          {t("UPLOAD_RECORD_AUDIO_NOTICE")}
        </div>
        {localFile.current && <>
          <div className="upload-record-audio__title">
            {t("UPLOADED_FILE")}
          </div>

          <SpeakingAudioPlayer audioFile={localFile.current} onDurationChange={setAudioDuration}/>


        </>}
      </Form>
    </AntModal>
  </>;
}


function mapStateToProps(store) {
  const { user } = store.auth;
  const { studentTools } = store.tool;
  return { user, studentTools };
}

export default connect(mapStateToProps)(PopupUploadAudio);
