import React from "react";
import { Button, Input, Layout, Typography, Upload } from "antd";
import { InboxOutlined } from "@ant-design/icons";
import "./AudioTranscriptEditor.scss";
import { API } from "@api";
import { useTranslation } from "react-i18next";

const { Dragger } = Upload;
const { TextArea } = Input;

const AudioTranscriptEditor = (props) => {
  const { t } = useTranslation();
  const { audioId } = props;
  const { exerciseData } = props;
  
  return (
    <div className="content-wrapper">
      <Typography.Title level={4}>{t("UPLOAD_AUDIO")}</Typography.Title>
      <Dragger
        name="audio"
        action={API.DICTATION_AND_SHADOWING_UPLOAD_AUDIO}
        accept="audio/*"
        maxCount={1}
        onChange={props.onDropAudio}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined/>
        </p>
        <p className="ant-upload-text">Click hoặc kéo file audio vào đây để upload</p>
        <p className="ant-upload-hint">Chỉ hỗ trợ file audio (mp3, wav, ogg, ...)</p>
      </Dragger>
      {audioId && (
        <div className="title">
          <Typography.Title level={4}>{t("AUDIO")}</Typography.Title>
          <audio src={API.STREAM_MEDIA.format(audioId)} controls style={{ width: "100%" }}/>
        </div>
      )}
      
      <Typography.Title level={4} className="title">Transcript</Typography.Title>
      <TextArea
        value={exerciseData.transcript}
        onChange={props.handleTranscriptChange}
        placeholder="Transcript sẽ hiển thị ở đây sau khi upload audio hoặc bạn có thể tự nhập/chỉnh sửa"
        autoSize={{ minRows: 2, maxRows: 10 }}
      />
      <div className="button-wrapper">
        <Button type="primary" onClick={props.updateSegmentsFromTranscript}>
          {t("UPDATE_SEGMENTS_FROM_TRANSCRIPT")}
        </Button>
        <Button type="primary" onClick={props.handleCreateNewAudio}>
          {t("CREATE_NEW_AUDIO_FROM_TRANSCRIPT")}
        </Button>
      </div>
    </div>
  );
};

export default AudioTranscriptEditor;