import React from "react";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import QRCode from "react-qr-code";

import { CONSTANT } from "@constant";

import "./QrCodeDownload.scss";

function QrCodeDownload({ type = CONSTANT.VIDEO, link }) {
  const { t } = useTranslation();
  
  return <div className="qr-code-container">
    <div className="qr-code__qr">
      <QRCode value={link} size={80} />
    </div>
    <div className="qr-code__title">
      {t("QR_CODE")}
    </div>
    <div className="qr-code__description">
      {t(type === CONSTANT.VIDEO
        ? "SCAN_QR_CODE_TO_DOWNLOAD_VIDEO"
        : type === CONSTANT.AUDIO ? "SCAN_QR_CODE_TO_DOWNLOAD_AUDIO" : "")}
    </div>
  </div>;
}

QrCodeDownload.propTypes = {
  type: PropTypes.string,
};

export default QrCodeDownload;