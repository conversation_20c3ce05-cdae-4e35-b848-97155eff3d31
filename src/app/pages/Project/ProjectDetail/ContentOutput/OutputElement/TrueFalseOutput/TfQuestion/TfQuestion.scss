.true-false-output__question-container {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .true-false-output__question-item {
    display: flex;
    flex-direction: column;
    page-break-inside: avoid;
    gap: 8px;

    .true-false-output__question-text {
      display: flex;
      font-weight: 400;
      line-height: 24px;

      .true-false-output__question-index {
        flex-shrink: 0;
        display: flex;

        width: 24px;
        height: 24px;
        background: var(--primary-colours-blue-navy);
        color: #FFFFFF;
        border-radius: 50%;
        justify-content: center;
        line-height: 24px;
        margin-right: 8px;
      }
    }
  }
}