import React from "react";

import "./TfAnswer.scss";

function TfAnswer({ correctAnswers }) {
  
  return <div className="true-false-output__answer-list">
    {correctAnswers?.map(correctAnswer => {
      return <div key={correctAnswer.questionId} className="true-false-output__answer-item">
        <div className="true-false-output__answer-index">
          <span className={".js-index-question-margin"}>{correctAnswer.questionId}</span>
        </div>
        <div className="true-false-output__answer-text">
          {correctAnswer.correctAnswer}{correctAnswer.correctAnswer.toLowerCase() === "false" ? ` -- ${correctAnswer.answerExplain}` : ""}
        </div>
      </div>;
    })}
  </div>;
}

export default TfAnswer;