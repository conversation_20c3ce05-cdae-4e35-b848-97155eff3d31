@import "src/app/styles/scroll";

.resource__video-type {
  .ant-tabs-nav {
    margin: -12px 0 12px 0;

    &:before {
      display: none;
    }

    .ant-tabs-nav-wrap {
      .ant-tabs-nav-list {
        gap: 24px;

        .ant-tabs-tab {
          color: var(--typo-colours-support-blue-light);
          margin: 0;
        }

        .ant-tabs-ink-bar {
          display: none;
        }
      }
    }
  }
}

.upload-resource__progress-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;


  .upload-resource__progress-list {
    @extend .scrollbar;
    @extend .scrollbar-show;

    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: calc(100vh - 400px);

    //.upload-resource__progress-label {
    //  font-weight: 600;
    //}
  }

  .upload-resource__capacity {
    display: flex;
    justify-content: space-between;
    padding-right: 48px;

    .upload-resource__capacity-label {
      font-weight: 700;
    }

    .upload-resource__capacity-value {
      color: var(--support-colours-green-dark);


      &.upload-resource__capacity-exceeded {
        color: var(--support-colours-yellow);
      }
    }
  }

  .upload-resource__progress-action {
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-self: center;
  }
}

#youtube-link-form {

  .youtube-link__list {

    .youtube-link__item {
      display: flex;
      flex-direction: row;
      gap: 16px;

      .youtube-link__item-input {
        flex: 1;

        .ant-input {
          height: 48px;
        }
      }

      .youtube-link__item-remove {
        margin-top: 8px;
        box-shadow: var(--shadow-level-2);
      }

    }
  }

  .youtube-link__item-add {
    display: flex;
    justify-content: center;

    .ant-btn {
      box-shadow: var(--shadow-level-2);

      .ant-btn-icon svg {
        width: 20px;
        height: 20px;
      }
    }
  }

  .youtube-link__submit {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    gap: 16px;
  }
}