import React, { useEffect, useState } from "react";
import "./TotalTokenByUser.scss";
import { Card, Col, Form, Row, Statistic, Table } from "antd";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { handleSearchParams } from "@common/functionCommons";
import { getTokenInstructionByUser } from "@services/Dashboard";
import { connect } from "react-redux";
import Loading from "@component/Loading";
import { DollarOutlined } from "@ant-design/icons";

TotalTokenByUser.propTypes = {};

function TotalTokenByUser({ user }) {
  const { t } = useTranslation();
  const [formFilter] = Form.useForm();
  const location = useLocation();
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [tokenData, setTokenData] = useState([]);
  const [totalStats, setTotalStats] = useState({
    totalTokens: 0,
    promptTokens: 0,
    completionTokens: 0,
    numberOfSubmissions: 0,
    totalCost: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    getTokenDataFromAPI();
  }, [location.search]);
  
  
  const handlePaginationChange = (page) => {
    setLimit(page.pageSize);
    setPage(page.current);
  };
  
  const getTokenDataFromAPI = async () => {
    setIsLoading(true);
    const { query } = handleSearchParams(location.search);
    const apiResponse = await getTokenInstructionByUser(query);
    if (apiResponse) {
      setTokenData(apiResponse);
    }
    setIsLoading(false);
    formFilter.setFieldsValue({
      userId: query?.userId || user?._id,
      fullName: query?.userId || user?._id,
    });
  };
  
  
  function formatCurrency(currentcy) {
    const US = new Intl.NumberFormat("en-US", {
      currency: "USD",
      maximumFractionDigits: 6,
    });
    return US.format(currentcy);
  }
  
  const columns = [
    {
      title: "Order",
      dataIndex: "order",
      align: "center",
      render: (value, record, index) => (page - 1) * limit + (index + 1),
      width: 100,
    },
    {
      title: "Instruction",
      dataIndex: "shortName",
      width: 400,
    },
    {
      title: "GPT Model",
      dataIndex: "gptModel",
      key: "gptModel",
      width: 200,
    },
    {
      title: "Input token",
      dataIndex: "promptTokens",
      align: "center",
      width: 200,
    },
    {
      title: "Output token",
      dataIndex: "completionTokens",
      align: "center",
      width: 200,
    },
    {
      title: "Total token",
      dataIndex: "totalTokens",
      align: "center",
      width: 200,
    },
    {
      title: "Cost",
      dataIndex: "totalCost",
      align: "center",
      render: (value) => <>${formatCurrency(value)}</>,
      width: 200,
    },
  ];
  
  return (
    <div className="token-user-page-container">
      <span className="token-user-title">Statistics token by instructions</span>
      {isLoading && <Loading active transparent/>}
      {!!tokenData?.length && !isLoading && (
        <Table
          columns={columns}
          pagination={{
            current: page,
            pageSize: limit,
            pageSizeOptions: ["1", "10", "20", "50"],
            showSizeChanger: true,
          }}
          dataSource={tokenData}
          scroll={{ x: 1000 }}
          onChange={handlePaginationChange}
        />
      )}
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(TotalTokenByUser);
