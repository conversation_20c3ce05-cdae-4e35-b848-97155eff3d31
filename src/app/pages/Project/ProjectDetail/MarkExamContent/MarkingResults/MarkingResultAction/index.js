import React from "react";

import SaveOutput from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content/ContentFooter/SaveOutput";
import { CONSTANT } from "@constant";
import OutputProcessing
  from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content/ContentFooter/OutputProcessing";
import OutputAction from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content/ContentFooter/OutputAction";
import OutputError from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content/ContentFooter/OutputError";
import { useMarkExam } from "@app/pages/Project/ProjectDetail/MarkExamContent";
import { confirm } from "@component/ConfirmProvider";
import { useTranslation } from "react-i18next";
import { useProject } from "@app/pages/Project";
import HtmlOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/HtmlOutput";
import { retryResponse } from "@services/Response";
import { cloneObj } from "@common/functionCommons";
import MarkTestWritingOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/MarkTestWritingOutput";


function MarkingResultAction({
                               formResult,
                               isEditing, setEditing,
                               toolInfo, instructionSelected,
                             }) {
  
  const { t } = useTranslation();
  
  const { projectId, projectData, setProjectContentData } = useProject();
  const { handleDeleteResponse, handleCancelRetryResponse } = useProject();
  const { contentData, resultSelected } = useMarkExam();
  
  const outputState = resultSelected?.state?.toUpperCase();
  
  async function handleRegenerate(values) {
    const additionalRequest = values?.additionalRequest;
    const apiRequest = { _id: resultSelected._id, projectId, workspaceId: projectData?.workspaceId };
    if (additionalRequest) {
      apiRequest.additionalRequest = additionalRequest;
    }
    const apiResponse = await retryResponse(apiRequest);
    if (apiResponse) {
      await setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState.forEach(state => {
          if (state._id === contentData._id) {
            state.responses = state.responses?.map(response => {
              return response._id === apiResponse._id ? { ...apiResponse, typingEffect: true } : response;
            });
          }
        });
        return newState;
      });
    }
  }
  
  function cancelResponseProcessing() {
    if (resultSelected.hasOwnProperty("isNewResponse") && resultSelected.isNewResponse) {
      deleteOutput();
    } else {
      handleCancelRetryResponse(resultSelected._id);
    }
  }
  
  function deleteOutput() {
    confirm.delete({
      content: t("ARE_YOU_SURE_YOU_WANT_TO_DELETE_THE_TOOL_RESULT?"),
      handleConfirm: () => {
        handleDeleteResponse(contentData._id, resultSelected._id);
      },
    });
  }
  
  const contentFooterProps = {
    deleteOutput,
    cancelResponseProcessing,
    handleRegenerate,
    responseSelected: resultSelected,
  };
  
  if (outputState === CONSTANT.PROCESSING) {
    return <OutputProcessing {...contentFooterProps} />;
  } else if (outputState === CONSTANT.DONE) {
    return <>
      <MarkTestWritingOutput
        isEditing={isEditing}
        setEditing={setEditing}
        responseSelected={resultSelected}
        content={contentData}
        outputForm={formResult}
      />
      
      {isEditing
        ? <SaveOutput outputForm={formResult} setEditing={setEditing} />
        : <OutputAction
          showNextTool={false}
          allowSaveGuide={false}
          content={contentData}
          toolInfo={toolInfo}
          setEditing={setEditing}
          instructionSelected={instructionSelected}
          {...contentFooterProps}
        />}
    </>;
  } else if (outputState === CONSTANT.ERROR) {
    return <OutputError {...contentFooterProps} />;
  }
  
  return <></>;
  
}

export default MarkingResultAction;