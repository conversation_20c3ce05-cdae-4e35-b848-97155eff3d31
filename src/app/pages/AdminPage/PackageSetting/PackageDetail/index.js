import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import {useNavigate, useParams} from "react-router-dom";
import {Col, Divider, Form, Input, InputNumber, Row, Select} from "antd";

import AntButton from "@src/app/component/AntButton";

import {createPackage, getPackageDetail, updatePackage} from "@src/app/services/Package";
import {getAllFeature} from "@src/app/services/Feature";

import {toast} from "@src/app/component/ToastProvider";

import {
  BUTTON,
  CONSTANT,
  CURRENCY_TYPE,
  PACKAGE_TYPE,
  PRICE_TYPE,
  PACKAGE_CUSTOMER_TARGET,
} from "@src/constants/constant";
import {LINK} from "@link";

import CancelIcon from "@component/SvgIcons/CancelIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";

import "./PackageDetail.scss";
import RULE from "@rule";


const PackageDetail = ({...props}) => {
  const {t} = useTranslation();
  const {id: packageId} = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const [allFeatures, setAllfeatures] = useState([]);
  const [usingFeatures, setUsingFeatures] = useState([]);
  const [packageType, setPackageType] = useState(null);

  useEffect(() => {
    getAllData();
  }, []);

  const getAllData = async () => {
    const [featureResponse, packageResponse] = await Promise.all([
      getAllFeature(),
      packageId ? getPackageDetail(packageId) : null,
    ]);
    if (featureResponse) {
      setAllfeatures(featureResponse);
    }
    if (packageResponse && featureResponse) {
      let features = [];
      if (packageResponse?.features) {
        features = Object.entries(packageResponse?.features || {}).map(([key, value]) => {
          const feature = featureResponse.find((feature) => feature._id === key);
          return {_id: feature._id, value: value, type: feature.type, unit: feature.unit};
        });
      }
      setUsingFeatures(features);
      setPackageType(packageResponse?.type);
      form.setFieldsValue({...packageResponse, features});
    }
  };


  const onSelectFeature = (feature, index) => {
    const {_id, type, unit} = feature;
    const updatedFeatures = [...usingFeatures];
    updatedFeatures[index] = {_id, type, unit};
    setUsingFeatures(updatedFeatures);
  };

  const onRemoveFeature = (index, name, remove) => {
    remove(index, name);
    const updatedFeatures = [...usingFeatures].filter((_, i) => i !== index);
    setUsingFeatures(updatedFeatures);
  };

  const onAddFeature = (add) => {
    add();
    setUsingFeatures((pre) => [...pre, {}]);
  };

  const getAvailableFeatures = (selectedIndex) => {
    if (!packageType) {
      return [];
    }
    const selectedFeatureIds = usingFeatures?.filter((_, index) => index !== selectedIndex)?.map(({_id}) => _id);
    return allFeatures?.filter((feature) => {
      // Return all features if packageType is 'vnpt', otherwise filter by packageType
      return !selectedFeatureIds.includes(feature._id) && (packageType === 'vnpt' || feature?.packageType === packageType);
    });
  };


  const onFinish = async (values) => {
    const featureObj = values?.features?.reduce((data, {_id, value}) => ({...data, [_id]: value}), {});
    const requestData = {...values, features: featureObj};
    const response = packageId
      ? await updatePackage({
        ...requestData,
        _id: packageId,
      })
      : await createPackage(requestData);
    if (response) {
      toast.success(packageId ? "UPDATE_PACKAGE_SUCCESS" : "CREATE_PACKAGE_SUCCESS");
      if (!packageId) {
        navigate(LINK.PACKAGE_ID.format(response?._id));
      }
    }
  };

  const handleCancel = () => {
    navigate(LINK.PACKAGE);
  };

  const renderFeatureInput = (restField, name, index) => {
    const {unit, type} = usingFeatures[index];
    let inputElement;
    switch (type) {
      case "Boolean":
        inputElement = (
          <Select
            placeholder="Value of feature"
            options={[
              {label: "True", value: true},
              {label: "False", value: false},
            ]}
          />
        );
        break;
      case "Number":
      case "String":
        inputElement = <Input placeholder="Value of feature" addonAfter={unit}/>;
        break;
      default:
        return null;
    }

    return (
      <Col xs={24} lg={12}>
        <Form.Item {...restField} name={[name, "value"]} rules={[{required: true, message: "Missing value!"}]}>
          {inputElement}
        </Form.Item>
      </Col>
    );
  };

  const showAddFeature = packageType === PACKAGE_TYPE.ADDON.value ? !usingFeatures?.length || usingFeatures?.length < 1 : !usingFeatures?.length || usingFeatures?.length < allFeatures?.length;

  const handleSetPackageType = (value) => {
    if (value !== packageType) {
      setPackageType(value);
      setUsingFeatures([]);
      form.setFieldsValue({features: []});
      if (value === PACKAGE_TYPE.ADDON.value) {
        form.validateFields([["prices", 0, "unitName"], ["prices", 1, "unitName"]]);
      }
    }
  };
  return (
    <div className="package-detail">
      <div className="package-detail__title">{packageId ? t("PACKAGE_DETAIL") : t("CREATE_PACKAGE")}</div>
      <Form
        onFinish={onFinish}
        layout="vertical"
        form={form}
        initialValues={{prices: [{currency: null}]}}
        size={"large"}
        className={"form-package-details"}
      >
        <Row gutter={24}>
          <Col sm={24} xs={12} lg={6}>
            <Form.Item label={t("NAME")} name="name" rules={[{required: true, message: "Name can't be blank!"}]}>
              <Input placeholder={"Enter name package"}/>
            </Form.Item>
          </Col>
          <Col sm={24} xs={12} lg={6}>
            <Form.Item label={t("CODE")} name="code" rules={[{required: true, message: "Code can't be blank!"}]}>
              <Input placeholder={"Enter name code"}/>
            </Form.Item>
          </Col>
          <Col sm={24} xs={12} lg={6}>
            <Form.Item label={t("DESCRIPTION")} name="description">
              <Input.TextArea autoSize={{minRows: 1}} placeholder={"Enter description"}/>
            </Form.Item>
          </Col>
          <Col sm={24} xs={12} lg={6}>
            <Form.Item label={t("ORDER")} name="order">
              <InputNumber min={1} formatter={(value) => Math.round(value)} placeholder={"Order"}/>
            </Form.Item>
          </Col>
          <Col sm={24} xs={12} lg={6}>
            <Form.Item label={t("TYPE")} name="type" rules={[{required: true, message: "Type can't be blank!"}]}>
              <Select options={Object.values(PACKAGE_TYPE)} placeholder="Select package type" value={packageType}
                      onChange={handleSetPackageType}/>
            </Form.Item>
          </Col>
          <Col sm={24} xs={12} lg={6}>
            <Form.Item label={t("CUSTOMER")} name="customerTarget"
                       rules={[{required: true, message: "Type can't be blank!"}]}>
              <Select options={Object.values(PACKAGE_CUSTOMER_TARGET)} placeholder="Select customer target"/>
            </Form.Item>
          </Col>
          {packageType === 'vnpt' && (
            <Col sm={24} xs={12} lg={12}>
              <Form.Item label="VNPT Data" name="vnptData">
                <Input placeholder="Enter VNPT data"/>
              </Form.Item>
            </Col>
          )}
        </Row>
        <Divider/>
        <Form.Item label={<>{t("PRICE")} <span className={"required-color"}> * </span></>}>
          <Form.List name="prices">
            {(fields, {add, remove}) => (
              <>
                {fields.map(({key, name, ...restField}) => (
                  <div className={"row-price-unit"} key={key}>
                    <Row gutter={24} className="baseline-row">
                      <Col lg={6} md={12} sm={24}>
                        <Form.Item
                          {...restField}
                          name={[name, "unitName"]}
                          rules={[
                            () => ({
                              validator(_, value) {
                                if (packageType !== PACKAGE_TYPE.BASE.value || value) {
                                  return Promise.resolve();
                                }
                                return Promise.reject(new Error("Unit name can't be blank!"));
                              },
                            }),
                          ]}
                        >
                          <Select
                            options={PRICE_TYPE}
                            placeholder="Select unit name"
                            {...packageType !== PACKAGE_TYPE.BASE.value ? {allowClear: true} : {}}
                          />
                        </Form.Item>
                      </Col>
                      <Col lg={6} md={12} sm={24}>
                        <Form.Item
                          {...restField}
                          name={[name, "currency"]}
                          rules={[{required: true, message: "Currency can't be blank!"}]}
                        >
                          <Select options={CURRENCY_TYPE} placeholder="Select currency"/>
                        </Form.Item>
                      </Col>
                      <Col lg={6} md={12} sm={24}>
                        <Form.Item
                          {...restField}
                          name={[name, "intervalCount"]}
                          rules={[{required: true, message: "Interval count can't be blank!"}]}
                        >
                          <Input placeholder="Interval count"/>
                        </Form.Item>
                      </Col>
                      <Col lg={6} md={12} sm={24}>
                        <Form.Item
                          {...restField}
                          name={[name, "unitAmount"]}
                          rules={[{required: true, message: "Unit amount can't be blank!"}]}
                        >
                          <Input placeholder="Unit amount"/>
                        </Form.Item>
                      </Col>
                    </Row>
                    <div className={"price-action"}>
                      <AntButton
                        type={BUTTON.WHITE}
                        shape={"circle"}
                        className={"btn-cancel-add-price"}
                        icon={<CancelIcon/>}
                        size={"small"}
                        onClick={() => remove(name)}
                      />
                    </div>
                  </div>
                ))}
                <div className={"add-price-actions"}>
                  <AntButton
                    shape={"circle"}
                    size={"large"}
                    type={BUTTON.WHITE_BLUE}
                    onClick={() => add()}
                    icon={<PlusIcon/>}
                  ></AntButton>
                </div>
              </>
            )}
          </Form.List>
        </Form.Item>
        {packageType && (
          <Form.Item label={<>{t("FEATURES")} <span className={"required-color"}> * </span></>}>
            <Form.List name="features">
              {(fields, {add, remove}) => {
                return (
                  <>
                    <div className={"package-detail-feature-list"}>
                      {fields.map(({key, name, ...restField}, index) => (
                        <div className={"package-detail-feature-list-item"} key={key}>
                          <Row gutter={24} className="baseline-row">
                            <Col xs={24} lg={12}>
                              <Form.Item
                                {...restField}
                                name={[name, "_id"]}
                                rules={[{required: true, message: "Missing feature!"}]}
                              >
                                <Select
                                  options={getAvailableFeatures(index)}
                                  placeholder="Select feature"
                                  fieldNames={{label: "name", value: "_id"}}
                                  onSelect={(_, option) => onSelectFeature(option, index)}
                                />
                              </Form.Item>
                            </Col>
                            {renderFeatureInput(restField, name, index)}
                          </Row>
                          <div className={"price-action"}>
                            <AntButton
                              shape={"circle"}
                              className={"btn-remove-feature"}
                              size={"small"}
                              type={BUTTON.WHITE}
                              onClick={() => onRemoveFeature(index, name, remove)}
                              icon={<CancelIcon/>}
                            ></AntButton>
                          </div>
                        </div>
                      ))}
                    </div>
                    {showAddFeature && (
                      <div className={"add-price-actions"}>
                        <AntButton
                          shape={"circle"}
                          size={"large"}
                          type={BUTTON.WHITE_BLUE}
                          onClick={() => onAddFeature(add)}
                          icon={<PlusIcon/>}
                        ></AntButton>
                      </div>
                    )}
                  </>
                );
              }}
            </Form.List>
          </Form.Item>)}

        <Row gutter={20} justify="center" className="gap-4">
          <Form.Item className="save-button">
            <AntButton onClick={handleCancel} type={BUTTON.WHITE}>
              {t("BACK")}
            </AntButton>
          </Form.Item>
          <Form.Item className="save-button">
            <AntButton htmlType="submit" type={BUTTON.DEEP_NAVY}>
              {t("SAVE")}
            </AntButton>
          </Form.Item>
        </Row>
      </Form>
    </div>
  )
    ;
};

export default PackageDetail;
