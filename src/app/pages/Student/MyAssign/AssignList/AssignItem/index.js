import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { Dropdown } from "antd";

import AntButton from "@component/AntButton";

import { LINK } from "@link";
import { CONSTANT } from "@constant";
import { formatDate, formatTimeDate } from "@common/functionCommons";
import { calculateIELTSOverall } from "@src/common/functionCommons";

import MoreVertical20 from "@component/SvgIcons/MoreVertical/MoreVertical20";
import ShareIcon from "@component/SvgIcons/ShareIcon";
import Trash from "@component/SvgIcons/Trash";

import "./AssignItem.scss";
import { useDispatch } from "react-redux";
import { actions, PARAM_CATEGORIES, PARAM_TYPES, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";


function AssignItem({ studentProjectType, assignData, onDeleteAssign, onShareAssign }) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  const [inputData, outputData] = useMemo(() => {
    const response = assignData?.content?.response;
    return [response?.inputId?.inputData, response?.output];
    
  }, [assignData]);
  
  
  const linkTo = useMemo(() => {
    const link = studentProjectType === CONSTANT.SPEAKING ? LINK.SPEAKING : LINK.WRITING_RESULT;
    return link.format(assignData?._id);
  }, [assignData, studentProjectType]);
  
  const generateWritingScore = (outputData) => {
    if (!outputData?.overallBandScore?.length) return 0;
    const avg = calculateIELTSOverall(outputData.overallBandScore.map(item => item.score));
    return avg;
  };
  
  const score = useMemo(() => {
    if (studentProjectType === CONSTANT.SPEAKING) return outputData?.score;
    else return generateWritingScore(outputData);
  }, [outputData, studentProjectType]);
  
  return <Link
    to={linkTo}
    state={{ projectId: assignData?._id }}
    className="assign-item-container"
    onClick={() => dispatch(actions.trackCustomClickType(TRACKING_ACTIONS.CLICK_CARD, studentProjectType === CONSTANT.SPEAKING ? PARAM_TYPES.CARD_SPEECH : PARAM_TYPES.CARD_ESSAY))}
  >
    <div className="assign-item__header">
      
      <div className="assign-item__time">
        {t("CREATED_AT")}: {formatDate(assignData.createdAt)} | {t("UPDATED_AT")}: {formatTimeDate(assignData.updatedAt, false)}
      </div>
      <div id={`js-language-header-${assignData._id}`} className="assign-item__action">
        <Dropdown
          menu={{
            items: [
              { key: "SHARE", label: t("SHARE"), icon: <ShareIcon />, onClick: () => onShareAssign(assignData?._id) },
              { key: "DELETE", label: t("DELETE"), icon: <Trash />, onClick: () => onDeleteAssign(assignData?._id) },
            ],
            onClick: ({ domEvent }) => {
              domEvent.preventDefault();
              domEvent.stopPropagation();
            },
          }}
          trigger={["click"]}
          onClick={e => {
            e.stopPropagation();
            e.preventDefault();
          }}
          getPopupContainer={() => document.getElementById(`js-language-header-${assignData._id}`)}
        >
          <AntButton
            size="xsmall"
            icon={<MoreVertical20 />}
            onClick={e => {
              e.stopPropagation();
              e.preventDefault();
            }}
          />
        </Dropdown>
      
      </div>
    
    </div>
    <div className="assign-item__body">
      {assignData.projectName}
    </div>
    <div className="assign-item__footer">
      <div className="assign-item__tags">
      </div>
      <div className="assign-item__score">
        {!!score ? `${t("SCORE")}: ${score}` : ""}
      </div>
    </div>
  </Link>;
}

export default AssignItem;