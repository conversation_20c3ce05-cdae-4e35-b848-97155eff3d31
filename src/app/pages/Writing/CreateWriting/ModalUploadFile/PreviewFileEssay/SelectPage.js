import { useEffect } from "react";
import { Form, InputNumber } from "antd";
import { useTranslation } from "react-i18next";

import DANGER_ICON from "@src/asset/icon/danger/danger.svg";

const SelectPage = (props) => {
  const { t } = useTranslation();
  const { pageRange, setPageRange, validatePageRange } = props;

  const [selectPageForm] = Form.useForm();


  useEffect(() => {
    const { startPage, endPage } = pageRange;
    if (startPage && endPage) {
      selectPageForm.setFieldsValue(pageRange);
    }
  }, [pageRange])

  const onValuesChange = (changedValues, allValues) => {
    setPageRange(allValues);
  };

  return <div className="writing-preview-pdf__select-page">
    <Form
      id="form-select-page"
      name="form-select-page"
      layout="horizontal"
      form={selectPageForm}
      onValuesChange={onValuesChange}
    >
      <Form.Item label={t("SELECT_PAGE_PDF")} name="startPage" layout="horizontal">
        <InputNumber
          size="large"
          controls={false}
          changeOnBlur
        />
      </Form.Item>
      <span className="form-select-page__dash"/>
      <Form.Item name="endPage">
        <InputNumber
          size="large"
          controls={false}
          changeOnBlur
        />
      </Form.Item>
    </Form>
    {validatePageRange?.isError && <div className="form-select-page__error">
      <img src={DANGER_ICON} alt="" />
      {validatePageRange?.errorMsg}
    </div>}
  </div>;
}

export default SelectPage;