.speaking-errors {
  margin: 8px 0;
  box-shadow: 0 4px 20px 0 #0000001A;
  border-radius: 24px;
  padding: 8px 16px;

  display: flex;
  flex-direction: column;
  gap: 8px;

  .speaking-errors-item {
    display: flex;
    //gap: 4px;

    .speaking-error__count {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      align-content: center;
      text-align: center;
    }

    .speaking-error__name {
      align-content: center;
      font-size: 12px;
      width: 123px;
      margin-left: 9px;
    }

    .speaking-error__info-icon {
      display: flex;
      align-items: center;
      margin-left: 4px;
    }

    .speaking-error__switch {
      align-content: center;
      text-align: center;
      margin-left: 8px;

      .ant-switch {
        background: #F1F1F1;


        .ant-switch-handle:before {
          background-color: #B3B3B3;
        }

        &.ant-switch-checked {
          background: #E7E5FF;

          .ant-switch-handle:before {
            background-color: #3A18CE;
          }
        }
      }

      .speaking-error__switch-status {
        font-size: 13px;
        margin-left: 4px;
        width: 26px;
        display: inline-block;
      }
    }
  }
}

.speaking-error__info-tooltip{
  .ant-tooltip-inner{
    background-color: #2E2A2ACC;
    border-radius: 4px;
  }

  .ant-tooltip-arrow{
    &::before{
      background-color: #2E2A2ACC;
    }
  }
}