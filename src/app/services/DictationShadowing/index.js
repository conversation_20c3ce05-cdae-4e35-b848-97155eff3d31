import {API} from '@api';
import {getAllBase, getAllPaginationBaseList, getBase, getDetailBaseWithParams, postBase} from '../Base';

/**
 * Fetch all published dictation shadowing exercises
 * @param {Object} query - Query parameters for filtering
 * @param {Object} paging - Paging parameters for filtering
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the exercises data
 */
export const getAllPublishedExercises = (query = {}, paging = {page:1, limit:12}, populateOpts = [], loading = true) => {
  return getAllPaginationBaseList(API.DICTATION_SHADOWING_EXERCISES_ALL_PUBLISHED, paging, query, populateOpts, loading);
};

/**
 * Fetch details for a specific dictation shadowing exercise
 * @param {String} id - The ID of the exercise
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the exercise details
 */
export const getExerciseDetails = (id, dictationMode, populateOpts = [], loading = true, toastError = false) => {
  return getDetailBaseWithParams(
    API.DICTATION_SHADOWING_EXERCISE_DETAILS,
    id,
    {mode: dictationMode, exerciseType: "dictation"},
    populateOpts,
    loading,
    toastError,
  );
};

/**
 * Fetch details for a specific dictation shadowing exercise
 * @param {String} id - The ID of the exercise
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the exercise details
 */
export const getShadowingExerciseDetails = (id, populateOpts = [], loading = true, toastError = false) => {
  return getDetailBaseWithParams(
    API.DICTATION_SHADOWING_EXERCISE_DETAILS,
    id,
    {exerciseType: "shadowing"},
    populateOpts,
    loading,
    toastError,
  );
};

/**
 * Submit student answer for dictation exercise checking
 * @param {Object} data - The answer data with segment, mode, studentAnswer, and exerciseId
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the result data with score and feedback
 */
export const checkDictationAnswer = (data, loading = true, toastError = true) => {
  return postBase(API.CHECK_DICTATION_ANSWER, data, loading, toastError);
};

/**
 * Submit student answer for dictation exercise checking
 * @param {Object} data - The answer data with segment, mode, studentAnswer, and exerciseId
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the result data with score and feedback
 */
export const resetAnswerDictation = (data, loading = true, toastError = true) => {
  return postBase(API.RESET_DICTATION_ANSWER, data, loading, toastError);
};
