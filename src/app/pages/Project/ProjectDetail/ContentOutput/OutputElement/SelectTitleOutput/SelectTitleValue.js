import React from "react";
import { useTranslation } from "react-i18next";

import TypingEffect from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TypingEffect";
import Answer from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/Answer";
import OptionAnswer from "./OptionAnswer";
import OptionQuestion from "./OptionQuestion";

function SelectTitleValue({ responseSelected, ...props }) {
  const { t } = useTranslation();
  const outputData = responseSelected.output;
  
  const correctOptionText = outputData.options?.find(option => option.optionId === outputData.correctOptionId)?.text;
  
  const selectTitleHtml = <div className="option-list-output">
    
    <div className="project-content-output__title">
      {t("QUESTION")}
    </div>
    
    <OptionQuestion options={outputData?.options}/>
    
    {!!correctOptionText && <Answer>
      <OptionAnswer
        correctOptionId={outputData.correctOptionId}
        correctOptionText={correctOptionText}
      />
    </Answer>}
  </div>;
  
  if (responseSelected.typingEffect)
    return <TypingEffect html={selectTitleHtml} />;
  return selectTitleHtml;
}

export default SelectTitleValue;