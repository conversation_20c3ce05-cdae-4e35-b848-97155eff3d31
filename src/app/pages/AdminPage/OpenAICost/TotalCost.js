import React, { useMemo } from "react";
import { Card, Col, Row, Statistic, Tooltip, Empty } from "antd";
import { useTranslation } from "react-i18next";
import {
  DollarOutlined,
  FileImageOutlined,
  SoundOutlined,
  FileTextOutlined,
  NumberOutlined,
  UserOutlined,
  RiseOutlined
} from "@ant-design/icons";

import Loading from "@src/app/component/Loading";

const TotalCost = ({ activeTab, totalCostData, isLoading }) => {
  const { t } = useTranslation();


  // Removed pieChartData

  const totalCost = useMemo(() => {
    return +((
      (totalCostData.mediaCost?.totalCost || 0) +
      (totalCostData.textCost?.totalCost || 0) +
      (totalCostData.imageCost?.totalCost || 0) +
      (totalCostData.shadowingCost?.totalCost || 0)
    ).toFixed(5));
  }, [totalCostData]);

  const totalSubmits = useMemo(() => {
    return (
      (totalCostData.mediaCost?.totalSubmits || 0) +
      (totalCostData.textCost?.totalSubmits || 0) +
      (totalCostData.imageCost?.totalSubmits || 0) +
      (totalCostData.shadowingCost?.totalSubmits || 0)
    );
  }, [totalCostData]);

  // Calculate percentages for each cost type
  const costPercentages = useMemo(() => {
    if (totalCost === 0) return { media: 0, text: 0, image: 0, shadowing: 0 };

    return {
      media: ((totalCostData.mediaCost?.totalCost || 0) / totalCost * 100).toFixed(1),
      text: ((totalCostData.textCost?.totalCost || 0) / totalCost * 100).toFixed(1),
      image: ((totalCostData.imageCost?.totalCost || 0) / totalCost * 100).toFixed(1),
      shadowing: ((totalCostData.shadowingCost?.totalCost || 0) / totalCost * 100).toFixed(1)
    };
  }, [totalCostData, totalCost]);

  if (activeTab !== "1") return null;
  return (
    <div className="total-cost-container">
      <Card className="total-cost-summary-card">
        <Loading active={isLoading} transparent>
          {totalCost === 0 && !isLoading ? (
            <Empty description={t("NO_COST_DATA_FOUND")} />
          ) : (
            <>
              <h2 className="total-cost-title">{t("COST_OVERVIEW")}</h2>
              <Row gutter={[24, 24]} className="total-cost-summary-row">
                <Col xs={24} sm={8}>
                  <Card className="total-cost-stat-card cost-card">
                    <Statistic
                      title={t("TOTAL_COST")}
                      value={`$${totalCost.toFixed(5)}`}
                      prefix={<DollarOutlined />}
                    />
                    <div className="stat-description">{t("TOTAL_COST_DESCRIPTION")}</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8}>
                  <Card className="total-cost-stat-card submits-card">
                    <Statistic
                      title={t("TOTAL_SUBMITS")}
                      value={totalSubmits}
                      prefix={<NumberOutlined />}
                    />
                    <div className="stat-description">{t("TOTAL_SUBMITS_DESCRIPTION")}</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8}>
                  <Card className="total-cost-stat-card average-card">
                    <Statistic
                      title={t("AVERAGE_COST_PER_SUBMIT")}
                      value={`$${totalSubmits > 0 ? (totalCost / totalSubmits).toFixed(5) : '0.00000'}`}
                      prefix={<DollarOutlined />}
                    />
                    <div className="stat-description">{t("AVERAGE_COST_PER_SUBMIT_DESCRIPTION")}</div>
                  </Card>
                </Col>
              </Row>

              <h2 className="total-cost-title">{t("COST_BREAKDOWN_BY_TYPE")}</h2>
              <div className="total-cost-details">
                <div className="cost-tabs">
                  <div className="tab-header">
                    <div className="tab active">{t("COST_BY_TOOL_TYPE")}</div>
                  </div>

                  <div className="tab-content">
                    <div className="cost-list">
                      <Card className="cost-list-item" hoverable>
                        <div className="cost-type-icon media-icon">
                          <SoundOutlined />
                        </div>
                        <div className="cost-info">
                          <div className="cost-name-container">
                            <h3 className="cost-name">{t("MEDIA_TOOLS_COST")}</h3>
                            <div className="cost-percent">{costPercentages.media}%</div>
                          </div>

                          <div className="cost-stats-row">
                            <div className="cost-stat">
                              <NumberOutlined className="stat-icon submits-icon" />
                              <span>{totalCostData?.mediaCost?.totalSubmits || 0} {t("SUBMITS")}</span>
                            </div>
                            <div className="cost-stat">
                              <DollarOutlined className="stat-icon cost-icon" />
                              <span className="cost-value">${(totalCostData?.mediaCost?.totalCost || 0).toFixed(5)}</span>
                            </div>
                            <div className="cost-stat">
                              <RiseOutlined className="stat-icon average-icon" />
                              <Tooltip title={t("AVERAGE_COST_PER_SUBMIT")}>
                                <span className="average-value">
                                  ${((totalCostData?.mediaCost?.totalSubmits || 0) > 0 ?
                                    ((totalCostData?.mediaCost?.totalCost || 0) / (totalCostData?.mediaCost?.totalSubmits || 1)).toFixed(5) :
                                    '0.00000')} {t("AVG")}
                                </span>
                              </Tooltip>
                            </div>
                          </div>

                          <div className="cost-progress">
                            <div
                              className="progress-bar media-bar"
                              style={{ width: `${costPercentages.media}%` }}
                            ></div>
                          </div>
                        </div>
                      </Card>

                      <Card className="cost-list-item" hoverable>
                        <div className="cost-type-icon text-icon">
                          <FileTextOutlined />
                        </div>
                        <div className="cost-info">
                          <div className="cost-name-container">
                            <h3 className="cost-name">{t("TEXT_TOOLS_COST")}</h3>
                            <div className="cost-percent">{costPercentages.text}%</div>
                          </div>

                          <div className="cost-stats-row">
                            <div className="cost-stat">
                              <NumberOutlined className="stat-icon submits-icon" />
                              <span>{totalCostData?.textCost?.totalSubmits || 0} {t("SUBMITS")}</span>
                            </div>
                            <div className="cost-stat">
                              <DollarOutlined className="stat-icon cost-icon" />
                              <span className="cost-value">${(totalCostData?.textCost?.totalCost || 0).toFixed(5)}</span>
                            </div>
                            <div className="cost-stat">
                              <RiseOutlined className="stat-icon average-icon" />
                              <Tooltip title={t("AVERAGE_COST_PER_SUBMIT")}>
                                <span className="average-value">
                                  ${((totalCostData?.textCost?.totalSubmits || 0) > 0 ?
                                    ((totalCostData?.textCost?.totalCost || 0) / (totalCostData?.textCost?.totalSubmits || 1)).toFixed(5) :
                                    '0.00000')} {t("AVG")}
                                </span>
                              </Tooltip>
                            </div>
                          </div>

                          <div className="cost-progress">
                            <div
                              className="progress-bar text-bar"
                              style={{ width: `${costPercentages.text}%` }}
                            ></div>
                          </div>
                        </div>
                      </Card>

                      <Card className="cost-list-item" hoverable>
                        <div className="cost-type-icon image-icon">
                          <FileImageOutlined />
                        </div>
                        <div className="cost-info">
                          <div className="cost-name-container">
                            <h3 className="cost-name">{t("IMAGE_TOOLS_COST")}</h3>
                            <div className="cost-percent">{costPercentages.image}%</div>
                          </div>

                          <div className="cost-stats-row">
                            <div className="cost-stat">
                              <NumberOutlined className="stat-icon submits-icon" />
                              <span>{totalCostData?.imageCost?.totalSubmits || 0} {t("SUBMITS")}</span>
                            </div>
                            <div className="cost-stat">
                              <DollarOutlined className="stat-icon cost-icon" />
                              <span className="cost-value">${(totalCostData?.imageCost?.totalCost || 0).toFixed(5)}</span>
                            </div>
                            <div className="cost-stat">
                              <RiseOutlined className="stat-icon average-icon" />
                              <Tooltip title={t("AVERAGE_COST_PER_SUBMIT")}>
                                <span className="average-value">
                                  ${((totalCostData?.imageCost?.totalSubmits || 0) > 0 ?
                                    ((totalCostData?.imageCost?.totalCost || 0) / (totalCostData?.imageCost?.totalSubmits || 1)).toFixed(5) :
                                    '0.00000')} {t("AVG")}
                                </span>
                              </Tooltip>
                            </div>
                          </div>

                          <div className="cost-progress">
                            <div
                              className="progress-bar image-bar"
                              style={{ width: `${costPercentages.image}%` }}
                            ></div>
                          </div>
                        </div>
                      </Card>

                      {/*<Card className="cost-list-item" hoverable>*/}
                      {/*  <div className="cost-type-icon shadowing-icon">*/}
                      {/*    <UserOutlined />*/}
                      {/*  </div>*/}
                      {/*  <div className="cost-info">*/}
                      {/*    <div className="cost-name-container">*/}
                      {/*      <h3 className="cost-name">{t("SHADOWING_TOOLS_COST")}</h3>*/}
                      {/*      <div className="cost-percent">{costPercentages.shadowing}%</div>*/}
                      {/*    </div>*/}

                      {/*    <div className="cost-stats-row">*/}
                      {/*      <div className="cost-stat">*/}
                      {/*        <NumberOutlined className="stat-icon submits-icon" />*/}
                      {/*        <span>{totalCostData?.shadowingCost?.totalSubmits || 0} {t("SUBMITS")}</span>*/}
                      {/*      </div>*/}
                      {/*      <div className="cost-stat">*/}
                      {/*        <DollarOutlined className="stat-icon cost-icon" />*/}
                      {/*        <span className="cost-value">${(totalCostData?.shadowingCost?.totalCost || 0).toFixed(5)}</span>*/}
                      {/*      </div>*/}
                      {/*      <div className="cost-stat">*/}
                      {/*        <RiseOutlined className="stat-icon average-icon" />*/}
                      {/*        <Tooltip title={t("AVERAGE_COST_PER_SUBMIT")}>*/}
                      {/*          <span className="average-value">*/}
                      {/*            ${((totalCostData?.shadowingCost?.totalSubmits || 0) > 0 ? */}
                      {/*              ((totalCostData?.shadowingCost?.totalCost || 0) / (totalCostData?.shadowingCost?.totalSubmits || 1)).toFixed(5) : */}
                      {/*              '0.00000')} {t("AVG")}*/}
                      {/*          </span>*/}
                      {/*        </Tooltip>*/}
                      {/*      </div>*/}
                      {/*    </div>*/}

                      {/*    <div className="cost-progress">*/}
                      {/*      <div*/}
                      {/*        className="progress-bar shadowing-bar"*/}
                      {/*        style={{ width: `${costPercentages.shadowing}%` }}*/}
                      {/*      ></div>*/}
                      {/*    </div>*/}
                      {/*  </div>*/}
                      {/*</Card>*/}
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </Loading>
      </Card>
    </div>
  );
    {/*<Card title="Statistics for media tools" bordered={true} className="mt-4">*/}
    {/*  <Row gutter={16}>*/}
    {/*    <Col span={8}>*/}
    {/*      <Statistic*/}
    {/*        title="Total submitsions"*/}
    {/*        value={totalCostData.mediaCost.totalSubmits}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={8}>*/}
    {/*      <Statistic*/}
    {/*        title="Total minutes"*/}
    {/*        precision={2}*/}
    {/*        value={totalCostData.mediaCost.totalMinutes}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={8}>*/}
    {/*      <Statistic*/}
    {/*        title="Cost per submitsion"*/}
    {/*        precision={5}*/}
    {/*        value={totalCostData.mediaCost.costPerSubmit}*/}
    {/*        suffix={<DollarOutlined/>}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*  </Row>*/}
    {/*  <Row gutter={16} style={{ marginTop: 32 }}>*/}
    {/*    <Col span={8}>*/}
    {/*      <Statistic*/}
    {/*        title="Total cost"*/}
    {/*        precision={5}*/}
    {/*        value={totalCostData.mediaCost.totalCost}*/}
    {/*        suffix={<DollarOutlined/>}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    */}
    {/*    <Col span={8}>*/}
    {/*      <Statistic*/}
    {/*        title="Minutes per submitsion"*/}
    {/*        precision={2}*/}
    {/*        value={totalCostData.mediaCost.minutesPerSubmit}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={8}>*/}
    {/*      <Statistic*/}
    {/*        title="Cost per minute"*/}
    {/*        precision={3}*/}
    {/*        value={totalCostData.mediaCost.costPerMinute}*/}
    {/*        suffix={<DollarOutlined/>}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*  </Row>*/}
    {/*</Card>*/}
    {/*<Card title="Statistics for image tools" bordered={true} className="mt-4">*/}
    {/*  <Row gutter={16}>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Total submitsions"*/}
    {/*        value={totalCostData.imageCost.totalSubmits}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        titleFontSize={20}*/}
    {/*        title="Total input tokens"*/}
    {/*        value={totalCostData.imageCost.totalPromptTokens}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Total output tokens"*/}
    {/*        value={totalCostData.imageCost.totalCompletionTokens}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Total tokens"*/}
    {/*        value={totalCostData.imageCost.totalTokens}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*  */}
    {/*  </Row>*/}
    {/*  <Row gutter={16} style={{ marginTop: 32 }}>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Total cost"*/}
    {/*        precision={5}*/}
    {/*        value={totalCostData.imageCost.totalCost}*/}
    {/*        suffix={<DollarOutlined/>}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Input tokens per submitsion"*/}
    {/*        precision={0}*/}
    {/*        value={totalCostData.imageCost.promptTokensPerSubmit}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Output tokens per submitsion"*/}
    {/*        precision={0}*/}
    {/*        value={totalCostData.imageCost.completionTokensPerSubmit}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Cost per submitsion"*/}
    {/*        precision={5}*/}
    {/*        value={totalCostData.imageCost.costPerSubmit}*/}
    {/*        suffix={<DollarOutlined/>}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*  </Row>*/}
    {/*</Card>*/}
    {/*<Card title="Statistics for text tools" bordered={true} className="mt-4">*/}
    {/*  <Row gutter={16}>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Total submitsions"*/}
    {/*        value={totalCostData.textCost.totalSubmits}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Total input tokens"*/}
    {/*        value={totalCostData.textCost.totalPromptTokens}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Total output tokens"*/}
    {/*        value={totalCostData.textCost.totalCompletionTokens}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Total tokens"*/}
    {/*        value={totalCostData.textCost.totalTokens}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*  */}
    {/*  </Row>*/}
    {/*  <Row gutter={16} style={{ marginTop: 32 }}>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Total cost"*/}
    {/*        precision={5}*/}
    {/*        value={totalCostData.textCost.totalCost}*/}
    {/*        suffix={<DollarOutlined/>}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Input tokens per submitsion"*/}
    {/*        precision={0}*/}
    {/*        value={totalCostData.textCost.promptTokensPerSubmit}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Output tokens per submitsion"*/}
    {/*        precision={0}*/}
    {/*        value={totalCostData.textCost.completionTokensPerSubmit}*/}
    {/*      />*/}
    {/*    </Col>*/}
    {/*    <Col span={6}>*/}
    {/*      <Statistic*/}
    {/*        title="Cost per submitsion"*/}
    {/*        precision={5}*/}
    {/*        value={totalCostData.textCost.costPerSubmit}*/}
    {/*        suffix={<DollarOutlined/>}*/}
    {/*      />*/}
};

export default TotalCost;
