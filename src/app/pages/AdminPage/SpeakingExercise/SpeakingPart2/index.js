import React, {useState} from "react";
import {<PERSON>ton, Card, Input, Table, Popconfirm, Typography, Space, Tooltip, Modal} from "antd";
import {DeleteOutlined, PlusOutlined, SoundOutlined, AppstoreAddOutlined} from "@ant-design/icons";
import {useTranslation} from "react-i18next";
import AI_GEN_STAR from "@src/assets/icons/ai-gen-star.svg";
import "../SpeakingParts.scss";

const {TextArea} = Input;

const SpeakingPart2 = ({
                         questions,
                         onQuestionsChange,
                         onCreateAudio,
                         onCreateHint,
                         onShowHint,
                         audioMap,
                         loadingHintPart,
                         loadingHintIndex,
                         loadingAudioPart,
                         loadingAudioIndex,
                         onBatchAudioCreation,
                         onBatchHintCreation,
                         loadingBatchAudioPart,
                         loadingBatchHintPart,
                         isAnyActionRunning
                       }) => {
  const {t} = useTranslation();
  const [isBulkAddModalVisible, setIsBulkAddModalVisible] = useState(false);
  const [bulkQuestions, setBulkQuestions] = useState("");
  const handleAddCueCard = () => {
    const updatedQuestions = [
      ...questions,
      {
        id: `part2_cuecard_${Date.now()}`,
        text: "",
        audioId: null
      }
    ];
    onQuestionsChange(updatedQuestions);
  };

  const handleDeleteCueCard = (index) => {
    const updatedQuestions = [...questions];
    updatedQuestions.splice(index, 1);
    onQuestionsChange(updatedQuestions);
  };

  const handleCueCardChange = (index, value) => {
    const updatedQuestions = [...questions];
    updatedQuestions[index].text = value;
    onQuestionsChange(updatedQuestions);
  };

  const handleCreateCueCardAudio = (index) => {
    onCreateAudio(questions[index].text, "part2", index);
  };

  const handleCreateHint = (index) => {
    onCreateHint(questions[index].text, "part2", index);
  };

  const handleShowHint = (index) => {
    onShowHint(questions[index].hint, "part2", index);
  };

  const handleBulkAddCueCards = () => {
    if (bulkQuestions.trim()) {
      // Tách các cue card theo số thứ tự (1., 2., 3., ...)
      // Sử dụng regex để tìm các vị trí bắt đầu của mỗi cue card
      const text = bulkQuestions.trim();
      const matches = [...text.matchAll(/(\d+\.\s)/g)];

      if (matches.length === 0) {
        // Nếu không có số thứ tự, coi toàn bộ text là 1 cue card
        const newCueCard = {
          id: `part2_cuecard_${Date.now()}_0`,
          text: text,
          audioId: null
        };
        const updatedQuestions = [...questions, newCueCard];
        onQuestionsChange(updatedQuestions);
      } else {
        // Tách theo các vị trí số thứ tự và xoa số thự tự trong text
        const cueCardBlocks = [];
        for (let i = 0; i < matches.length; i++) {
          const startIndex = matches[i].index;
          const endIndex = i < matches.length - 1 ? matches[i + 1].index : text.length;
          const block = text.substring(startIndex, endIndex).trim();
          if (block.length > 0) {
            cueCardBlocks.push(block);
          }
        }
        if (cueCardBlocks.length > 0) {
          const newCueCards = cueCardBlocks.map((cueCardText, index) => ({
            id: `part2_cuecard_${Date.now()}_${index}`,
            text: cueCardText.trim().replace(/\d+\.\s/g, ""),
            audioId: null
          }));

          const updatedQuestions = [...questions, ...newCueCards];
          onQuestionsChange(updatedQuestions);
        }
      }

      setBulkQuestions("");
      setIsBulkAddModalVisible(false);
    }
  };

  const handleBulkAddModalCancel = () => {
    setIsBulkAddModalVisible(false);
    setBulkQuestions("");
  };

  return (
    <Card
      title={<Typography.Title level={4}>{t("SPEAKING_PART2_TITLE")}</Typography.Title>}
      className="speaking-part2-container"
      extra={<Typography.Text type="secondary">{t("SPEAKING_PART2_SUBTITLE")}</Typography.Text>}
    >
      <Typography.Title level={5}>{t("SPEAKING_PART2_CUE_CARD_TITLE")} <Typography.Text type="secondary" style={{
        fontSize: '14px',
        fontWeight: 'normal'
      }}>{t("SPEAKING_PART2_CUE_CARD_SUBTITLE")}</Typography.Text></Typography.Title>

      {questions.map((cueCard, index) => (
        <div className="cue-card-section" key={cueCard.id}>
          <div className="cue-card-header">
            <Typography.Text strong className="cue-card-number">{index + 1}.</Typography.Text>
            <div className="cue-card-actions">
              <Tooltip
                title={cueCard.audioId ? t("RECREATE_AUDIO_TOOLTIP") : t("CREATE_AUDIO_TOOLTIP")}
                placement="top"
              >
                <Button
                  icon={<SoundOutlined/>}
                  onClick={() => handleCreateCueCardAudio(index)}
                  type={cueCard.audioId ? "primary" : "default"}
                  className={!cueCard.audioId ? "audio-btn-default" : ""}
                  loading={loadingAudioPart === "part2" && loadingAudioIndex === index}
                  disabled={isAnyActionRunning && !(loadingAudioPart === "part2" && loadingAudioIndex === index)}
                >
                  {!(loadingAudioPart === "part2" && loadingAudioIndex === index) &&
                    (cueCard.audioId ? t("RECREATE_AUDIO") : t("CREATE_AUDIO"))}
                </Button>
              </Tooltip>
              <Tooltip
                title={cueCard.hint ? t("SHOW_HINT_TOOLTIP") : t("CREATE_HINT_TOOLTIP")}
                placement="top"
              >
                <Button
                  onClick={() => cueCard.hint ? handleShowHint(index) : handleCreateHint(index)}
                  className={cueCard.hint ? "ai-hint-btn show-hint" : "ai-hint-btn"}
                  loading={!cueCard.hint && loadingHintPart === "part2" && loadingHintIndex === index}
                  disabled={isAnyActionRunning && !(loadingHintPart === "part2" && loadingHintIndex === index)}
                >
                  {!(loadingHintPart === "part2" && loadingHintIndex === index) &&
                    <img src={AI_GEN_STAR} alt="AI Generate Hint" className="ai-gen-star-icon"/>}
                  {cueCard.hint ? t("SHOW_HINT") : t("CREATE_HINT")}
                  {cueCard.hint && <span className="hint-badge"></span>}
                </Button>
              </Tooltip>
              <Popconfirm
                title={t("DELETE_CUE_CARD_CONFIRM")}
                description={t("DELETE_CUE_CARD_DESCRIPTION")}
                onConfirm={() => handleDeleteCueCard(index)}
                okText={t("YES")}
                cancelText={t("NO")}
                placement="left"
                okButtonProps={{danger: true}}
                icon={<DeleteOutlined style={{color: 'red'}}/>}
              >
                <Tooltip title={t("DELETE_CUE_CARD_TOOLTIP")} placement="top">
                  <Button
                    danger
                    icon={<DeleteOutlined/>}
                    disabled={isAnyActionRunning}
                  />
                </Tooltip>
              </Popconfirm>
            </div>
          </div>

          <div className="cue-card-textarea-container">
            <div className="cue-card-label">{t("IELTS_CUE_CARD")}</div>
            <TextArea
              value={cueCard.text}
              onChange={(e) => handleCueCardChange(index, e.target.value)}
              placeholder={t("SPEAKING_PART2_CUE_CARD_PLACEHOLDER")}
              autoSize={{minRows: 3, maxRows: 5}}
              className="cue-card-textarea"
              disabled={isAnyActionRunning}
            />
          </div>

          {cueCard.audioId && audioMap[cueCard.audioId] && (
            <audio
              src={audioMap[cueCard.audioId]}
              controls
              className="cue-card-audio"
            />
          )}
        </div>
      ))}

      <div className="add-cue-card-button-container">
        <Tooltip title={t("ADD_CUE_CARD_TOOLTIP")} placement="top">
          <Button
            type="dashed"
            icon={<PlusOutlined/>}
            onClick={handleAddCueCard}
            className="add-cue-card-button"
            size="large"
            disabled={isAnyActionRunning}
          >
            {t("ADD_CUE_CARD")}
          </Button>
        </Tooltip>
        <Tooltip title={t("ADD_MULTIPLE_CUE_CARDS_TOOLTIP")} placement="top">
          <Button
            type="dashed"
            icon={<AppstoreAddOutlined/>}
            onClick={() => setIsBulkAddModalVisible(true)}
            className="add-multiple-cue-cards-button"
            size="large"
            disabled={isAnyActionRunning}
          >
            {t("ADD_MULTIPLE_CUE_CARDS")}
          </Button>
        </Tooltip>
      </div>

      <div className="batch-operations-container">
        {/*<div className="batch-operations-label">{t("BATCH_OPERATIONS")}</div>*/}
        <div className="batch-operations-buttons">
          <Button
            type="primary"
            icon={<SoundOutlined/>}
            onClick={() => onBatchAudioCreation("part2")}
            loading={loadingBatchAudioPart === "part2"}
            disabled={(questions.filter(q => !q.audioId).length === 0) || (isAnyActionRunning && loadingBatchAudioPart !== "part2")}
            className="batch-operation-button"
          >
            {t("CREATE_AUDIO_FOR_ALL")}
          </Button>
          <Button
            type="primary"
            icon={<img src={AI_GEN_STAR} alt="AI" className="ai-gen-star-icon"/>}
            onClick={() => onBatchHintCreation("part2")}
            loading={loadingBatchHintPart === "part2"}
            disabled={(questions.filter(q => !q.hint).length === 0) || (isAnyActionRunning && loadingBatchHintPart !== "part2")}
            className="batch-operation-button ai-batch-button"
          >
            {t("CREATE_HINT_FOR_ALL")}
          </Button>
        </div>
      </div>

      <Modal
        title={t("ADD_MULTIPLE_CUE_CARDS")}
        open={isBulkAddModalVisible}
        onOk={handleBulkAddCueCards}
        onCancel={handleBulkAddModalCancel}
        width={800}
        okText={t("ADD_CUE_CARDS")}
        cancelText={t("CANCEL")}
        okButtonProps={{
          disabled: !bulkQuestions.trim() || isAnyActionRunning
        }}
        cancelButtonProps={{
          disabled: isAnyActionRunning
        }}
      >
        <div className="bulk-add-modal-content">
          <p className="bulk-add-description">
            {t("BULK_ADD_CUE_CARDS_DESCRIPTION")}
          </p>
          <TextArea
            value={bulkQuestions}
            onChange={(e) => setBulkQuestions(e.target.value)}
            placeholder={t("BULK_CUE_CARDS_PLACEHOLDER")}
            autoSize={{minRows: 8, maxRows: 15}}
            disabled={isAnyActionRunning}
          />
          <div className="bulk-add-info">
            <Typography.Text type="secondary">
              {t("BULK_ADD_CUE_CARDS_INFO")}
            </Typography.Text>
          </div>
        </div>
      </Modal>
    </Card>
  );
};

export default SpeakingPart2;
