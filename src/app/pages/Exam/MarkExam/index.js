import { useEffect, useMemo, useState } from "react";
import { useTranslation } from 'react-i18next';

import { BUTTON, MARK_EXAM_TOOL_TYPE, TOOL_GROUP_ICON } from '@constant';

import AntButton from '@src/app/component/AntButton';
import Loading from "@component/Loading";
import ToolGroup from "@src/app/component/Tool/ToolGroup";
import NoData from "@src/app/component/NoData";

import { cloneObj } from "@common/functionCommons";
import { getMarkExamTools } from "@src/app/services/Tool";

import './MarkExam.scss'

const MarkExam = ({ ...props }) => {
  const { t, i18n } = useTranslation();
  const { language } = i18n;

  const [activeType, setActiveType] = useState(MARK_EXAM_TOOL_TYPE.IELTS);
  const [toolLoading, setToolLoading] = useState(false);
  const [markExamTools, setMarkExamTools] = useState([]);

  useEffect(() => {
    getMarkExamToolData();
  }, []);

  const getMarkExamToolData = async () => {
    setToolLoading(true);
    const apiResponse = await getMarkExamTools();
    if (apiResponse) {
      setMarkExamTools(apiResponse);
    }
    setToolLoading(false);
  }

  const toolGrouped = useMemo(() => {
    if (!markExamTools.length) return {};
    const activeTypeTools = markExamTools.filter(tool => tool.type === activeType);
    const toolGrouped = cloneObj(activeTypeTools).reduce(function (grouped, element) {
      element.groupToolIds.forEach(group => {
        grouped[group.code] ||= group;
        grouped[group.code].icon ||= TOOL_GROUP_ICON[group.code] || TOOL_GROUP_ICON.READ_UNDERSTAND;
        grouped[group.code].tools ||= [];
        grouped[group.code].tools.push({
          ...element,
          name: element.localization?.name[language] || element.name,
          description: element.localization?.description[language] || element.description
        });
      });
      return grouped;
    }, {});
    Object.values(toolGrouped).forEach(group => {
      group.groupName = group.localization?.groupName[language] || group.groupName;
      group.description = group.localization?.description[language] || group.description;
    })
    return toolGrouped;
  }, [markExamTools, activeType, language]);

  const onChangeActiveType = (type) => {
    setActiveType(type);
  }

  const hanldeAfterFavoriteTool = (toolId, isFavorite) => {
    setMarkExamTools(pre => pre.map(tool => {
      if (tool._id === toolId) {
        return {
          ...tool,
          isFavorite
        }
      }
      return tool
    }))
  }

  if (toolLoading) {
    return <Loading active transparent />;
  }

  return (
    <div className="mark-exam-container">
      <div className="mark-exam-category">
        <AntButton
          size="small"
          type={BUTTON.LIGHT_PINK}
          active={activeType === MARK_EXAM_TOOL_TYPE.IELTS}
          onClick={() => onChangeActiveType(MARK_EXAM_TOOL_TYPE.IELTS)}
        >
          {t("MARK_IELTS_EXAM")}
        </AntButton>
        <AntButton
          size="small"
          type={BUTTON.LIGHT_NAVY}
          active={activeType === MARK_EXAM_TOOL_TYPE.HIGHT_SCHOOL}
          onClick={() => onChangeActiveType(MARK_EXAM_TOOL_TYPE.HIGHT_SCHOOL)}
        >
          {t("MARK_HIGH_SCHOOL_EXAM")}
        </AntButton>
      </div>

      {!Object.values(toolGrouped).length && <NoData searchNoData={true}>{t("NO_DATA")}</NoData>}

      {Object.values(toolGrouped).map((group, index) => {
        return <ToolGroup
          key={index}
          toolGroupData={group}
          hanldeAfterFavoriteTool={hanldeAfterFavoriteTool}
          {...props}
        />;
      })}
    </div>
  )
}

export default MarkExam;