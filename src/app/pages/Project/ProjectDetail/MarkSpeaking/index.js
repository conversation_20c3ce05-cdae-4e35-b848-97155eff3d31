import React, { useMemo } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";
import { MarkSpeakingDetail } from "./MarkSpeakingDetail";

import "./MarkSpeaking.scss";

function MarkSpeaking({ listAllTool }) {
  const { t, i18n } = useTranslation();
  const { projectId, projectContentData, isAllowEditing } = useProject();
  
  const [inputId, responseId, instruction, instructionId] = useMemo(() => {
    const {
      inputId,
      ...responseActivate
    } = projectContentData?.[0]?.responses?.find(response => response.isActivate) || {};
    
    const toolId = projectContentData?.[0]?.toolId?._id;
    const toolData = listAllTool.find(tool => tool._id === toolId);
    const instruction = toolData.instructionIds?.[0];
    const instructionId = toolData.instructionIds?.[0]?._id;
    
    return [inputId, responseActivate, instruction, instructionId];
    
  }, [projectContentData, listAllTool]);
  
  return <>
    <MarkSpeakingDetail
      projectId={projectId}
      inputData={inputId?.inputData}
      resultData={responseId}
      instructionId={instructionId}
      instructionOptions={instruction?.options}
      isAllowEditing={isAllowEditing}
    />
  
  </>;
}


function mapStateToProps(store) {
  const { listAllTool } = store.tool;
  return { listAllTool };
}

export default connect(mapStateToProps)(MarkSpeaking);