import React from "react";

import HtmlContent from "@component/HtmlContent";
import TypingEffect from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TypingEffect";


function HtmlOutputValue({ responseSelected }) {
  
  const textHtml = <div className="html-output__value">
    <HtmlContent dangerouslySetInnerHTML={{ __html: responseSelected?.output?.html }} />
  </div>;
  
  if (responseSelected.typingEffect) return <TypingEffect html={textHtml} />;
  return textHtml;
}

export default HtmlOutputValue;