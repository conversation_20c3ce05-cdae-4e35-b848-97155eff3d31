import { CONSTANT } from '@constant';
import { useTranslation } from 'react-i18next';
import { WRITING_EVALUATION_LIST } from './functionCommon';

const ImprovedEssay = ({ improvedEssay, evaluationSelected }) => {
  const { t } = useTranslation();

  if (!improvedEssay || !['improvedEssay', CONSTANT.ALL].includes(evaluationSelected)) return null;
  
  return <div className="evaluation__content__item evaluation-improved-essay">
    <div
      className="content__item__title evaluation-improved-essay__title"
      style={{ color: WRITING_EVALUATION_LIST.improvedEssay.color }}
    >
      <img src={WRITING_EVALUATION_LIST.improvedEssay.icon} alt="" />
      {t("IMPROVED_ESSAY")}
    </div>
    <p>{improvedEssay}</p>
  </div>
}

export default ImprovedEssay;