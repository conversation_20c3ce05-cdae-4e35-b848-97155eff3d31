import React, { useState, useEffect } from "react";
import { Card, Input, Form, Row, Col, Select, Tag, Tooltip } from "antd";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { EditOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";

import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";

import { getPaginationTool, deleteTool, copyTool } from "@services/Tool";
import { confirm } from "@component/ConfirmProvider";
import { handlePagingData } from "@common/dataConverter";
import { handleSearchParams, paginationConfig, handleReplaceUrlSearch, orderColumn } from "@common/functionCommons";
import { getAllOrg } from "@services/OrganizationUser";
import { toast } from "@component/ToastProvider";
import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import { TOOL_INPUT_TYPE, PAGINATION_INIT, BUTTON, SHOW_ON_WELCOME, VISIBLE_TOOL } from "@constant";
import { LINK } from "@link";


import "./Tool.scss";
import Copy from "@component/SvgIcons/Copy";
import { getInstructions, getPaginationInstruction } from "@services/Instruction";

const Tools = () => {
  const [toolsData, setToolsData] = useState(PAGINATION_INIT);
  const [orgList, setOrgList] = useState([]);
  const [instructionData, setInstructionData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [formFilter] = Form.useForm();

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getToolsData(paging, query);
  }, [location.search]);

  useEffect(() => {
    getOrgData();
    getInstructionData()
  }, []);

  const getToolsData = async (paging = toolsData.paging, query = toolsData.query) => {
    setIsLoading(true);
    const dataResponse = await getPaginationTool(paging, query);
    if (dataResponse) {
      setToolsData(handlePagingData(dataResponse, query));
    }
    setIsLoading(false);
  };

  const getInstructionData = async (query) => {
    const dataResponse = await getInstructions(query, ["optionIds", "outputTypeId"]);
    if (dataResponse) {
      setInstructionData(dataResponse);
    }
  };

  const getOrgData = async () => {
    const dataResponse = await getAllOrg();
    if (dataResponse) {
      setOrgList(dataResponse);
    }
  };

  const handleDelete = (toolId, toolName) => {
    confirm.delete({
      title: t("DELETE_TOOL"),
      content: t("DELETE_TOOL_CONFIRM", { name: toolName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async (e) => {
        setIsLoading(true);
        const apiResponse = await deleteTool(toolId, true);
        if (apiResponse) {
          toast.success(t("DELETE_TOOL_SUCCESS"));
          await getToolsData();
        } else {
          toast.error(t("DELETE_TOOL_ERROR"));
          setIsLoading(false);
        }
      },
    });
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, toolsData.paging.pageSize, values);
  };

  function renderOrganizations(value) {
    return value.map((orgId, index) => <div>
      {orgList.find((org) => org._id === orgId)?.name}
      {index < value.length - 1 ? ", " : ""}
    </div>);
  }

  function renderShowGroups(value) {
    if (!value) return;
    return value.map((item, index) => <div>
      {item?.groupName[i18n.language] || item?.groupName?.en || item?.groupName}{index < value.length - 1 ? ", " : ""}
    </div>);
  }


  const handleCopy = async (toolId, toolName) => {
    try {
      setIsLoading(true);
      const apiResponse = await copyTool({ toolId }, true);
      if (!apiResponse) throw new Error("Copy failed");

      toast.success(t("COPY_TOOL_SUCCESS", { name: toolName }));

      const paging = { ...toolsData.paging, page: 1 };

      handleReplaceUrlSearch(1, paging.pageSize, {});
      await getToolsData(paging);
    } catch (error) {
      console.error("Failed to copy tool:", error);
      toast.error(t("COPY_TOOL_ERROR"));
      setIsLoading(false);
    }
  };

  const columns = [
    orderColumn(toolsData.paging),
    {
      title: t("NAME"),
      dataIndex: "name",
      key: "name",
      width: 250,
      render: (value) => <span className="tool-name-value">{value[i18n.language] || value?.en || value}</span>,
    },
    {
      title: t("GROUPS"),
      dataIndex: "groupToolIds",
      render: (value) => renderShowGroups(value),
      key: "groupToolIds",
      width: 200,
    },
    {
      title: t("ORGANIZATIONS"),
      dataIndex: "organizationIds",
      render: (value) => renderOrganizations(value),
      key: "organizationIds",
      width: 200,
    },
    {
      title: t("CATEGORIES"),
      dataIndex: "categories",
      key: "categories",
      width: 120,
    },
    {
      title: t("INPUT_TYPE"),
      dataIndex: "inputType",
      key: "inputType",
      width: 120,
      render: (value) => {
        const inputType = TOOL_INPUT_TYPE.find((item) => item.value === value);
        let color;

        switch (value) {
          case "text":
            color = "blue";
            break;
          case "audio":
            color = "green";
            break;
          case "video":
            color = "purple";
            break;
          case "image":
            color = "magenta";
            break;
          case "topic":
            color = "orange";
            break;
          default:
            color = "default";
        }

        return (
          <Tag color={color}>
            {inputType?.label || value}
          </Tag>
        );
      },
    },
    {
      title: t("INSTRUCTION"),
      dataIndex: "instructionIds",
      render: (value) => value?.map((instruction, index) => <div>
        {!!instruction?.shortName && <>{instruction?.shortName}{index < value.length - 1 ? "," : ""}</>}
      </div>),
      width: 200,
    },
    {
      title: t("VISIBLE"),
      dataIndex: "visible",
      key: "visible",
      width: 120,
      align: "center",
      render: (value) => {
        let color;

        switch (value) {
          case "public":
            color = "green";
            break;
          case "developing":
            color = "orange";
            break;
          case "private":
            color = "red";
            break;
          default:
            color = "default";
        }

        return (
          <Tag color={color}>
            {VISIBLE_TOOL[value]?.label || value}
          </Tag>
        );
      },
    },
    {
      title: t("ACTION"),
      key: "action",
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className="tool-actions">
          <Tooltip title={t("COPY_TOOL")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-copy-tool"}
              icon={<Copy/>}
              onClick={() => handleCopy(record?._id, record?.name[i18n.language] || record?.name?.en || record?.name)}
            />
          </Tooltip>
          <Tooltip title={t("EDIT_TOOL")}>
            <Link to={LINK.TOOL_DETAIL.format(record._id)}>
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="small"
                className={"btn-edit-tool"}
                icon={<EditOutlined/>}
              />
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_TOOL")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-tool"}
              icon={<DeleteIcon/>}
              onClick={() => handleDelete(record?._id, record?.name[i18n.language] || record?.name?.en || record?.name)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(toolsData.paging, toolsData.query, i18n.language);

  const clearSearch = () => {
    onSubmitFilter({});
    formFilter.resetFields();
  };
  return (
    <Loading active={isLoading} transparent>
      <div className="tools">
        <Card className="tools-info-card">
          <div className="tools-info-header">
            <div>
              <h1 className="tools-title">{t("TOOLS_MANAGEMENT")}</h1>
              <p className="tools-description">{t("TOOLS_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.TOOL_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create-tool"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_TOOL")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="tools-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_TOOL_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item name="inputType" className="search-form-item">
                      <Select
                        options={TOOL_INPUT_TYPE}
                        allowClear
                        placeholder={t("FILTER_BY_INPUT_TYPE")}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item name="visible" className="search-form-item">
                      <Select
                        options={Object.values(VISIBLE_TOOL)}
                        allowClear
                        placeholder={t("FILTER_BY_VISIBILITY")}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item name="instructionIds" className="search-form-item">
                      <Select
                        showSearch
                        options={instructionData}
                        placeholder={t("FILTER_BY_INSTRUCTION")}
                        allowClear
                        fieldNames={{ label: "shortName", value: "_id" }}
                        optionFilterProp="shortName"
                        filterOption={(input, option) =>
                          option.shortName.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={clearSearch}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="tools-table-card">
          <TableAdmin
            columns={columns}
            dataSource={toolsData.rows}
            pagination={{ ...pagination }}
            className="tool-table"
            scroll={{ x: 1000 }}
            rowClassName={() => "tool-table-row"}
            locale={{ emptyText: t("NO_TOOLS_FOUND") }}
          />
        </Card>
      </div>
    </Loading>
  );
};

export default Tools;
