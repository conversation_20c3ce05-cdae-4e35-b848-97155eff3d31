import { useState, useEffect, useMemo } from 'react';
import { useTranslation } from "react-i18next";
import jsPDF from 'jspdf';

import AntModal from '@src/app/component/AntModal';
import Loading from '@src/app/component/Loading';
import AntButton from "@component/AntButton";
import Text from "@component/SvgIcons/Text";
import Camera from "@component/SvgIcons/Camera";
import File from "@component/SvgIcons/File";

import { BUTTON, CONSTANT, MAX_PAGE_RANGER, UPLOAD_STATUS } from '@constant';

import { getImageDetail } from "@services/Image";
import { cloneObj, stringSplit } from "@common/functionCommons";
import { getFileById, uploadFile } from "@services/File";
import { createInput, updateInputMarkTest } from "@services/Input";
import { toast } from "@component/ToastProvider";

import { useMarkStudent } from '../..';
import { useProject } from '@src/app/pages/Project';

import ImagesContent from '../../../MarkExamContent/ExamSubmission/ModalSubmission/ImagesContent';
import PdfContent from '../../../MarkExamContent/ExamSubmission/ModalSubmission/PdfContent';

import './ModalFileAssignment.scss';

const ModalFileAssignment = (props) => {
  const { t } = useTranslation();
  const { open, onClose, formEssay, setFileData } = props;
  const [pageRange, setPageRange] = useState({ startPage: 0, endPage: 0 });
  const [totalPages, setTotalPages] = useState(0);


  const { projectData } = useProject();

  const [inputType, setInputType] = useState(CONSTANT.IMAGE);
  const [isLoadingModal, setLoadingModal] = useState(false);
  const [isLoadingSubmit, setLoadingSubmit] = useState(false);

  const [imageList, setImageList] = useState([]);
  const [pdfFile, setPdfFile] = useState(null);

  useEffect(() => {
    if (open) {
      setImageList([]);
      setPdfFile(null);
      setPageRange({ startPage: 0, endPage: 0 });
      setTotalPages(0);
      setInputType(CONSTANT.IMAGE);
    }
  }, [open]);

  const validatePageRange = useMemo(() => {
    const { startPage, endPage } = pageRange;
    if (!startPage || !endPage || startPage < 1 || endPage > totalPages || startPage > endPage) {
      return { isError: true, errorMsg: t("PAGE_NUMBER_INVALID") };
    } else if (endPage - startPage >= MAX_PAGE_RANGER) {
      return { isError: true, errorMsg: t("PAGE_NUMBER_LIMIT") };
    }
    return { isError: false, errorMsg: "" };
  }, [pageRange, totalPages]);

  const calculateImageDimensions = (imgWidth, imgHeight) => {
    const aspectRatio = imgWidth / imgHeight;
    //set pdf width, height after padding 10px.
    const pageWidth = 190;
    const pageHeight = 277;

    let newWidth, newHeight;

    // So sánh tỷ lệ giữa chiều rộng và chiều cao của trang để giữ đúng tỷ lệ
    if (imgWidth > imgHeight) {
      newWidth = Math.min(pageWidth, imgWidth);
      newHeight = newWidth / aspectRatio;
      if (newHeight > pageHeight) {
        newHeight = pageHeight;
        newWidth = newHeight * aspectRatio;
      }
    } else {
      newHeight = Math.min(pageHeight, imgHeight);
      newWidth = newHeight * aspectRatio;
      if (newWidth > pageWidth) {
        newWidth = pageWidth;
        newHeight = newWidth / aspectRatio;
      }
    }

    // Căn giữa hình ảnh
    const xPos = (pageWidth + 20 - newWidth) / 2;
    const yPos = (pageHeight + 20 - newHeight) / 2;

    return { xPos, yPos, newWidth, newHeight };
  };

  const loadImage = async (src) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = src;

      img.onload = () => resolve(img);
      img.onerror = reject;
    });
  };

  const convertImagesToPdf = async (images) => {
    const pdf = new jsPDF();
    const imagesCount = images.length;
    let fileConverted, totalPages;

    for (let i = 0; i < imagesCount; i++) {
      try {
        const imageUrl = URL.createObjectURL(images[i]);
        const imageExtension = images[i].name.split('.').pop();
        const addFormat = imageExtension.toUpperCase() === 'PNG' ? 'PNG' : 'JPEG';

        const img = await loadImage(imageUrl);

        const { xPos, yPos, newWidth, newHeight } = calculateImageDimensions(img.width, img.height);

        pdf.addImage(img, addFormat, xPos, yPos, newWidth, newHeight);

        if (i < imagesCount - 1) {
          pdf.addPage();
        } else {
          const fileBlob = pdf.output('blob');
          totalPages = pdf.getNumberOfPages();
          fileConverted = fileBlob;
        }
      } catch (error) {
        toast.success(t("CONVERT_IMAGE_ERROR").format(i + 1));
        fileConverted = null;
        break;
      }
    };

    return { fileConverted, totalPages };
  }

  const validateInputType = () => {
    let isError = false;
    if (inputType === CONSTANT.IMAGE) {
      if (!imageList.length) {
        toast.warning({ description: t("UPLOAD_IMAGES_WARNING") });
        isError = true;
      } else if (imageList.length > MAX_PAGE_RANGER) {
        isError = true;
      }

    } else if (inputType === CONSTANT.FILE) {
      if (!pdfFile) {
        toast.warning({ description: t("UPLOAD_PDF_WARNING") });
        isError = true;
      } else return validatePageRange.isError;
    }
    return isError;
  }

  async function handleUpload() {
    const isValidateError = validateInputType();
    if (isValidateError) return;

    let fileUpload = pdfFile;
    let pagesInfo = {
      totalPages,
      ...pageRange
    }
    if (inputType === CONSTANT.IMAGE) {
      const convertData = await convertImagesToPdf(imageList);

      if (!convertData?.fileConverted) return;
      fileUpload = convertData?.fileConverted;
      pagesInfo = {
        totalPages: convertData.totalPages,
        startPage: 1,
        endPage: convertData.totalPages
      }
    }

    setLoadingSubmit(true);

    const formData = { workspaceId: projectData.workspaceId, folder: "office" };
    const fileResponse = await uploadFile(fileUpload, formData);
    if (fileResponse) {
      setFileData({
        fileId: fileResponse._id,
        ...pagesInfo
      });
      formEssay.setFieldsValue({ text: '' });
    }
    setLoadingSubmit(false);
    onClose();
  }

  const onChangeInputType = (type) => {
    setInputType(type);
  }

  const clearDataOtherTab = () => {
    if (inputType === CONSTANT.FILE) {
      imageList.length && setImageList([]);
    } else if (inputType === CONSTANT.IMAGE) {
      if (pdfFile) {
        setPdfFile(null);
        setPageRange({ startPage: 0, endPage: 0 });
        setTotalPages(0);
      }
    }
  }

  return (
    <AntModal
      footerless
      className="exam-submission-modal"
      open={open}
      onCancel={onClose}
      title={t("UPLOAD_FILE")}
      confirmLoading={isLoadingModal}
    >
      <Loading active={isLoadingModal}>
        <div className="content-type">
          <AntButton
            icon={<Camera />}
            size="compact"
            type={inputType === CONSTANT.IMAGE ? BUTTON.DEEP_GREEN : BUTTON.LIGHT_GREEN}
            onClick={() => onChangeInputType(CONSTANT.IMAGE)}
          >
            {t("UPLOAD_IMAGES")}
          </AntButton>
          <AntButton
            icon={<File />}
            size="compact"
            type={inputType === CONSTANT.FILE ? BUTTON.DEEP_PURPLE : BUTTON.LIGHT_PURPLE}
            onClick={() => onChangeInputType(CONSTANT.FILE)}
          >
            {t("UPLOAD_PDF")}
          </AntButton>
        </div>

        <ImagesContent
          imageList={imageList}
          setImageList={setImageList}
          inputType={inputType}
          clearDataOtherTab={clearDataOtherTab}
        />

        <PdfContent
          pdfFile={pdfFile}
          setPdfFile={setPdfFile}
          inputType={inputType}
          pageRange={pageRange}
          setPageRange={setPageRange}
          clearDataOtherTab={clearDataOtherTab}
          validatePageRange={validatePageRange}
          totalPages={totalPages}
          setTotalPages={setTotalPages}
        />
      </Loading>
      <div className="exam-submission-modal-actions">
        <AntButton
          size="large"
          onClick={onClose}
          type={BUTTON.WHITE}
          disabled={isLoadingModal}
        >
          {t("CANCEL")}
        </AntButton>
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          onClick={handleUpload}
          loading={isLoadingSubmit}
          disabled={isLoadingModal || isLoadingSubmit}
        >
          {t('SAVE')}
        </AntButton>
      </div>
    </AntModal>
  );
};
export default ModalFileAssignment;