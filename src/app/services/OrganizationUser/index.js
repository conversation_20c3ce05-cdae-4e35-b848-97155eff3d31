import { API } from "@api";

import {
  createBase,
  deleteBase,
  getAllBase,
  updateBase,
  getAllManager,
  getDetailBase,
  getAllPaginationBase,
} from "@services/Base";
import axios from "axios";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";

export function getOrganizationByUser(query, loading) {
  return getAllBase(API.ORGANIZATION_USER, query, ["organizationId"], loading);
}
export function deleteUserOrganization(data) {
  return updateBase(API.USER_ID, data);
}
export function updateOrganization(data) {
  return updateBase(API.ORGANIZATION_ID, data);
}
export function getAllOrg(query, loading) {
  return getAllBase(API.ORGANIZATION, query, [], loading);
}
