import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";
import { Dropdown, Form, Input } from "antd";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";


import AntButton from "@component/AntButton";
import MoreVertical from "@component/SvgIcons/MoreVertical";
import { AntForm } from "@component/AntForm";

import { BUTTON, CONSTANT, PERMISSION, RECOGNIZE_STATUS } from "@constant";
import RULE from "@rule";

import SpeakingResult from "@app/pages/Student/Speaking/SpeakingResult";
import SpeakingErrors from "@app/pages/Student/Speaking/SpeakingErrors";
import SpeakingChart from "@app/pages/Student/Speaking/SpeakingChart";
import SpeakingRecordedAudioPlayer from "@app/pages/Student/Speaking/SpeakingRecordedAudioPlayer";
import SpeakingAudioInputAction from "@app/pages/Student/Speaking/SpeakingAudioInputAction";
import SpeakingRecorder from "@app/pages/Student/Speaking/SpeakingRecorder";
import UploadRecordedAudio from "@app/pages/Student/Speaking/UploadRecordedAudio";
import SpeakingTooShort from "@app/pages/Student/Speaking/SpeakingTooShort";
import SpeakingGetFeedback from "@app/pages/Student/Speaking/SpeakingGetFeedback";
import SpeakingHeader from "@app/pages/Student/Speaking/SpeakingHeader";

import { deleteProject, getProjectDetail, updateProject } from "@services/Project";

import "./Speaking.scss";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import clsx from "clsx";
import SpeakingFeedback from "@app/pages/Student/Speaking/SpeakingFeedback";
import { streamSSEResponse } from "@services/Content";
import { toast } from "@component/ToastProvider";
import { LINK } from "@link";
import PopupUploadAudio from "@app/pages/Student/Speaking/PopupUploadAudio";
import ShareIcon from "@component/SvgIcons/ShareIcon";
import Trash from "@component/SvgIcons/Trash";
import { confirm } from "@component/ConfirmProvider";
import Share from "@component/Share";
import StudentBreadcrumb from "@app/layout/StudentLayout/StudentBreadcrumb";
import AutoResizeInput from "@component/AutoResizeInput";
import Loading from "@component/Loading";
import { cloneObj } from "@common/functionCommons";
import NeedAccess from "@src/app/component/NeedAccess";
import NoData from "@src/app/component/NoData";
import TagItem from "@src/app/pages/Student/MyAssign/FilterAssign/FillterTag/TagItem";
import { checkAutoFeedback } from "@src/app/services/Feedback";
import FeedbackModal from "@src/app/component/FeedbackModal";
import { actions, paramsCreators, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";
import { connect, useDispatch } from "react-redux";
import GenerateTopicPopover from "@src/app/component/GenerateTopicPopover";

export const SpeakingContext = React.createContext();


function Speaking({ studentTools }) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const speakingId = useParams().id;
  const location = useLocation();

  const eventSource = useRef(undefined);
  const [speakingForm] = Form.useForm();

  const [isFirst, setFirst] = useState(true);
  const [isLoading, setLoading] = useState(false);

  const [text, setText] = useState("");
  const [recordResult, setRecordResult] = useState([]);

  const projectNameTemp = useRef("");
  const [projectData, setProjectData] = useState(null);

  const [audioFileId, setAudioFileId] = useState(null);
  const [audioUrlDownload, setAudioUrlDownload] = useState(null);
  const [wordAnalyzed, setWordAnalyzed] = useState([]);
  const [countErrors, setCountErrors] = useState({});

  const [showErrors, setShowErrors] = useState({
    mispronunciation: true,
    unexpectedBreak: true,
    missingBreak: true,
    monotone: true,
  });

  const [recognizeStatus, setRecognizeStatus] = useState(RECOGNIZE_STATUS.NOT_STARTED);

  const [isContentTooShort, setContentTooShort] = useState(false);
  const [isAudioTooShort, setAudioTooShort] = useState(false);

  const [responseData, setResponseData] = useState(null);
  const [feedbackData, setFeedbackData] = useState({});
  const [audioInputType, setAudioInputType] = useState(CONSTANT.RECORD);

  const [isShowUpload, setShowUpload] = useState(false);
  const [audioFileSelected, setAudioFileSelected] = useState(null);
  const [isShowModalShare, setShowModalShare] = useState(false);

  const [isShowFeedbackModal, setShowFeedbackModal] = useState(false);
  const dispatch = useDispatch();

  const { permission } = projectData || {};
  const disabledEdit = permission === PERMISSION.VIEWER || speakingId || recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING;

  const { speaking: speakingTools } = studentTools || {};

  const toolInfo = useMemo(() => {
    const toolData = speakingTools
    return toolData;
  }, [speakingTools]);

  useEffect(() => {
    if (speakingId) {
      getSpeakingData();
    } else {
      setFirst(false);
      speakingForm.resetFields();
      setText("");
      setRecordResult([]);
      setProjectData(null);
      setAudioFileId(null);
      setAudioUrlDownload(null);
      setWordAnalyzed([]);
      setCountErrors({});

      setShowErrors({
        mispronunciation: true,
        unexpectedBreak: true,
        missingBreak: true,
        monotone: true,
      });
      setRecognizeStatus(RECOGNIZE_STATUS.NOT_STARTED);
      setContentTooShort(false);
      setAudioTooShort(false);
      setResponseData(null);
      setFeedbackData({});
      setAudioInputType(CONSTANT.RECORD);
      setShowUpload(false);
      setAudioFileSelected(null);
      projectNameTemp.current = "";
    }

    return () => setFirst(true);
  }, [speakingId]);

  useEffect(() => {
    if (location?.state?.projectId) {
      getSpeakingData(location.state.projectId);
    }
  }, [location]);

  useEffect(() => {
    function findErrors(word) {
      const breakErrors = word?.pronunciationAssessment?.feedback?.prosody?.break?.errorTypes;
      const intonationErrors = word?.pronunciationAssessment?.feedback?.prosody?.intonation?.errorTypes;
      const isUnexpectedBreak = breakErrors?.includes("UnexpectedBreak");
      const isMissingBreak = breakErrors?.includes("MissingBreak");
      const isMonotone = intonationErrors?.includes("Monotone");
      const isMispronunciation = word.pronunciationAssessment?.accuracyScore < 60;

      return {
        isUnexpectedBreak,
        isMissingBreak,
        isMonotone,
        isMispronunciation,
      };
    }

    const errorTemp = {
      unexpectedBreak: 0,
      missingBreak: 0,
      monotone: 0,
      mispronunciation: 0,
    };

    const words = recordResult
      .flatMap(item => item.nBest.flatMap(nBestItem => nBestItem.words))
      .filter(item => !!item?.word)
      .map(word => {
        const { isUnexpectedBreak, isMissingBreak, isMonotone, isMispronunciation } = findErrors(word);
        if (isUnexpectedBreak) errorTemp.unexpectedBreak += 1;
        if (isMissingBreak) errorTemp.missingBreak += 1;
        if (isMonotone) errorTemp.monotone += 1;
        if (isMispronunciation) errorTemp.mispronunciation += 1;

        word.errors = {
          unexpectedBreak: isUnexpectedBreak,
          missingBreak: isMissingBreak,
          monotone: isMonotone,
          mispronunciation: isMispronunciation,
        };

        return word;
      });

    setWordAnalyzed(words);
    setCountErrors(errorTemp);
    setContentTooShort(recordResult.length && words.length < 10);

  }, [recordResult]);

  useEffect(() => {
    speakingId && projectData?.content?.[0] && dispatch(actions.trackCustomViewStudyHub(paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name)));
  }, [projectData, speakingId]);

  useEffect(() => {
    if (responseData?.state?.toUpperCase() === CONSTANT.PROCESSING) {
      if(responseData._id){
        handleStream();
      }
    }
    return () => closeEv();
  }, [responseData]);

  useEffect(() => {
    const { checkFeedback } = location.state || {};
    if (checkFeedback) {
      setTimeout(() => {
        checkShowFeedback();
      }, [10000]);
    }
  }, [location.state]);

  const checkShowFeedback = async () => {
    const response = await checkAutoFeedback();
    if (response?.showFeedback) setShowFeedbackModal(true);
  }

  function handleStream() {
    eventSource.current = streamSSEResponse(responseData._id);
    eventSource.current.addEventListener(responseData._id, async (event) => {
      const data = JSON.parse(event?.data);
      switch (data?.state?.toUpperCase()) {
        case CONSTANT.ERROR:
          setResponseData(data);
          break;
        case CONSTANT.DONE:
          if(data?.output?.state === 'invalid'){
            setResponseData(data);
            data?.output?.reason && toast.error(data?.output?.reason);
          } else {
            navigate(LINK.SPEAKING_ID.format(projectData._id), { state: { checkFeedback: true } });
          }
          break;
        default:
          break;
      }
    });
    eventSource.current.addEventListener("error", async (error) => {
      closeEv();
    });
  }

  function closeEv() {
    eventSource.current?.close();
    eventSource.current = null;
  }

  async function getSpeakingData(projectId = speakingId) {
    if (isFirst && projectId === projectData?._id) {
      setFirst(false);
      return;
    }

    setLoading(true);

    const apiResponse = await getProjectDetail(projectId);

    switch (apiResponse?.code) {
      case 200:
        setProjectData(apiResponse.data);
        const response = apiResponse.data?.content?.[0]?.responses?.find(response => response.isActivate);

        if (response) {
          const inputData = response.inputId?.inputData || {};
          speakingForm.setFieldsValue(inputData);

          setAudioFileId(inputData.fileId);
          setAudioUrlDownload(inputData.audioUrl);

          if (Array.isArray(inputData?.results)) {
            setRecordResult(convertSnakeCaseToCamelCase(inputData.results));
          }
          setFeedbackData(response.output);
          setResponseData(response);
          setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
        }

        handleRedirect(projectId, response?.output);
        setLoading(false);
        break;
      case 403:
        setFirst(false);
        setProjectData({ permission: PERMISSION.NO_PERMISSION });
        setLoading(false);
        break;
      case 404:
        setFirst(false);
        setProjectData({ permission: CONSTANT.NOT_FOUND });
        setLoading(false);
        break;
      default:
        setFirst(false);
        toast.error("AN_ERROR_OCCURRED");
        break;
    }
  }

  const isShowFeedback = useMemo(() => {
    return !!speakingId && !!feedbackData?.evaluations && !!feedbackData?.score;
  }, [speakingId, feedbackData]);


  function handleRedirect(projectId, feedback) {
    if (!speakingId && !!feedback?.evaluations && !!feedback?.score) {
      setFirst(true);
      navigate(LINK.SPEAKING_ID.format(projectId), { replace: true });
    } else if (location.pathname !== LINK.SPEAKING && !feedback?.evaluations && !feedback?.score) {
      setFirst(false);
      navigate(LINK.SPEAKING, { state: { projectId }, replace: true });
    } else {
      setFirst(false);
    }
  }

  function handleDataResponse(socket) {
    socket.on("error", (err) => {
      socket.disconnect();
      setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
    });

    socket.on("finish-recognition", () => {
      socket.disconnect();
      setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
    });
    socket.on("message", (message) => {
      //if (message.state !== "recognizing")
      //console.log("Message from server:", message);

      switch (message.state) {
        case "recognizing":
          setText(message.recognizing);
          break;
        case "sentence_score":
          setText("");
          const results = convertSnakeCaseToCamelCase([message.results]);
          setRecordResult(prevState => [...prevState, ...results]);
          break;
        case "overall_score":
          setRecordResult(convertSnakeCaseToCamelCase(message.results));
          break;
        case "recognized_text":
          //console.log("recognized_text", message);
          break;
        case "content_score":
          //console.log("content_score", message);
          break;
        case "audio_file_saved":
          setAudioFileId(message.fileId || "");
          setAudioUrlDownload(message.audioUrl || "");
          break;
        case "error":
          console.log("error", message);
          socket.disconnect();
          setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
          toast.error("AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR");
          break;
        default:
          break;
      }
    });
  }


  function onShareProjectAction() {
    setShowModalShare(prevState => !prevState);
    !isShowModalShare && dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_SHARE, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name)))
  }

  function onDeleteProjectAction() {
    confirm.delete({
      content: t("CONFIRM_DELETE_PROJECT"),
      handleConfirm: async () => {
        const apiResponse = await deleteProject(projectData._id);
        if (apiResponse) {
          toast.success("DELETE_PROJECT_SUCCESS");
          navigate(LINK.SPEAKING, { replace: true });
        }
      },
    });
    dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_DELETE, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name)))
  }

  async function handleChangeProjectName(value) {
    if (projectData?._id) {
      const dataResponse = await updateProject({ _id: projectData._id, projectName: value });
      if (dataResponse) {
        setProjectData(prevState => {
          const newState = cloneObj(prevState);
          newState.projectName = dataResponse.projectName;
          return newState;
        });
      }
    } else {
      projectNameTemp.current = value;
    }
  }

  if (isFirst) return <Loading />;

  if (permission === PERMISSION.NO_PERMISSION) {
    return <NeedAccess />;
  }

  if (permission === CONSTANT.NOT_FOUND) {
    return <NoData>{t("DATA_NOT_FOUND")}</NoData>;
  }

  const handleTopicGenerated = (topic) => {
    speakingForm.setFieldsValue({ topic });
  }

  return <>
    <SpeakingContext.Provider
      value={{
        speakingId,
        speakingForm,

        projectNameTemp: projectNameTemp.current,
        projectData, setProjectData,

        text, setText,
        recordResult, setRecordResult,
        wordAnalyzed, setWordAnalyzed,
        countErrors, setCountErrors,
        showErrors, setShowErrors,
        audioFileId, setAudioFileId,
        audioUrlDownload, setAudioUrlDownload,

        recognizeStatus, setRecognizeStatus,
        isContentTooShort, setContentTooShort,
        isAudioTooShort, setAudioTooShort,

        responseData, setResponseData,
        feedbackData,
        isShowFeedback,

        audioInputType, setAudioInputType,

        handleDataResponse,
        isShowUpload, setShowUpload,
        audioFileSelected, setAudioFileSelected,

        disabledEdit,
      }}
    >
      <Loading
        active={isLoading}
        className={clsx("speaking-container", { "speaking-container-has-feedback": isShowFeedback })}
      >
        {isShowFeedback && <StudentBreadcrumb />}

        <div className="speaking-inner">
          <SpeakingHeader />

          <div className="speaking-title">
            <AutoResizeInput
              value={projectData?.projectName || "Untitled speech" || t("UNTITLED_SPEECH")}
              onSubmit={handleChangeProjectName}
              disabledEdit={disabledEdit}
              extra={!!projectData && <Dropdown
                menu={{
                  items: [
                    { key: "SHARE", label: t("SHARE"), icon: <ShareIcon />, onClick: onShareProjectAction },
                    //{ key: "RENAME", label: t("RENAME"), icon: <Edit />, onClick: onRenameProjectAction },
                    { key: "DELETE", label: t("DELETE"), icon: <Trash />, onClick: onDeleteProjectAction },
                  ],
                  //className: clsx("action-dropdown-menu", menuClassName),
                }}
                trigger={["click"]}
              >
                <AntButton
                  size="compact"
                  icon={<MoreVertical />}
                  type={BUTTON.GHOST_NAVY}
                  onClick={(e) => dispatch(actions.trackCustomClick(TRACKING_ACTIONS.OPEN_OPTION, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name)))}
                />
              </Dropdown>}
            />
          </div>


          <div className="speaking__body">
            <div className="speaking-input-container">
              <div id="js-speaking-topic" className="speaking-section">
                <div className="speaking-section__title">
                  <span className="speaking-section__title-text">
                    {t("TOPIC")}
                  </span>
                  {!disabledEdit && (
                    <GenerateTopicPopover
                      tool={toolInfo}
                      onTopicGenerated={handleTopicGenerated}
                      showCategorySelect={false}
                    />
                  )}
                </div>
                <AntForm
                  form={speakingForm}
                  className="speaking-topic__input"
                  disabled={speakingId || recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING}
                >
                  <AntForm.Item
                    name="topic"
                    rules={[{ ...RULE.REQUIRED, lang: "PLEASE_ENTER_TOPIC" }]}>
                    <Input.TextArea
                      autoSize={{
                        minRows: 1,
                      }}
                      disabled={disabledEdit}
                      placeholder={t("YOUR_TOPIC")}
                      onFocus={() => dispatch(actions.trackCustomClick(TRACKING_ACTIONS.INPUT_TOPIC))}
                    />
                  </AntForm.Item>
                </AntForm>
                {speakingId && feedbackData?.tag && <div
                  className="speaking-topic__tag"
                >
                  <span className="speaking-topic__tag-label">{t("TAG")}</span>
                  <TagItem tag={feedbackData?.tag} />
                </div>}
              </div>


              <div id="js-speaking-audio-input" className="speaking-section">
                <div className="speaking-section__title">
                  <span className="speaking-section__title-text">
                    {t("AUDIO_INPUT")}
                  </span>
                  {!isShowFeedback && <span className="speaking-section__title-action">
                    <SpeakingAudioInputAction />
                  </span>}
                </div>

                <div className={clsx("speaking-record", { "speaking-record__centered": isShowFeedback })}>
                  {!isShowFeedback && <>
                    {audioInputType === CONSTANT.RECORD
                      ? <SpeakingRecorder />
                      : <UploadRecordedAudio />}
                  </>}
                  <SpeakingErrors />
                </div>

                <div className="speaking-section__divider" />

                <SpeakingResult />

                {!isShowFeedback && <SpeakingChart />}
              </div>

              <SpeakingRecordedAudioPlayer />

              <SpeakingTooShort />

              <SpeakingGetFeedback />


            </div>

            {isShowFeedback && <div className="speaking-feedback-container">
              <SpeakingFeedback />
            </div>}
          </div>

          <PopupUploadAudio />

        </div>
        {isShowFeedbackModal && <FeedbackModal toolId={projectData?.content[0]?.toolId?._id} />}
      </Loading>
    </SpeakingContext.Provider>


    {!!projectData && <Share
      isShowModal={isShowModalShare}
      handleCancel={onShareProjectAction}
      queryAccess={{ projectId: projectData._id }}
      name={projectData.projectName}
      owner={projectData.ownerId}
      workspaceId={projectData.workspaceId}
      disabledEdit
    />}
  </>;
}

const useSpeaking = () => useContext(SpeakingContext);

const mapStateToProps = (state) => ({
  studentTools: state.tool.studentTools,
});
const ConnectedCreateSpeaking = connect(mapStateToProps, {})(Speaking);
export { ConnectedCreateSpeaking as Speaking, useSpeaking };