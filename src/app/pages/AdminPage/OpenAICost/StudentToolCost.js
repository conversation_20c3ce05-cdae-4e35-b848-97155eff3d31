import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Card, Row, Col, Statistic, Empty, Tooltip } from "antd";
import { DollarOutlined, RiseOutlined, ToolOutlined, NumberOutlined, InfoCircleOutlined } from "@ant-design/icons";

import Loading from "@component/Loading";

function StudentToolCost({ queryParams, activeTab, studentToolCostData, isLoading }) {
  const { t, i18n } = useTranslation();

  const formatCurrency = useMemo(() => {
    return (value) => {
      const US = new Intl.NumberFormat("en-US", {
        currency: "USD",
        maximumFractionDigits: 6,
      });
      return US.format(value || 0);
    };
  }, []);

  // Calculate total statistics
  const totalStats = useMemo(() => {
    if (!studentToolCostData || studentToolCostData.length === 0) return null;

    const totalSubmits = studentToolCostData.reduce((sum, item) => sum + item.numberSubmit, 0);
    const totalCost = studentToolCostData.reduce((sum, item) => sum + item.totalCost, 0);
    const averageCost = totalSubmits > 0 ? totalCost / totalSubmits : 0;

    return {
      totalSubmits,
      totalCost,
      averageCost,
      toolCount: studentToolCostData.length
    };
  }, [studentToolCostData]);

  // Sort data by total cost (descending)
  const sortedData = useMemo(() => {
    if (!studentToolCostData) return [];
    return [...studentToolCostData].sort((a, b) => b.totalCost - a.totalCost);
  }, [studentToolCostData]);

  if (activeTab !== "4") return null;

  return (
    <div className="student-tool-cost-container">
      <Card className="student-tool-cost-summary-card">
        <Loading active={isLoading} transparent>
          {!totalStats ? (
            <Empty description={t("NO_STUDENT_TOOL_COST_DATA")} />
          ) : (
            <>
              <h2 className="student-tool-cost-title">{t("STUDENT_TOOLS_COST_SUMMARY")}</h2>
              <Row gutter={[24, 24]} className="student-tool-cost-summary-row">
                <Col xs={24} sm={6}>
                  <Card className="student-tool-stat-card tools-card">
                    <Statistic
                      title={t("TOTAL_TOOLS")}
                      value={totalStats.toolCount}
                      prefix={<ToolOutlined />}
                    />
                    <div className="stat-description">{t("TOTAL_TOOLS_DESCRIPTION")}</div>
                  </Card>
                </Col>
                <Col xs={24} sm={6}>
                  <Card className="student-tool-stat-card submits-card">
                    <Statistic
                      title={t("TOTAL_SUBMITS")}
                      value={totalStats.totalSubmits}
                      prefix={<NumberOutlined />}
                    />
                    <div className="stat-description">{t("TOTAL_SUBMITS_DESCRIPTION")}</div>
                  </Card>
                </Col>
                <Col xs={24} sm={6}>
                  <Card className="student-tool-stat-card cost-card">
                    <Statistic
                      title={t("TOTAL_COST")}
                      value={`$${formatCurrency(totalStats.totalCost)}`}
                      prefix={<DollarOutlined />}
                    />
                    <div className="stat-description">{t("TOTAL_COST_DESCRIPTION")}</div>
                  </Card>
                </Col>
                <Col xs={24} sm={6}>
                  <Card className="student-tool-stat-card average-card">
                    <Statistic
                      title={t("AVERAGE_COST_PER_SUBMIT")}
                      value={`$${formatCurrency(totalStats.averageCost)}`}
                      prefix={<RiseOutlined />}
                    />
                    <div className="stat-description">{t("AVERAGE_COST_PER_SUBMIT_DESCRIPTION")}</div>
                  </Card>
                </Col>
              </Row>

              <h2 className="student-tool-cost-title">{t("STUDENT_TOOLS_COST_BREAKDOWN")}</h2>
              <div className="student-tool-cost-details">
                <div className="tool-cost-tabs">
                  <div className="tab-header">
                    <div className="tab active">{t("TOP_TOOLS_BY_COST")}</div>
                  </div>

                  <div className="tab-content">
                    <div className="tools-list">
                      {sortedData.slice(0, 10).map((tool, index) => {
                        // Calculate percentage of total cost
                        const percentOfTotal = (tool.totalCost / totalStats.totalCost) * 100;
                        const percentFormatted = percentOfTotal.toFixed(1);

                        return (
                          <Card className="tool-list-item" key={index} hoverable>
                            <div className="tool-rank">{index + 1}</div>
                            <div className="tool-info">
                              <div className="tool-name-container">
                                <h3 className="tool-name" title={tool.toolName}>{tool.toolName}</h3>
                                <div className="tool-percent">{percentFormatted}%</div>
                              </div>

                              <div className="tool-stats-row">
                                <div className="tool-stat">
                                  <NumberOutlined className="stat-icon submits-icon" />
                                  <span>{tool.numberSubmit.toLocaleString()} {t("SUBMITS")}</span>
                                </div>
                                <div className="tool-stat">
                                  <DollarOutlined className="stat-icon cost-icon" />
                                  <span className="cost-value">${formatCurrency(tool.totalCost)}</span>
                                </div>
                                <div className="tool-stat">
                                  <RiseOutlined className="stat-icon average-icon" />
                                  <Tooltip title={t("AVERAGE_COST_PER_SUBMIT")}>
                                    <span className="average-value">
                                      ${formatCurrency(tool.numberSubmit > 0 ? tool.totalCost / tool.numberSubmit : 0)} {t("AVG")}
                                    </span>
                                  </Tooltip>
                                </div>
                              </div>

                              <div className="tool-progress">
                                <div
                                  className="progress-bar"
                                  style={{ width: `${Math.min(100, percentOfTotal)}%` }}
                                ></div>
                                <div className="progress-bg"></div>
                              </div>
                            </div>
                          </Card>
                        );
                      })}

                      {sortedData.length > 10 && (
                        <div className="more-tools-info">
                          <InfoCircleOutlined />
                          <span>{t("SHOWING_TOP_10_OF")} {sortedData.length} {t("TOOLS")}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </Loading>
      </Card>
    </div>
  );
}

export default StudentToolCost;
