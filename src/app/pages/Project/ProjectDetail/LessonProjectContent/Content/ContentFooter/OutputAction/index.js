import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { Collapse, Dropdown, Form, Input, Popconfirm, Popover } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

import useWindowDimensions from "@common/windowDimensions";
import { toast } from "@component/ToastProvider";
import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";
import { useProject } from "@app/pages/Project";

import AntButton from "@component/AntButton";
import { DownloadProject } from "@app/pages/Project/DownloadProject";
import NextTool from "./NextTool";
import GoogleForm from "./GoogleForm";
import ActionPopover from "@app/pages/Project/ProjectDetail/ActionPopover";

import { BUTTON, CONSTANT, VISIBLE_TOOL } from "@constant";
import { cloneObj } from "@common/functionCommons";
import { updateResponse } from "@services/Response";
import { saveSample } from "@services/Sample";

import Rotate from "@component/SvgIcons/Rotate";
import Copy from "@component/SvgIcons/Copy";
import Edit from "@component/SvgIcons/Edit";
import Trash from "@component/SvgIcons/Trash";
import Like from "@component/SvgIcons/Dislike";
import Download from "@component/SvgIcons/Download";
import Dislike from "@component/SvgIcons/Unlike";
import MoreHorizontal from "@component/SvgIcons/MoreHorizontal";
import PaperBold from "@component/SvgIcons/Paper/PaperBold";

import * as tool from "@src/ducks/tool.duck";

import "./OutputAction.scss";


function OutputAction({
                        listToolAvailable,
                        listAllTool,
                        user,
                        showNextTool = true,
                        allowSaveGuide = true,
                        ...props
                      }) {
  const { t } = useTranslation();
  const { projectData, setProjectContentData, addContentBlock, isAllowEditing, subSelectedIndex } = useProject();
  
  const content = props?.content || useContent()?.content;
  const inputData = props?.inputData || useContent()?.inputData;
  const toolInfo = props?.toolInfo || useContent()?.toolInfo;
  const responseSelected = props?.responseSelected || useContent()?.responseSelected;
  const setEditing = props?.setEditing || useContent()?.setEditing;
  const instructionSelected = props?.instructionSelected || useContent()?.instructionSelected;
  
  const { width } = useWindowDimensions();
  
  const [actionActive, setActionActive] = useState(null);
  const [isCopied, setCopied] = useState(false);
  const [isShowModalExportPDF, setShowModalExportPDF] = useState(false);
  const [ratingLoading, setRatingLoading] = useState(null);
  
  const inputType = toolInfo?.inputType;
  const outputType = responseSelected?.outputType;
  const rating = responseSelected?.rating;
  
  useEffect(() => {
    if (isCopied) {
      setTimeout(() => {
        setCopied(false);
      }, 1000);
    }
  }, [isCopied]);
  
  const isShowActionMobile = useMemo(() => width < 1536, [width]);
  
  const handleClickInputAction = useCallback((tool) => {
    if (!addContentBlock || !inputData) return;
    const inputTemp = cloneObj(inputData);
    delete inputTemp.instructionId;
    addContentBlock({ toolId: tool._id, inputType: tool.inputType, inputData: inputTemp });
  }, [inputData]);
  
  const handleClickOutPutAction = useCallback((tool) => {
    const inputData = {
      text: responseSelected?.plaintext || responseSelected?.output?.text,
    };
    if (tool.inputType === "html") inputData.text = responseSelected?.output?.html;
    if (tool.inputType === "audio" && responseSelected?.output?.audio) {
      inputData.audioId = responseSelected?.output?.audio?.audioFileId;
      inputData.cutEnd = responseSelected?.output?.audio?.duration;
      inputData.cutStart = 0;
    }
    if (typeof addContentBlock === "function") {
      addContentBlock({
        toolId: tool._id,
        inputType: tool.inputType,
        inputData,
      });
    }
  }, [responseSelected]);
  
  const toolAvailable = useMemo(() => {
    return listToolAvailable.filter(item => item.visible !== VISIBLE_TOOL.developing.value && item.type === projectData.type);
  }, [listToolAvailable, toolInfo]);
  
  const [inputNextTool, outputNextTool] = useMemo(() => {
    return [
      toolAvailable.filter((item) => item.inputType === toolInfo?.inputType && item._id !== toolInfo?._id),
      toolAvailable.filter((item) => item.inputType === responseSelected?.outputType),
    ];
  }, [toolAvailable, toolInfo, responseSelected]);
  
  const collapseItems = useMemo(() => {
    return [
      {
        key: CONSTANT.INPUT,
        children: <NextTool toolData={inputNextTool} type={inputType} onClick={handleClickInputAction} />,
        showArrow: false,
      },
      {
        key: CONSTANT.OUTPUT,
        children: <NextTool toolData={outputNextTool} type={outputType} onClick={handleClickOutPutAction} />,
        showArrow: false,
      },
    ];
  }, [inputType, outputType, inputNextTool, outputNextTool, handleClickInputAction, handleClickOutPutAction]);
  
  const copyOutput = () => {
    if (!isCopied && responseSelected.plaintext) {
      navigator.clipboard.writeText(responseSelected.plaintext);
      setCopied(true);
    }
  };
  
  const handleEditResponse = () => {
    setEditing(true);
  };
  
  const handleDeleteResponse = () => {
    props.deleteOutput();
  };
  
  const handleRating = async (newRating) => {
    const rating = newRating === responseSelected.rating ? null : newRating; //if re rating then set null
    const dataRequest = { _id: responseSelected._id, rating };
    setRatingLoading(newRating);
    
    const apiResponse = await updateResponse(dataRequest);
    if (apiResponse) {
      setProjectContentData(prevState => {
        return cloneObj(prevState).map(state => {
          if (state._id === content._id) {
            state.responses = state.responses?.map(response => {
              if (response._id === apiResponse._id) {
                response.rating = apiResponse.rating;
              }
              return response;
            });
          }
          return state;
        });
      });
    }
    setRatingLoading(null);
  };
  
  const handleCollapse = (key = null) => {
    setActionActive(prevState => prevState === key ? null : key);
  };
  
  const onSaveExample = async () => {
    const apiResponse = await saveSample({ contentId: content?._id });
    if (apiResponse) {
      if (!toolInfo?.existGuide) {
        props.setTool([...listAllTool].map(tool => {
          if (tool._id === toolInfo._id) {
            tool.existGuide = true;
          }
          return tool;
        }));
      }
      
      toast.success("GUIDE_SAVE_SUCCESS");
    }
  };
  
  
  function onOpenModalExportPDF() {
    setShowModalExportPDF(true);
  }
  
  function onCloseModalExportPDF() {
    setShowModalExportPDF(false);
  }
  
  function renderMobileAction() {
    if (!isShowActionMobile) return;
    
    const renderMenuSaveGuide = () => {
      return <Popconfirm
        rootClassName="confirm-save-guide confirm-save-guide__mobile"
        placement="left"
        icon={null}
        title={`${t(toolInfo.existGuide ? "REPLACE_GUIDE" : "SAVE_AS_GUIDE")}?`}
        onConfirm={onSaveExample}
        okText={t("SAVE")}
        cancelText={t("CANCEL")}
        trigger="hover"
        cancelButtonProps={{ className: "ant-btn-light-navy ant-btn-xsmall" }}
        okButtonProps={{ className: "ant-btn-deep-navy ant-btn-xsmall" }}
      >
        <div className="save-as-example__label"
             onClick={(e) => e.stopPropagation()}
        >
          {t("SAVE_AS_GUIDE")}
        </div>
      </Popconfirm>;
    };
    
    
    return <Dropdown
      trigger={"click"}
      destroyPopupOnHide
      placement="topRight"
      menu={{
        items: [
          outputType !== "audio" &&
          { key: "COPY", label: t("COPY"), icon: <Copy />, onClick: copyOutput },
          isAllowEditing && outputType !== "audio" &&
          { key: "EDIT", label: t("EDIT"), icon: <Edit />, onClick: handleEditResponse },
          isAllowEditing && { key: "DELETE", label: t("DELETE"), icon: <Trash />, onClick: handleDeleteResponse },
          user.isSystemAdmin && allowSaveGuide && {
            key: "SAVE_AS_GUIDE",
            label: renderMenuSaveGuide(),
            icon: <PaperBold />,
          },
        ],
      }}
    >
      <AntButton
        size="large"
        type={BUTTON.DEEP_NAVY}
        icon={<MoreHorizontal />}
      />
    </Dropdown>;
  }
  
  if (!responseSelected || responseSelected.typingEffect) return null;
  
  //if (isPreviewProject) {
  //  return <div className="content-action">
  //    <div className="content-action__result-tool">
  //      <div className="result-tool__action">
  //        <GoogleForm />
  //        <ActionPopover content="COPY" icon={<Copy />} onClick={copyOutput} />
  //        <ActionPopover content="DOWNLOAD" icon={<Download />} onClick={onOpenModalExportPDF} />
  //      </div>
  //    </div>
  //  </div>;
  //}
  
  return <div className="content-action">
    {instructionSelected?.showAdditionalRequest && !toolInfo.isDisable && isAllowEditing && <Form
      layout="vertical"
      className="content-action__regenerate"
      onFinish={props.handleRegenerate}
    >
      <Form.Item label={t("REQUESTS")} name="additionalRequest" className="regenerate__input">
        <Input size="large" placeholder={t("ENTER_ADDITIONAL_REQUEST")} />
      </Form.Item>
      <AntButton
        size="large"
        type={BUTTON.DEEP_NAVY}
        icon={<Rotate />}
        className="regenerate__submit"
        htmlType="submit"
      >
        {t("REGENERATE")}
      </AntButton>
    </Form>}
    
    <div className="content-action__result-tool">
      <div className="result-tool__vote">
        <AntButton
          size="large"
          icon={ratingLoading === CONSTANT.LIKE ? <LoadingOutlined /> : <Like />}
          type={rating === CONSTANT.LIKE ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
          onClick={() => handleRating(CONSTANT.LIKE)}
          disabled={!!ratingLoading}
        />
        <AntButton
          size="large"
          icon={ratingLoading === CONSTANT.DISLIKE ? <LoadingOutlined /> : <Dislike />}
          type={rating === CONSTANT.DISLIKE ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
          onClick={() => handleRating(CONSTANT.DISLIKE)}
          disabled={!!ratingLoading}
        />
      </div>
      <div className="result-tool__action">
        <GoogleForm responseSelected={responseSelected} />
        
        {!isShowActionMobile && <>
          {outputType !== "audio" && <ActionPopover content="COPY" icon={<Copy />} onClick={copyOutput} />}
          {isAllowEditing && <>
            {outputType !== "audio" && <ActionPopover content="EDIT" icon={<Edit />} onClick={handleEditResponse} />}
            <ActionPopover content="DELETE" icon={<Trash />} onClick={handleDeleteResponse} />
          </>}
        </>}
        
        {/*<ActionPopover content="DOWNLOAD" icon={<Download />} onClick={downloadOutput} />*/}
        <ActionPopover content="DOWNLOAD" icon={<Download />} onClick={onOpenModalExportPDF} />
        
        
        {user.isSystemAdmin && !isShowActionMobile && allowSaveGuide && <Popconfirm
          rootClassName="confirm-save-guide"
          placement="topRight"
          icon={null}
          title={`${t(toolInfo.existGuide ? "REPLACE_GUIDE" : "SAVE_AS_GUIDE")}?`}
          onConfirm={onSaveExample}
          okText={t("SAVE")}
          cancelText={t("CANCEL")}
          trigger="hover"
          cancelButtonProps={{ className: "ant-btn-light-navy ant-btn-xsmall" }}
          okButtonProps={{ className: "ant-btn-deep-navy ant-btn-xsmall" }}
        >
          <AntButton size="large" icon={<PaperBold />} />
        </Popconfirm>}
        
        
        {renderMobileAction()}
      </div>
    </div>
    
    {showNextTool && <div className="content-action__next-tool">
      <div className="next-tool__panel">
        {!!inputNextTool.length && <AntButton
          size="large"
          type={actionActive === CONSTANT.INPUT ? BUTTON.DEEP_PURPLE : BUTTON.WHITE}
          onClick={() => handleCollapse(CONSTANT.INPUT)}
        >
          {t("TOOL_SUGGESTIONS_FROM_INPUT")}
        </AntButton>}
        
        {!!outputNextTool.length && <AntButton
          size="large"
          type={actionActive === CONSTANT.OUTPUT ? BUTTON.DEEP_PURPLE : BUTTON.WHITE}
          onClick={() => handleCollapse(CONSTANT.OUTPUT)}
        >
          {t("TOOL_SUGGESTIONS_FROM_RESULTS")}
        </AntButton>}
      </div>
      <div className="next-tool__content">
        <Collapse
          items={collapseItems}
          activeKey={[actionActive]}
          ghost
        />
      </div>
    </div>}
    
    <DownloadProject
      open={isShowModalExportPDF}
      onCancel={onCloseModalExportPDF}
      fileName={content?.title}
      contentIdInitial={content._id}
      downloadType={CONSTANT.CONTENT}
      indexSelected={subSelectedIndex}
    />
  </div>;
}

function mapStateToProps(store) {
  const { listToolAvailable, listAllTool } = store.tool;
  const { user } = store.auth;
  return { listToolAvailable, listAllTool, user };
}

const mapDispatchToProps = {
  ...tool.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(OutputAction);