import React, { useEffect, useState } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { useTranslation } from "react-i18next";
import { Progress } from "antd";

import Loading from "@src/app/component/Loading";
import ModalPreviewPdf from "./ModalPreviewPdf";

import { UPLOAD_STATUS } from "@constant";

import Maximize from "@src/asset/icon/maximize-24.svg";

import "./PreviewPdf.scss";

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.js",
  import.meta.url,
).toString();

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

function PreviewPdf({ file, startPage, endPage, uploadData, uploadStatus }) {

  const { t } = useTranslation();

  const [isLoading, setLoading] = useState(false);
  const [openPreview, setOpenPreview] = useState(false);
  const [totalPages, setTotalPages] = useState(0);

  useEffect(() => {
    if (uploadData?.file) {
      setLoading(true);
    }
  }, [uploadData?.file]);

  useEffect(() => {
    if (!uploadData?.file && file) {
      setLoading(true);
    }
  }, [file, uploadData?.file]);

  function onLoadSuccess({ numPages }) {
    setLoading(false);
    setTotalPages(numPages);
  }

  const renderSelectedPages = () => {
    if ((!startPage || !endPage) && totalPages) return t("SELECTED_PAGES").format(1, totalPages);;
    if (endPage !== startPage) return t("SELECTED_PAGES").format(startPage, endPage);
    return t("SELECTED_PAGE").format(startPage);
  }

  const onPreviewPdf = () => {
    setOpenPreview(pre => !pre);
  }

  const showPreviewBackdrop = (uploadData?.file || file) && !isLoading && (!uploadStatus?.status || uploadStatus?.status === UPLOAD_STATUS.SUCCESS);

  return <Loading active={isLoading} fixedMiddleScreen className="preview-pdf-container">
    <div className="preview-pdf-content">
      <Document
        file={uploadData?.file || file}
        onLoadSuccess={onLoadSuccess}
        onLoadError={() => setLoading(false)}
        loading=""
      >
        <Page pageNumber={1} loading={null} />

        {!file && uploadStatus?.status === UPLOAD_STATUS.UPLOADING && <div className="preview-pdf__progress-wrapper">
          <Progress
            percent={uploadStatus?.percent}
            className="preview-pdf__progress"
            trailColor="#FFFFFFCC"
            strokeLinecap="butt"
          />
        </div>}
      </Document>
      {!isLoading && <div className="preview-pdf__select-page">{renderSelectedPages()} </div>}
    </div>

    {showPreviewBackdrop && <div className="preview-pdf-backdrop" onClick={onPreviewPdf} >
      <img src={Maximize} alt="" />
    </div>}

    <ModalPreviewPdf openPreview={openPreview} onPreviewPdf={onPreviewPdf} file={uploadData?.file || file} />
  </Loading>;
}

export default PreviewPdf;