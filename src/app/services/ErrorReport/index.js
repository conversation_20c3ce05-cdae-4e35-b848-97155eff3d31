import { API } from "@api";
import { createBase, deleteBase, getAllPaginationBase, updateBase, getDetailBase } from "@services/Base";

/**
 * <PERSON><PERSON><PERSON> danh sách error reports với pagination
 * @param {Object} paging - Thông tin phân trang
 * @param {Object} query - Query parameters cho filter/search
 * @param {Array} searchFields - Các trường để search
 * @returns {Promise} API response
 */
export function getAllErrorReports(paging, query, searchFields = []) {
  const queryObj = { ...query };
  queryObj.sort = queryObj.sort || "-createdAt";

  // Handle time filter and convert to createdAt range
  if (queryObj.time) {
    const now = new Date();
    switch (queryObj.time) {
      case "week":
        const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
        startOfWeek.setHours(0, 0, 0, 0);
        queryObj.createdAtFrom = Math.floor(startOfWeek.getTime() / 1000);
        queryObj.createdAtTo = Math.floor(Date.now() / 1000);
        break;
      case "month":
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        queryObj.createdAtFrom = Math.floor(startOfMonth.getTime() / 1000);
        queryObj.createdAtTo = Math.floor(Date.now() / 1000);
        break;
      // For custom, fromDate and toDate should already be set
    }
    delete queryObj.time;
  }

  // Convert fromDate/toDate to createdAtFrom/createdAtTo for API
  // This ensures the backend receives the correct field names for date filtering
  if (queryObj.fromDate) {
    queryObj.createdAtFrom = queryObj.fromDate;
    delete queryObj.fromDate;
  }

  if (queryObj.toDate) {
    queryObj.createdAtTo = queryObj.toDate;
    delete queryObj.toDate;
  }

  // Debug log to verify query conversion (remove in production)
  if (process.env.NODE_ENV === 'development' && (queryObj.createdAtFrom || queryObj.createdAtTo)) {
    console.log('ErrorReport API Query:', {
      createdAtFrom: queryObj.createdAtFrom,
      createdAtTo: queryObj.createdAtTo,
      originalQuery: query
    });
  }

  return getAllPaginationBase(API.ERROR_REPORTS, paging, queryObj, searchFields, ["userId"]);
}

/**
 * Lấy chi tiết một error report
 * @param {string} id - ID của error report
 * @returns {Promise} API response
 */
export function getErrorReportDetail(id) {
  return getDetailBase(API.ERROR_REPORTS_ID, id, ["userId", "imageIds", "videoId"], true);
}

/**
 * Cập nhật trạng thái error report
 * @param {Object} data - Dữ liệu cập nhật (bao gồm _id)
 * @returns {Promise} API response
 */
export function updateErrorReport(data) {
  return updateBase(API.ERROR_REPORTS_ID, data, ["userId"], true, true);
}

/**
 * Xóa error report (soft delete)
 * @param {string} id - ID của error report
 * @returns {Promise} API response
 */
export function deleteErrorReport(id) {
  return deleteBase(API.ERROR_REPORTS_ID, id, [], true, true);
}

/**
 * Tạo error report mới (cho user submit)
 * @param {Object} data - Dữ liệu error report
 * @returns {Promise} API response
 */
export function createErrorReport(data) {
  return createBase(API.ERROR_REPORTS, data, [], true, true);
}
