.persona-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .persona-info-card,
  .persona-search-card,
  .persona-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .persona-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .persona-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .persona-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create-persona {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  // Search form styles
  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  // Table styles
  .persona-actions {
    display: flex;
    flex-direction: row;
    gap: 8px;
    justify-content: center;
  }

  .persona-table {
    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
    }

    .persona-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .persona-name {
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      font-size: 14px;
    }

    .template-persona {
      gap: 8px;
      display: flex;
      flex-direction: column;

      .template-persona-item {
        background-color: var(--background-light-background-grey);
        padding: 4px 8px;
        border-radius: 4px;
        display: inline-block;
        margin-right: 4px;
        margin-bottom: 4px;
        font-size: 13px;
        color: var(--typo-colours-primary-black);
        border: 1px solid var(--background-light-background-3);
      }
    }
  }

  // Tool styles
  .tool-persona {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--background-light-background-grey);

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }

  .persona-left-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    min-width: 90px;

    .tool-category {
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      font-size: 13px;
    }
  }

  .persona-right-item {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    text-align: start;

    .tool-persona-item {
      background-color: var(--background-light-background-grey);
      padding: 4px 8px;
      border-radius: 4px;
      display: inline-block;
      margin-right: 4px;
      margin-bottom: 4px;
      font-size: 13px;
      color: var(--typo-colours-primary-black);
      border: 1px solid var(--background-light-background-3);
    }
  }

  .tool-persona-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  // Responsive styles
  @media (max-width: 768px) {
    .persona-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .btn-create-persona {
        width: 100%;
        justify-content: center;
      }
    }

    .search-buttons {
      margin-top: 16px;
      width: 100%;
      justify-content: space-between;
    }

    .tool-persona {
      flex-direction: column;
      align-items: flex-start;
    }

    .persona-left-item {
      margin-bottom: 8px;
    }
  }
}
