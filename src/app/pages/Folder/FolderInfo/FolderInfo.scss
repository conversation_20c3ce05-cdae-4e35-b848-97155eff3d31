.folder-info-container {
  .divider-content-folder-info {
    margin: 16px 0;
    border-color: var(--background-light-background-grey);
  }

  overflow-x: auto;
  background-color: #ffffff;
  box-shadow: var(--shadow-level-2);
  border-radius: 8px;
  padding: 24px 24px;
  gap: 16px;

  .folder-info-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 8px;
    word-wrap: break-word;
    flex: 1;

    &__left {
      display: flex;
      flex-direction: row;
      gap: 8px;
    }

    &__name {
      font-size: 24px;
      line-height: 30px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      height: 61px;

      &__label {
        font-weight: 600;
      }

      &__text {
        word-break: break-word;

        &:before {
          content: ' ';
        }

        font-weight: 400;
      }
    }

    &__btn-actions {
      padding: 0;
      margin: 0;
      border: none !important;
    }

    &__right {
      cursor: pointer;
      display: flex;
      align-items: center;
      flex-direction: column;
      gap: 8px;

      .star-container {
        opacity: 1;
      }

      svg {
        width: 16px !important;
        height: 16px !important;
      }
    }

    .dropdown-actions {
      width: 16px;
      height: 16px;
      min-width: 16px;
      margin: 8px 0;
    }
  }

  .folder-info__description {
    color: var(--typo-colours-primary-black);
    text-align: justify;
    font-size: 14px;
    margin-top: 8px;
  }

  .folder-info__detail {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .folder-info__detail-title {
      color: #2196f3;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }
  }

  .folder-detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .folder-detail-label {
      font-size: 13px;
      color: var(--typo-colours-support-blue-light);
    }

    .folder-detail-value {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;

      &.folder-detail-value__workspace-name {
        cursor: pointer;
        padding: 8px 24px;
        margin: 0 -24px;
        transition-duration: 0s;
        color: #000000;

        &:hover:not(.disable-link) {
          background: var(--primary-colours-blue-light-1);
        }

        &:active:not(.disable-link) {
          background: var(--primary-colours-blue);
          color: var(--white);

          svg path {
            fill: #FFFFFF;
          }
        }

        &.disable-link {
          cursor: not-allowed;
          color: #C2C1C1;

          svg path {
            fill: #C2C1C1;
          }
        }
      }
    }
  }

}


.menu-dropdown-actions-folder-info {
  margin-right: -24px !important;
}