import React, { useEffect, useRef, useState } from 'react';
import "./ResultsPart.scss";
import "@src/app/pages/Student/Speaking/SpeakingResult/SpeakingResult.scss"
import Paper_Icon from '@src/asset/icon/Paper.svg';
import Warning_icon from "@src/asset/icon/warning.svg"
import Danger_Circle from '@src/asset/icon/Danger-Circle.svg'
import { useTranslation } from 'react-i18next';
import Markdown from 'react-markdown';
import ScoreBoard from '../ScoreBoard/ScoreBoard';
import AudioPlayer from './AudioPlayer/AudioPlayer';
import AntButton from '@src/app/component/AntButton';
import VolumeUp from '@src/app/component/SvgIcons/Volume/VolumeUp';
import EarIcon from '@src/app/component/SvgIcons/EarIcon';
import { Popover, Tooltip } from 'antd';
import clsx from 'clsx';
import ReactPlayer from 'react-player';
import { API } from '@api';

const ResultPart = ({ answers1, answers2, answers3, selectedPart, questions, questions2, questions3 }) => {
  const { t } = useTranslation();

  const playerUserRef = useRef(null);
  const playerIpaRef = useRef(null);

  const [wordIpa, setWordIpa] = useState('');

  const [audioFileId, setAudioFileId] = useState(''); // URL của file audio người dùng đang được phát
  const [playRequest, setPlayRequest] = useState(null); // Lưu trữ yêu cầu phát: { currentTime, duration }

  const answers = selectedPart === 1 ? answers1 : selectedPart === 2 ? answers2 : answers3;
  const questionsArray = selectedPart === 1 ? questions : selectedPart === 2 ? questions2 : questions3;

  const handleCalculateAverageScore = (a, b, c, d) => {
    const average = (a + b + c + d) / 4;
    const roundedAverage = Math.round(average * 2) / 2;
    return roundedAverage;
  }

  useEffect(() => {
    if (wordIpa) {
      playerIpaRef.current.getInternalPlayer()?.play();
    } else {
      playerIpaRef?.current?.getInternalPlayer()?.pause();
    }
  }, [wordIpa]);

  // Hàm này sẽ được gọi khi người dùng nhấn nút EarIcon
  const onPlayUserPronounce = (answerAudioUrl, currentTime, duration) => {
    setAudioFileId(answerAudioUrl); // Cập nhật URL cho ReactPlayer
    setPlayRequest({ currentTime, duration }); // Lưu lại yêu cầu phát
  };

  // useEffect để xử lý việc phát âm thanh khi audioFileId hoặc playRequest thay đổi
  useEffect(() => {
    if (audioFileId && playRequest && playerUserRef.current && playerUserRef.current.getInternalPlayer()) {
      const { currentTime, duration } = playRequest;
      // Đảm bảo player đã sẵn sàng với audioFileId mới trước khi seekTo và play
      playerUserRef.current.seekTo(currentTime / 1e7, "seconds");
      playerUserRef.current.getInternalPlayer().play();
      setTimeout(() => {
        playerUserRef.current?.getInternalPlayer()?.pause();
      }, duration / 1e4);
      setPlayRequest(null); // Reset yêu cầu sau khi đã xử lý
    }
  }, [audioFileId, playRequest]); // Chạy lại khi audioFileId hoặc playRequest thay đổi

  const onPlayIpaPronounce = (word) => {
    setWordIpa(word);
    playerIpaRef?.current?.seekTo(0);
  };

  const renderPhonemeText = (score) => {
    let className = '';
    let text = '';
    switch (true) {
      case score >= 90:
        className = "phoneme__excellent";
        text = t("EXCELLENT");
        break;
      case score >= 60 && score < 90:
        className = "phoneme__almost";
        text = t("ALMOST");
        break;
      default:
        className = "phoneme__try-again";
        text = t("TRY_AGAIN");
    };
    return <span className={className}>{`${text}`}</span>;
  }

  const renderPhonemeClassName = (score) => {
    let className = '';
    switch (true) {
      case score >= 90:
        className = "phoneme__excellent";
        break;
      case score >= 60 && score < 90:
        className = "phoneme__almost";
        break;
      default:
        className = "phoneme__try-again";
    };
    return className;
  }

  return (
    <div className="result-part">
      <div className="result-part__header">
        <img
          src={Paper_Icon}
          alt=""
        />
        <span>
          {selectedPart === 0 ? (
            <>{t('OVERALL')}</>
          ) : selectedPart === 1 ? (
            <>{t('PART_1_MENU')}: {t('INTRODUCTION_INTERVIEW_MENU')}</>
          ) : selectedPart === 2 ? (
            <>{t('PART_2_MENU')}: {t('INDIVIDUAL_LONG_TURN_MENU')}</>
          ) : (
            <>{t('PART_3_MENU')}: {t('TWO_WAY_DISCUSSION_MENU')}</>
          )}
        </span>
      </div>
      {questionsArray?.map((question, index) => {
        // Tìm câu trả lời tương ứng cho câu hỏi hiện tại
        // Giả định rằng answer.questionId._id khớp với question._id
        const correspondingAnswer = answers?.find(
          (ans) => ans.questionId?._id === question._id
        );

        return (
          <div key={question._id} className="result-part__question">
            <div className="result-part__question__name">
              {t("QUESTION")} {question.questionIndex}: {question.questionText}
            </div>

            {correspondingAnswer ? (
              <>
                {(() => {
                  // Di chuyển việc khởi tạo biến vào đây để chỉ thực hiện khi có correspondingAnswer
                  const vocabularyScore = correspondingAnswer.feedback.vocabularyScore;
                  const grammarScore = correspondingAnswer.feedback.grammarScore;
                  const pronScore = correspondingAnswer.feedback.pronScore;
                  const fluencyScore = correspondingAnswer.feedback.fluencyScore;

                  const words = correspondingAnswer.results?.flatMap((item) =>
                    item.NBest?.flatMap((nBestItem) => nBestItem?.Words)
                  );

                  return (
                    <>
                      {/* Show Score */}
                      <div className="result-part__body">
                        <div className="result-part__body__show">
                          <div className="result-part__body__show__box">
                            <div className="result-part__body__show__box__result">
                              {handleCalculateAverageScore(vocabularyScore, grammarScore, pronScore, fluencyScore)}/9
                            </div>
                          </div>
                          <div className="result-part__body__show__criteria">
                            <ScoreBoard resultSummary={correspondingAnswer.feedback} />
                          </div>
                        </div>
                      </div>

                      {/* Audio */}
                      <div className="result-part__audio">
                        <AudioPlayer
                          src={correspondingAnswer.audioUrl}
                          audioFileId={correspondingAnswer.audioFileId}
                        />
                      </div>

                      {/* ✅ Render từng từ với Popover */}
                      <div className="speaking-result flex-col gap-2">
                        <p className="result-part__body__feedback__header">{t("TRANSCRIPT")}:</p>
                        <div className='result-part__body__feedback__signage'>
                          <div className='result-part__body__feedback__signag__ingredient'>
                            <div className='w-2 h-2' style={{ backgroundColor: "#ff070c33" }}></div>
                            <span>{t("MISPRONUNCIATIONS")}</span>
                            <Tooltip
                              align={{ offset: [-11.5, -6] }}
                              overlayClassName="speaking-error__info-tooltip"
                              placement="topLeft"
                              title={t('MISPRONUNCIATION_DESC')}>
                              <img src={Danger_Circle} alt="" />
                            </Tooltip>
                          </div>
                          <div className='result-part__body__feedback__signag__ingredient'>
                            <div className='w-2 h-2' style={{ backgroundColor: "#c658fb" }}></div>
                            <span>{t("UNEXPECTED_BREAK")}</span>
                            <Tooltip
                              align={{ offset: [-11.5, -6] }}
                              overlayClassName="speaking-error__info-tooltip"
                              placement="topLeft"
                              title={t('UNEXPECTED_BREAK_DESC')}>
                              <img src={Danger_Circle} alt="" />
                            </Tooltip>
                          </div>
                          <div className='result-part__body__feedback__signag__ingredient'>
                            <div className='w-2 h-2' style={{ backgroundColor: "#e8eeff" }}></div>
                            <span>{t("MISSING_BREAK")}</span>
                            <Tooltip
                              align={{ offset: [-11.5, -6] }}
                              overlayClassName="speaking-error__info-tooltip"
                              placement="topLeft"
                              title={t('MISSING_BREAK_DESC')}>
                              <img src={Danger_Circle} alt="" />
                            </Tooltip>
                          </div>
                          <div className='result-part__body__feedback__signag__ingredient'>
                            <div className='w-2 h-2' style={{ backgroundColor: "#ff8f0d" }}></div>
                            <span>{t("MONOTONE")}</span>
                            <Tooltip
                              align={{ offset: [-11.5, -6] }}
                              overlayClassName="speaking-error__info-tooltip"
                              placement="topLeft"
                              title={t('MONOTONE_DESC')}>
                              <img src={Danger_Circle} alt="" />
                            </Tooltip>
                          </div>
                        </div>
                        <div className='flex gap-1 flex-wrap min-h-6'>
                          {words?.map((word, i) => {
                            const accuracyScore = word?.PronunciationAssessment?.AccuracyScore;
                            const breakErrors = word?.PronunciationAssessment?.Feedback?.Prosody?.Break?.ErrorTypes;
                            const intonationErrors = word?.PronunciationAssessment?.Feedback?.Prosody?.Intonation?.ErrorTypes;

                            const mispronunciation = typeof accuracyScore === 'number' && accuracyScore < 60;
                            const unexpectedBreak = breakErrors?.includes("UnexpectedBreak");
                            const missingBreak = breakErrors?.includes("MissingBreak");
                            const monotone = intonationErrors?.includes("Monotone");

                            const renderTitle = () => {
                              const overallClassName = renderPhonemeClassName(word?.PronunciationAssessment?.AccuracyScore);
                              return (
                                <>
                                  <span className="speaking-word__overall-accuracy">
                                    {`${word?.Word} : ${word?.PronunciationAssessment?.AccuracyScore}%`}
                                  </span>
                                  <div className="speaking-word__phonetic-transcriptions">
                                    <span className="transcription transcription__user">
                                      <AntButton
                                        size="xsmall"
                                        icon={<VolumeUp />}
                                        onClick={() => onPlayIpaPronounce(word?.Word)}
                                      />
                                      <span>
                                        /
                                        {word?.Phonemes?.map((p) => p?.Phoneme).join("")}
                                        /
                                      </span>
                                    </span>
                                    <span className="transcription">
                                      <AntButton
                                        size="xsmall"
                                        icon={<EarIcon />}
                                        onClick={() => onPlayUserPronounce(correspondingAnswer.audioFileId, word?.Offset, word?.Duration)}
                                      />
                                      <span>
                                        <span className={overallClassName}>/</span>
                                        {word?.Phonemes?.map((phoneme, phIndex) => (
                                          <span key={phIndex} className={renderPhonemeClassName(phoneme.PronunciationAssessment?.AccuracyScore)}>
                                            {phoneme?.Phoneme}
                                          </span>
                                        ))}
                                        <span className={overallClassName}>/</span>
                                      </span>
                                    </span>
                                  </div>
                                </>
                              );
                            };

                            const renderContent = () => (
                              <div className="speaking-word__phonemes-evaluation">
                                {word?.Phonemes?.map((phoneme, phIndex) => (
                                  <div key={phIndex} className="phonemes-evaluation__item">
                                    <div className="item__divider" />
                                    <div className="item__content">
                                      <span className="item__phoneme">/{phoneme?.Phoneme}/</span>
                                      <span>
                                        {phoneme.PronunciationAssessment?.AccuracyScore}% -{" "}
                                        {renderPhonemeText(phoneme.PronunciationAssessment?.AccuracyScore)}
                                      </span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            );

                            return (
                              <React.Fragment key={`${word?.Word}-${i}`}>
                                {unexpectedBreak && <span className="speaking-word__unexpected-break" />}
                                {missingBreak && <span className="speaking-word__missing-break" />}
                                <Popover
                                  overlayClassName="speaking-word__popover"
                                  title={renderTitle()}
                                  content={renderContent()}
                                  trigger="hover"
                                  mouseEnterDelay={0.05}
                                  mouseLeaveDelay={0.05}
                                  destroyTooltipOnHide={true}
                                  placement="topLeft"
                                >
                                  <span className="speaking-result__word result-part__body__feedback__content">
                                    <span className={clsx({
                                      "speaking-word__mispronunciation": mispronunciation,
                                      "speaking-word__monotone": monotone,
                                    })}>
                                      {`${word?.Word}${word?.Punctuation || ""}`}
                                    </span>
                                  </span>
                                </Popover>
                              </React.Fragment>
                            );
                          })}
                        </div>
                      </div>

                      {/* Comment */}
                      <div className="result-part__body__comment">
                        <p className="result-part__body__comment__header">{t("COMMENT")}:</p>
                        <div className="result-part__body__comment__content">
                          <Markdown>
                            {correspondingAnswer.feedback.comments}
                          </Markdown>
                        </div>
                      </div>
                    </>
                  );
                })()}
              </>
            ) : (
              <div className="result-part__no-answer">
                <img
                  src={Warning_icon}
                  alt=""
                  className=""
                />
                <p>{t("NO_ANSWER_FOR_THIS_QUESTION")}</p>
              </div>
            )}
          </div>
        );
      })}
      <ReactPlayer
        ref={playerIpaRef}
        url={wordIpa ? API.SPEAKING_WORD_IPA.format(wordIpa) : ""}
        onEnded={() => setWordIpa(null)}
        playing={!!wordIpa}
        width="0"
        height="0"
      />

      {audioFileId && <ReactPlayer
        ref={playerUserRef}
        url={API.STREAM_MEDIA.format(audioFileId)}
        playing={false}
        width="0"
        height="0"
      />
      }

    </div>
  );
}

export default ResultPart;