.error-reports-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .error-reports-info-card,
  .error-reports-search-card,
  .error-reports-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .error-reports-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .error-reports-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .error-reports-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }
  }

  // Search form styles
  .form-filter {
    width: 100%;
  }

  .search-form-item {
    margin-bottom: 16px;
  }

  .search-buttons-row {
    margin-top: 8px;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .ant-form-item {
    margin: 0;
  }

  // Table styles
  .error-reports-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .error-reports-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .error-id {
      font-family: 'Courier New', monospace;
      background-color: var(--background-light-background-1);
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .error-description {
      line-height: 1.4;
      color: var(--typo-colours-primary-black);
    }

    .error-url {
      color: var(--primary-colours-blue);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    .error-actions {
      display: flex;
      gap: 8px;
      justify-content: center;

      .ant-btn {
        border-radius: 6px;

        &.notified {
          background-color: #f6ffed;
          border-color: #b7eb8f;
          color: #52c41a;
        }

        &.not-notified {
          background-color: #fff2e8;
          border-color: #ffbb96;
          color: #fa8c16;
        }

        &.btn-delete {
          &:hover {
            background-color: #fff2f0;
            border-color: #ffccc7;
            color: #ff4d4f;
          }
        }
      }
    }
  }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .error-reports-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .search-buttons {
      margin-top: 16px;
      width: 100%;
      justify-content: space-between;
    }
  }
}
