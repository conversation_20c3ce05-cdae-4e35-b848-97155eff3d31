@mixin generate-nested-first-items($depth, $prefix: '*') {
  @for $i from 1 through $depth {
    > *:first-child {
      margin-top: 0;

      @if $i < $depth {
        @include generate-nested-first-items($depth - 1);
      }
    }
  }
}

@mixin generate-nested-last-items($depth, $prefix: '*') {
  @for $i from 1 through $depth {
    > *:last-child {
      margin-bottom: 0;

      @if $i < $depth {
        @include generate-nested-last-items($depth - 1);
      }
    }
  }
}

.question-title {
  font-weight: 700;
}

.question-title + * {
  @include generate-nested-first-items(6);
  @include generate-nested-last-items(6);
}
