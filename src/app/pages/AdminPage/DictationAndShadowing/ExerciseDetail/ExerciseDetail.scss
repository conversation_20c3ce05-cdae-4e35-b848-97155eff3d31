.document-template-detail-container {
  background-color: var(--white);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;

  .document-template-detail__header {
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .document-template-detail__section {

    .document-template__file-name {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .document-template__file-action {
        display: flex;
        flex-direction: row;
        gap: 16px;
      }
    }

  }

  .form-document-template-detail {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 24px;

    .ant-form-item {
      margin: 0;
    }

    .form-document-template-detail__submit {
      display: flex;
      align-self: end;
      justify-content: flex-end;
      grid-column-start: 2;
    }
  }

  .document-template-detail__no-data .no-data-container .no-data {
    width: 80px;
  }

}

.button-wrapper {
  margin-top: 16px;
  display: flex;
  gap: 16px;
  justify-content: flex-end;

  .ant-btn {
    width: 250px;
  }
}