import { useEffect, useState } from "react";
import { Table } from "antd";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";

import Loading from "@src/app/component/Loading";

import { getStatisticUsers } from "@services/Dashboard";
import { formatTimeDate } from "@src/common/functionCommons";

const UserStatistics = ({ activeTab, queryParams }) => {
  const { t } = useTranslation();
  const [statisticsData, setStatictisData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    getStatisticUsersData(queryParams);
  }, [queryParams]);
  
  const getStatisticUsersData = async (query) => {
    setIsLoading(true);
    const response = await getStatisticUsers(query);
    if (response) setStatictisData(response);
    setIsLoading(false);
  };
  
  const columns = [
    {
      title: t("FULL_NAME"),
      dataIndex: "fullName",
      key: "fullName",
      width: 300,
      sorter: (a, b) => a.fullName.localeCompare(b.fullName),
    },
    {
      title: t("EMAIL"),
      dataIndex: "email",
      key: "email",
      width: 300,
    },
    {
      title: t("LAST_VISIT"),
      dataIndex: "lastVisit",
      key: "lastVisit",
      align: "center",
      render: formatTimeDate,
      width: 200,
      sorter: (a, b) => dayjs(a.lastVisit || "1970-01-01") - dayjs(b.lastVisit || "1970-01-01"),
    },
    {
      title: t("NUMBER_OF_PROJECTS"),
      dataIndex: "numberOfProjects",
      key: "numberOfProjects",
      align: "center",
      width: 200,
      sorter: (a, b) => a.numberOfProjects - b.numberOfProjects,
    },
    {
      title: t("NUMBER_OF_SUBMITS"),
      dataIndex: "numberOfSubmits",
      key: "numberOfSubmits",
      align: "center",
      width: 200,
      sorter: (a, b) => a.numberOfSubmits - b.numberOfSubmits,
    },
  ];
  
  if (activeTab === "2") return null;
  if (isLoading) return <Loading/>;
  
  return (
    <Table columns={columns}
           dataSource={statisticsData}
           className={"table-user-subscription"}
           showSorterTooltip={false}
           scroll={{ x: 1000 }} rowKey="_id"
    />
  );
};

export default UserStatistics;