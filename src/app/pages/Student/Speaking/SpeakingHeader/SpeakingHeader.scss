.speaking-header {
  display: flex;
  flex-direction: column;
  align-items: center;

  .speaking-header__icon {
    img {
      height: 60px;
      width: 60px;
    }
  }

  .speaking-header__title {
    font-weight: 500;
    line-height: 24px;
  }

  .speaking-header__desc {
    font-size: 12px;
    line-height: 16px;
    display: flex;
    gap: 4px;

    .speaking-header__desc-link {
      user-select: none;
      line-height: 16px;
      font-weight: 500;
      text-decoration: underline;
      color: var(--navy);
      display: inline-flex;
      cursor: pointer;

      .speaking-header__desc-icon {
        width: 16px;
        height: 16px;
      }
    }
  }

}