@import "src/app/styles/scroll";

.project__info .project-info__activity .project-activity-item {
  &.project-activity__template {
    gap: unset;

    .project-template-form {
      margin-top: 8px;

      .ant-form {
        .ant-form-item {
          margin-bottom: 8px;
        }
      }

      .project-template__action {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .ant-btn {
          flex: 1;
          max-width: 96px;
        }
      }
    }
  }


  &.project-activity__update-template {
    gap: unset;

    .project-activity-label {
      .btn-toggle-template-list {
        .ant-btn-icon svg {
          transition: all var(--transition-timing);
        }

        &.show-list .ant-btn-icon svg {
          transform: rotate(-90deg);
        }
      }
    }

    .project-template-content {
      margin-top: 16px;
      margin-bottom: -2px;

      .project-template-list {
        @extend .scrollbar;
        @extend .scrollbar-show;

        max-height: 234px;

        .project-template-item {
          padding: 4px;
          cursor: pointer;

          &:hover {
            background: var(--lighttheme-content-background-stroke);
          }

          &.project-template-item__active {
            background: var(--background-light-background-blue);

            .ant-list-item-meta-avatar svg path {
              stroke: var(--white);
            }

            .ant-list-item-meta-content .ant-list-item-meta-title,
            .ant-list-item-meta-content .ant-list-item-meta-description {
              color: var(--white);
            }
          }

          .ant-list-item-meta-avatar {
            margin-inline-start: 4px;
            margin-inline-end: 6px;
            display: flex;
            align-self: center;

            svg {
              width: 24px;
              height: 24px;
            }
          }

          .ant-list-item-meta-content {
            .ant-list-item-meta-title {
              font-weight: 400;
              font-size: 14px;
            }

            .ant-list-item-meta-description {
              font-size: 10px;
              color: var(--typo-colours-support-blue-light);
            }
          }
        }
      }

      .project-template__action {
        display: flex;
        justify-content: center;
        margin-top: 14px;

        .ant-btn-default:disabled {
          background: transparent;
          border-color: transparent;
          color: var(--support-colours-grey-light);
        }
      }
    }
  }
}