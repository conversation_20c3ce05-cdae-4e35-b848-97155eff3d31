import React from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import clsx from "clsx";
import { Popover } from "antd";
import { useWorkspace } from "..";

import { LINK } from "@link";
import { formatDate } from "@common/functionCommons";
import { getProjectIcon } from "@src/app/component/FolderProjectList/FolderProjectItem";

import Starred from "@component/Star";
import ActionDropdown from "../ActionDropdown";

import FOLDER_ICON from "@src/asset/icon/folder/folder-light.svg";

export default function GridItem({ style, dataItem }) {

  const {
    user,
    handleChangeStarred,
  } = useWorkspace();
  const { t } = useTranslation();

  const addGuterStyle = {
    ...style,
    left: style.left + 24,
    top: style.top + 24,
    width: style.width - 24,
    height: style.height - 24,
  };
  if (!dataItem) return null;

  const { _id: itemId, isSaved, createdAt } = dataItem;
  const isFolder = dataItem.folderName;


  const linkDetail = isFolder ? LINK.FOLDER_DETAIL : LINK.PROJECT_DETAIL;
  const iconSrc = isFolder ? FOLDER_ICON : getProjectIcon(dataItem);
  const itemName = isFolder || dataItem.projectName;

  const creator = dataItem.ownerId?._id === user._id
    ? `${t("BY").toLowerCase()} ${t("ME").toLowerCase()}`
    : `${t("BY").toLowerCase()} ${dataItem.ownerId?.fullName}`;

  const itemDescription = isFolder
    ? `${t("PROJECT")}: ${dataItem.projects || 0}`
    : `${formatDate(createdAt)} ${creator}`;

  return (
    <div style={addGuterStyle} className="grid-item">
      <div className={clsx("grid-item-wraper")}>
        <Link
          to={linkDetail.format(itemId)}
          className="grid-item__content"
        >
          <img src={iconSrc} alt='' loading="lazy" />

          <div className="grid-item__info">
            <Popover
              placement="topLeft"
              content={itemName}
              className='grid-item__info__grid-item-name'
              trigger="hover">
              {itemName}
            </Popover>
            <div className="grid-item__info__grid-item-description">
              {itemDescription}
              <div className='grid-item__starred'>
                <Starred active={isSaved} onClick={() => { handleChangeStarred(dataItem) }} />
              </div>
            </div>
          </div>
        </Link >

        <ActionDropdown dataItem={dataItem} />

      </div >
    </div >
  );
}
