import React, { useEffect, useMemo, useState } from "react";
import { usePageViewTracker } from "@src/ga";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { Col, Form, Row, Select } from "antd";
import clsx from "clsx";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@src/app/component/ToastProvider";

import Loading from "@component/Loading";
import AntButton from "@src/app/component/AntButton";
import SearchInput from "@src/app/component/SearchInput";
import TemplateList from "@component/TemplateList";
import { AntForm } from "@src/app/component/AntForm";

import { BUTTON, CONSTANT, getExamCodeOptions } from "@constant";
import { LINK } from "@link";
import { cloneObj } from "@common/functionCommons";
import {
  getTemplateAvailable,
  updateTemplate,
  deleteTemplate,
  createProjectFromTemplate,
} from "@src/app/services/Template";

import "./Template.scss";
import LessonTemplate from "@src/app/component/SvgIcons/LessonTemplate";
import ExamTemplate from "@src/app/component/SvgIcons/ExamTemplate";
import MarkTemplate from "@src/app/component/SvgIcons/MarkTemplate";

const TAB_DATA_INIT = {
  [CONSTANT.ALL]: [],
  [CONSTANT.MY_TEMPLATE]: [],
  [CONSTANT.SYSTEM_TEMPLATE]: [],
  [CONSTANT.ORG_TEMPLATE]: [],
}

const TEMPLATE_ICON_INIT = {
  [CONSTANT.LESSON]: <LessonTemplate />,
  [CONSTANT.EXAM]: <ExamTemplate />,
  // [CONSTANT.MARKING]: <MarkTemplate />
}

const TEMPLATE_INIT = {
  [CONSTANT.LESSON]: {
    title: "LESSON_PLAN_TEMPLATE",
    dataSource: TAB_DATA_INIT,
    type: BUTTON.GHOST_PURPLE,
  },
  [CONSTANT.EXAM]: {
    title: "EXAM_CREATION_TEMPLATE",
    dataSource: TAB_DATA_INIT,
    type: BUTTON.GHOST_GREEN,
  },
  // [CONSTANT.MARKING]: {
  //   title: "MARKING_PLAN_TEMPLATE",
  //   dataSource: TAB_DATA_INIT,
  //   type: BUTTON.GHOST_PINK,
  // }
}

function TemplatePage({ user, dataSource, ...props }) {
  usePageViewTracker("Template");
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [formFilter] = Form.useForm();

  const [isFirst, setFirst] = useState(true);
  const [templateData, setTemplateData] = useState({});
  const [categoryActive, setCategoryActive] = useState(CONSTANT.ALL);
  const [cardActive, setCardActive] = useState(CONSTANT.LESSON);
  const [templateFiltered, setTemplateFiltered] = useState({});

  const categories = useMemo(() => {
    let newCategories = {
      [CONSTANT.ALL]: { title: "ALL_TEMPLATE", type: BUTTON.LIGHT_NAVY },
      [CONSTANT.MY_TEMPLATE]: { title: "MY_TEMPLATE", type: BUTTON.LIGHT_PINK },
      [CONSTANT.SYSTEM_TEMPLATE]: { title: "CLICKEE_TEMPLATE", type: BUTTON.LIGHT_YELLOW },
    };
    if (user?.organizationId) {
      newCategories[CONSTANT.ORG_TEMPLATE] = { title: user.organizationId.name, type: BUTTON.LIGHT_GREEN };
    }
    return newCategories;
  }, [user]);

  useEffect(() => {
    getOrganizationAndTemplate();
  }, []);

  const getOrganizationAndTemplate = async () => {
    const apiResponse = dataSource || await getTemplateAvailable({ sort: "-updatedAt" });
    if (Array.isArray(apiResponse)) {
      const templateObject = cloneObj(TEMPLATE_INIT);
      apiResponse.forEach(template => {
        const projectType = template?.projectType || CONSTANT.NORMAL;
        if (projectType === CONSTANT.NORMAL) {
          groupedByProjectType(template, templateObject[CONSTANT.LESSON].dataSource);
        } else if ([CONSTANT.EXAM_SCHOOL, CONSTANT.EXAM_IELTS].includes(projectType)) {
          groupedByProjectType(template, templateObject[CONSTANT.EXAM].dataSource);
        }
      });
      setTemplateData(templateObject);
      setTemplateFiltered(templateObject);
    }
    setFirst(false);
  };

  const groupedByProjectType = (template, object) => {
    object[CONSTANT.ALL].push(template);
    if (template.userId === user._id) {
      object[CONSTANT.MY_TEMPLATE].push(template);
    } else if (template.type === CONSTANT.ORGANIZATION && !!template.organizationId && template.organizationId?._id === user.organizationId?._id) {
      object[CONSTANT.ORG_TEMPLATE].push(template);
    } else if (template.type === CONSTANT.SYSTEM) {
      object[CONSTANT.SYSTEM_TEMPLATE].push(template);
    }
  }

  const onchangeFilter = (_, { name = '', folderCode = undefined }, actionData) => {
    const newTemplateData = actionData || templateData;
    if (!name && !folderCode) {
      setTemplateFiltered(newTemplateData);
    } else {
      const dataFilter = cloneObj(newTemplateData);
      Object.entries(dataFilter).forEach(([cardKey, cardData]) => {
        Object.entries(cardData.dataSource).forEach(([key, value]) => {
          cardData.dataSource[key] = value.filter(template => {
            const checkFolderCode = !folderCode || cardKey === CONSTANT.LESSON || template.folderCode === folderCode;
            const checkName = template.name.toLowerCase().includes(name?.toLowerCase());
            return checkFolderCode && checkName;
          });
        })
      });
      setTemplateFiltered(dataFilter);
    }
  };

  const onClearNameSearch = () => {
    formFilter.setFieldValue("name", "")
    const allValues = formFilter.getFieldsValue();
    onchangeFilter(null, allValues);
  };

  async function handleRenameTemplate(values) {
    const apiResponse = await updateTemplate(values);
    if (apiResponse) {
      const newTemplateData = cloneObj(templateData);
      Object.values(newTemplateData).forEach((cardData) => {
        Object.values(cardData.dataSource).forEach((value) => {
          value.forEach(template => {
            if (template._id === apiResponse._id) {
              template.name = apiResponse.name;
            }
          });
        })
      });
      setTemplateData(newTemplateData);
      const filter = formFilter.getFieldsValue();
      onchangeFilter(null, filter, newTemplateData);
      toast.success("UPDATE_TEMPLATE_SUCCESS");
    }
  }
  const deteteTemplateItem = (templateId, prevState) => {
    const newState = cloneObj(prevState);
    Object.values(newState).forEach((cardData) => {
      Object.entries(cardData.dataSource).forEach(([key, value]) => {
        cardData.dataSource[key] = value.filter(template => template._id !== templateId);
      })
    });
    return newState;
  }

  const handleDeleteTemplate = (templateId) => {
    confirm.delete({
      content: t("CONFIRM_DELETE_TEMPLATE"),
      handleConfirm: async () => {
        const apiResponse = await deleteTemplate(templateId);
        if (apiResponse) {
          setTemplateData(prevState => {
            const newState = deteteTemplateItem(apiResponse._id, prevState);
            return newState;
          });
          setTemplateFiltered(prevState => {
            const newState = deteteTemplateItem(apiResponse._id, prevState);
            return newState;
          });
          toast.success("DELETE_TEMPLATE_SUCCESS");
        }
      },
    });
  };

  async function handleCreateProject(template) {
    if (props.onUseTemplate) {
      props.onUseTemplate(template);
    } else {
      const response = await createProjectFromTemplate({ templateId: template._id });
      if (response) {
        toast.success("CREATE_PROJECT_FROM_TEMPLATE");
        navigate(LINK.PROJECT_DETAIL.format(response?._id));
      }
    }
  }

  if (isFirst) {
    return <Loading active transparent />;
  }

  return (
    <div className="template-container">

      <div className="template-container__header">
        <div className="template-project-type">
          {Object.entries(templateFiltered)
            ?.map(([key, value]) => {
              const countTemplate = value.dataSource[CONSTANT.ALL]?.length || 0;
              return <AntButton
                key={key}
                size="large"
                type={value.type}
                icon={TEMPLATE_ICON_INIT[key]}
                onClick={() => setCardActive(key)}
                active={cardActive === key}
                className={cardActive === key ? "project-type-active" : ""}
              >
                {t(value.title)} ({countTemplate})
              </AntButton>;
            })}
        </div>

        <AntForm form={formFilter} layout="vertical" onValuesChange={onchangeFilter}>
          <Row gutter={16}>
            <Col xs={24} md={12} lg={8} xl={6}>
              <AntForm.Item
                name="name"
              >
                <SearchInput
                  size="large"
                  placeholder={t("SEARCH_FOR_TEMPLATE")}
                  onClear={onClearNameSearch}
                  autoComplete="off"
                />
              </AntForm.Item>
            </Col>
            <Col xs={24} md={12} lg={8} xl={6}>
              <AntForm.Item
                name="folderCode"
                className={clsx({ 'form-item-hidden': cardActive !== CONSTANT.EXAM })}
              >
                <Select size="large" placeholder={t("SELECT_LEVEL")} options={getExamCodeOptions()} allowClear />
              </AntForm.Item>
            </Col>
          </Row>
        </AntForm>

        <div className="template-category">
          {Object.entries(categories).map(([key, value]) => {
            const countTemplate = templateFiltered[cardActive]?.dataSource[key].length || 0;
            return <span
              key={key}
              onClick={() => setCategoryActive(key)}
              className={clsx("template-category__item", { "category-active": categoryActive === key })}
            >
              {t(value.title)} ({countTemplate})
            </span>;
          })}
        </div>
      </div>

      <TemplateList
        showUpdate
        dataSource={templateFiltered[cardActive]?.dataSource[categoryActive] || []}
        onUseTemplate={handleCreateProject}
        onRename={handleRenameTemplate}
        onDelete={handleDeleteTemplate}
      />
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(TemplatePage);
