.error-report-detail-modal {
  .ant-modal-content {
    border-radius: 12px;
  }

  .ant-modal-header {
    border-radius: 12px 12px 0 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    .ant-modal-title {
      color: white;
      font-weight: 600;
    }
  }

  .ant-modal-close {
    .ant-modal-close-x {
      color: white;
      
      &:hover {
        color: #f0f0f0;
      }
    }
  }
}

.error-report-detail {
  .basic-info-card {
    margin-bottom: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;

    .ant-descriptions-item-label {
      font-weight: 600;
      color: #333;
      width: 120px;
    }

    .ant-descriptions-item-content {
      color: #666;
    }
  }

  .description-card {
    margin-bottom: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;

    .ant-card-head {
      background-color: #fafafa;
      border-bottom: 1px solid #e8e8e8;
    }

    .ant-typography {
      margin-bottom: 0;
      line-height: 1.6;
      color: #333;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .media-card {
    margin-bottom: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;

    .ant-card-head {
      background-color: #fafafa;
      border-bottom: 1px solid #e8e8e8;
    }

    .media-content {
      .images-section {
        margin-bottom: 24px;

        .ant-typography-title {
          margin-bottom: 12px;
          color: #333;
        }

        .images-gallery {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;

          .error-image {
            border: 2px solid #f0f0f0;
            transition: all 0.3s ease;
            cursor: pointer;

            &:hover {
              border-color: #1890ff;
              transform: scale(1.05);
            }
          }
        }
      }

      .video-section {
        .ant-typography-title {
          margin-bottom: 12px;
          color: #333;
        }

        .video-player {
          .error-video {
            border-radius: 8px;
            border: 1px solid #e8e8e8;
          }
        }
      }

      .no-media {
        text-align: center;
        padding: 40px 0;
        color: #999;
        font-style: italic;
      }
    }
  }

  .admin-actions-card {
    border: 1px solid #e8e8e8;
    border-radius: 8px;

    .ant-card-head {
      background-color: #f0f9ff;
      border-bottom: 1px solid #e8e8e8;
    }

    .action-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;

      .ant-switch {
        &.ant-switch-checked {
          background-color: #52c41a;
        }
      }
    }

    .admin-notes-section {
      border-top: 1px solid #f0f0f0;
      padding-top: 16px;

      .ant-typography {
        margin-bottom: 8px;
        color: #333;
      }

      .ant-input {
        border-radius: 6px;
        
        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }
}

// Image preview customization
.ant-image-preview-wrap {
  .ant-image-preview-img {
    max-height: 80vh;
    object-fit: contain;
  }
}

// Responsive design
@media (max-width: 768px) {
  .error-report-detail-modal {
    .ant-modal {
      width: 95% !important;
      margin: 10px;
    }

    .ant-modal-content {
      .ant-modal-body {
        padding: 16px;
      }
    }
  }

  .error-report-detail {
    .basic-info-card {
      .ant-descriptions {
        .ant-descriptions-item {
          padding-bottom: 8px;
        }
      }
    }

    .media-content {
      .images-gallery {
        justify-content: center;
        
        .error-image {
          width: 100px !important;
          height: 100px !important;
        }
      }

      .video-player {
        .error-video {
          height: 200px;
        }
      }
    }

    .admin-actions-card {
      .action-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }
  }
}

// Animation for loading states
.ant-spin-spinning {
  .error-report-detail {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Custom scrollbar for modal content
.error-report-detail-modal {
  .ant-modal-body {
    max-height: 70vh;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }
}
