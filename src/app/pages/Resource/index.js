import React, { useContext, useEffect, useMemo, useState } from "react";
import clsx from "clsx";
import { Tabs } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { useLocation } from "react-router-dom";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import AntButton from "@component/AntButton";
import SearchInput from "@component/SearchInput";
import ResourceContent from "@app/pages/Resource/ResourceContent";
import UploadResource from "@app/pages/Resource/UploadResource";

import { RESOURCE_TABS } from "@app/pages/Resource/resourceCommon";
import { BUTTON, CONSTANT } from "@constant";
import { groupBy } from "@common/dataConverter";
import { cloneObj } from "@common/functionCommons";
import { deleteResource, getAllResource, getResourceCapacity } from "@services/Resource";

import "./Resource.scss";

const CAPACITY_INIT = { video: 0, audio: 0, image: 0, document: 0 };

export const ResourceContext = React.createContext();

function Resource({ user, ...props }) {
  const { t } = useTranslation();
  const { state } = useLocation();
  const [categorySelected, setCategorySelected] = useState("");
  const [resourceTypeActive, setResourceTypeActive] = useState(CONSTANT.VIDEO);
  
  const [resourceSearchValue, setResourceSearchValue] = useState(null);
  
  const [myResourceData, setMyResourceData] = useState({});
  const [orgResourceData, setOrgResourceData] = useState({});
  
  const [userCapacity, setUserCapacity] = useState(CAPACITY_INIT);
  const [orgCapacity, setOrgCapacity] = useState(CAPACITY_INIT);
  
  const [userCapacityLimit, setUserCapacityLimit] = useState(0);
  const [orgCapacityLimit, setOrgCapacityLimit] = useState(0);
  
  const [isFirst, setFirst] = useState(true);
  
  const [isShowUpload, setShowUpload] = useState(false);
  
  useEffect(() => {
    getResourceData();
    getCapacityData();
  }, []);
  
  useEffect(() => {
    setCategorySelected(state?.asideType || CONSTANT.MY_RESOURCE);
  }, [state]);
  
  async function getCapacityData() {
    const allRequest = [getResourceCapacity({ userId: user._id })];
    if (user.organizationId?._id) {
      allRequest.push(getResourceCapacity({ organizationId: user.organizationId._id }));
    }
    const [userCapResponse, orgCapResponse] = await Promise.all(allRequest);
    if (userCapResponse) {
      setUserCapacity(userCapResponse);
      setUserCapacityLimit(userCapResponse.capacityLimit || 0);
    }
    if (orgCapResponse) {
      setOrgCapacity(orgCapResponse);
      setOrgCapacityLimit(orgCapResponse.capacityLimit || 0);
    }
  }
  
  async function getResourceData() {
    const allRequest = [getAllResource({ userId: user._id })];
    if (user.organizationId?._id) {
      allRequest.push(getAllResource({ organizationId: user.organizationId._id }));
    }
    
    const [myResourceList, orgResourceList] = await Promise.all(allRequest);
    if (Array.isArray(myResourceList)) setMyResourceData(groupBy(filterValidResource(myResourceList), "type"));
    if (Array.isArray(orgResourceList)) setOrgResourceData(groupBy(filterValidResource(orgResourceList), "type"));
    if (isFirst) setFirst(false);
  }
  const filterValidResource = (data) => {
    return data?.filter(item => {
      const resourceType = item?.type;
      switch (resourceType) {
        case CONSTANT.VIDEO: {
          return item?.videoId || item?.offlineVideoId;
        }
        case CONSTANT.AUDIO: {
          return item?.audioId;
        }
        case CONSTANT.IMAGE: {
          return item?.imageId;
        }
        case CONSTANT.DOCUMENT: {
          return item?.fileId || item?.imageId;
        }
        default: {
          return false;
        }
      }
    });
  };
  
  async function handleDeleteResource(resourceSelected) {
    if (!resourceSelected?._id || !resourceSelected?.type) return;
    
    function filterDataDeleted(prevState) {
      const newState = cloneObj(prevState);
      newState[resourceSelected.type] = newState[resourceSelected.type].filter(x => x._id !== resourceSelected._id);
      return newState;
    }
    
    confirm.delete({
      content: t("CONFIRM_DELETE_RESOURCE"),
      handleConfirm: async (e) => {
        const apiResponse = await deleteResource(resourceSelected._id);
        if (apiResponse) {
          if (resourceSelected.organizationId) {
            setOrgResourceData(filterDataDeleted);
          } else {
            setMyResourceData(filterDataDeleted);
          }
          await getCapacityData();
          toast.success("DELETE_RESOURCE_SUCCESS");
        }
      },
    });
  }
  
  const countMyResource = useMemo(() => {
    return Object.values(myResourceData)?.flat()?.length || 0;
  }, [myResourceData]);
  
  const countOrgResource = useMemo(() => {
    return Object.values(orgResourceData)?.flat()?.length || 0;
  }, [orgResourceData]);
  
  const resourceData = useMemo(() => {
    switch (categorySelected) {
      case CONSTANT.MY_RESOURCE:
        return myResourceData;
      case CONSTANT.ORG_RESOURCE:
        return orgResourceData;
      default:
        return {};
    }
  }, [categorySelected, myResourceData, orgResourceData]);
  
  const [resourceTypeCapacity, usedCapacity] = useMemo(() => {
    let capTemp;
    switch (categorySelected) {
      case CONSTANT.MY_RESOURCE:
        capTemp = userCapacity;
        break;
      case CONSTANT.ORG_RESOURCE:
        capTemp = orgCapacity;
        break;
      default:
        break;
    }
    // convert MB to kb
    const totalCap = calCapacity(capTemp);
    return [capTemp, totalCap];
    
  }, [categorySelected, userCapacity, orgCapacity]);
  
  const capacityLimit = useMemo(() => {
    switch (categorySelected) {
      case CONSTANT.MY_RESOURCE:
        return userCapacityLimit;
      case CONSTANT.ORG_RESOURCE:
        return orgCapacityLimit;
      default:
        return {};
    }
  }, [categorySelected, userCapacityLimit, orgCapacityLimit]);
  
  const resourceTabItems = useMemo(() => {
    return RESOURCE_TABS.map((resource, index) => {
      const isActiveTab = resource.key === resourceTypeActive;
      const tabClassName = clsx(`resource-tabs-tab ${resource.className}`, {
        "resource-tab__active": isActiveTab,
      });
      return {
        key: resource.key,
        className: resource.className,
        label: <div className={tabClassName}>
          <div className="resource-tabs-tab__icon">
            <img src={isActiveTab ? resource.activeIcon : resource.icon} alt="" />
          </div>
          {t(resource.lang)} ({resourceData?.[resource.key]?.length || 0})
        </div>,
      };
    });
  }, [t, resourceData, resourceTypeActive]);
  
  function calCapacity(capData) {
    if (!capData || !Array.isArray(Object.values(capData))) return 0;
    let total = 0;
    Object.entries(capData).forEach(([key, value]) => {
      if (key.includes("Sizes") && !Number.isNaN(value)) {
        total += value;
      }
    });
    return total;
  }
  
  return <ResourceContext.Provider
    value={{
      isFirst,
      resourceData, resourceTypeCapacity,
      usedCapacity, capacityLimit,
      categorySelected,
      resourceTypeActive,
      resourceSearchValue,
      handleDeleteResource,
      
      setMyResourceData, setOrgResourceData,
      
      isShowUpload, setShowUpload,
      
      calCapacity,
      getCapacityData,
    }}
  >
    <div className="resource-container">
      <div className="resource-categories">
        <AntButton
          size="small"
          type={BUTTON.LIGHT_NAVY}
          active={categorySelected === CONSTANT.MY_RESOURCE}
          onClick={() => setCategorySelected(CONSTANT.MY_RESOURCE)}
        >
          {t("MY_RESOURCES")} ({countMyResource})
        </AntButton>
        {!!user.organizationId && <AntButton
          size="small"
          type={BUTTON.LIGHT_PINK}
          active={categorySelected === CONSTANT.ORG_RESOURCE}
          onClick={() => setCategorySelected(CONSTANT.ORG_RESOURCE)}
        >
          {t("RESOURCE_ORG_NAME").format(user.organizationId?.name)} ({countOrgResource})
        </AntButton>}
      </div>
      
      <Tabs
        activeKey={resourceTypeActive}
        onChange={setResourceTypeActive}
        className="resource-tabs"
        items={resourceTabItems}
      />
      <div className="resource__search">
        <SearchInput
          size="large"
          placeholder={t("SEARCH_RESOURCE")}
          value={resourceSearchValue}
          onChange={(e) => setResourceSearchValue(e.target.value)}
          onClear={() => setResourceSearchValue(null)}
        />
        
        <div className="resource__add-new">
          <AntButton
            size="large"
            type={BUTTON.LIGHT_NAVY}
            onClick={() => setShowUpload(true)}
          >
            {t("ADD_RESOURCE")}
          </AntButton>
        </div>
      </div>
      
      <ResourceContent />
      
      <UploadResource />
    </div>
  </ResourceContext.Provider>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {};

const ConnectedResource = connect(mapStateToProps, mapDispatchToProps)(Resource);

const useResource = () => useContext(ResourceContext);

export { ConnectedResource as Resource, useResource };