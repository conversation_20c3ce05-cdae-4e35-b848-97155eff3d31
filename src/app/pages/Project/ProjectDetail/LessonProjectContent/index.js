import React from "react";
import { useProject } from "@app/pages/Project";

import { Content } from "./Content";

import "./LessonProjectContent.scss";

function LessonProjectContent() {
  const { projectContentData } = useProject();
  
  if (!projectContentData.length) return null;
  
  return <div className="project-content__inner">
    {projectContentData.map(content => {
      return <React.Fragment key={content._id}>
        <Content content={content} />
        <div className="preview-project__divider" />
      </React.Fragment>;
    })}
  </div>;
}

export default LessonProjectContent;