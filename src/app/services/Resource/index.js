import { createBase, deleteBase, getAllBase, getBase, getDetailBase, joinParams } from "@services/Base";
import { API } from "@api";
import axios from "axios";
import { CONSTANT } from "@constant";
import { convertObjectToParam, genPopulateParam } from "@common/functionCommons";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";

export function getAllResource(query) {
  return getAllBase(API.RESOURCE, query);
}

export function deleteResource(id) {
  return deleteBase(API.RESOURCE_ID, id, false, true);
}

export function getResourceAvailable() {
  return getBase(API.RESOURCE_AVAILABLE);
}

export function checkResourceExist(id) {
  return getDetailBase(API.RESOURCE_CHECK_EXIST, id);
}

export function getResourceCapacity(query) {
  const param = convertObjectToParam(query);
  return axios.get(API.RESOURCE_CAPACITY + param)
              .then(response => {
                if (response.status === 200) return response?.data;
                return null;
              })
              .catch(() => null);
}

export function uploadResource(file, fileInfo, config = {}) {
  config.headers = { "content-type": "multipart/form-data" };
  const formData = new FormData();
  Object.entries(fileInfo).forEach(([key, value]) => {
    formData.append(key, value);
  });
  const fileType = file.type.includes("image") ? CONSTANT.IMAGE : CONSTANT.FILE;
  formData.append("fileType", fileType.toLowerCase());
  formData.append("file", file);
  
  return axios.post(API.RESOURCE_UPLOAD, formData, config)
              .then(response => ({ success: true, data: response.data }))
              .catch((error) => error.response.data);
}

export function uploadVideoResource(file, fileInfo, config = {}) {
  config.headers = { "content-type": "multipart/form-data" };
  const formData = new FormData();
  Object.entries(fileInfo).forEach(([key, value]) => {
    formData.append(key, value);
  });
  formData.append("fileType", "file");
  formData.append("file", file);
  
  return axios.post(API.RESOURCE_UPLOAD_VIDEO, formData, config)
              .then(response => ({ success: true, data: response.data }))
              .catch((error) => error?.response?.data || { success: false });
}


export function createResourceYoutubeVideo(data) {
  return axios.post(API.RESOURCE_VIDEO, (data))
              .then(response => {
                return {
                  success: response.status === 200,
                  data: response.data,
                };
              })
              .catch((err) => err?.response?.data);
}