import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Dropdown } from "antd";
import { useNavigate } from "react-router-dom";

import AntButton from "@src/app/component/AntButton";
import Share from "@src/app/component/Share";
import StudentProjectName from "@src/app/component/StudentProjectName";
import { toast } from "@src/app/component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";
import ShareIcon from "@component/SvgIcons/ShareIcon";
import Trash from "@component/SvgIcons/Trash";
import MoreVertical from "@component/SvgIcons/MoreVertical";

import { deleteProject, updateProject } from "@src/app/services/Project";

import { LINK } from "@link";
import { BUTTON } from "@constant";
import { useDispatch } from "react-redux";
import { actions, paramsCreators, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";

const ResultHeader = ({ projectData, ...props }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [isShowModalShare, setShowModalShare] = useState(false);
  const { projectName, setProjectName, disabledEdit } = props;

  function onShareProjectAction() {
    setShowModalShare(prevState => !prevState);
    !isShowModalShare && dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_SHARE, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name)))
  }

  function onDeleteProjectAction() {
    confirm.delete({
      content: t("CONFIRM_DELETE_PROJECT"),
      handleConfirm: async () => {
        const apiResponse = await deleteProject(projectData._id);
        if (apiResponse) {
          toast.success("DELETE_PROJECT_SUCCESS");
          navigate(LINK.WRITING, { replace: true });
        }
      },
    });
    dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_DELETE, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name)))
  }

  const onChangeProjectName = (e) => {
    setProjectName(e.target.value);
  };

  const onSaveProjectname = async (e) => {
    let newProjectName = e.target.value;
    if (newProjectName === projectData.projectName) {
      return;
    }
    if (!newProjectName || !newProjectName.trim()) {
      // newProjectName = "Untitled essay";
      setProjectName(projectData.projectName);
      return;
    }

    const dataRequest = { _id: projectData._id, projectName: newProjectName };
    const dataResponse = await updateProject(dataRequest, true, { workspaceId: projectData.workspaceId });
    if (dataResponse) {
      toast.success("UPDATE_PROJECT_SUCCESS");
      navigate(LINK.WRITING_RESULT.format(projectData._id), { state: { data: { ...projectData, projectName: newProjectName } } });
      // setProjectData(pre => ({ ...pre, projectName: newProjectName }));
    } else {
      setProjectName(projectData.projectName);
    }
  };

  if (!projectData) return null;

  return <div className="result-writing__header">
    <StudentProjectName
      value={projectName}
      onBlur={onSaveProjectname}
      onChange={onChangeProjectName}
      isResult
      disabledEdit={disabledEdit}
    />
    <Dropdown
      menu={{
        items: [
          { key: "SHARE", label: t("SHARE"), icon: <ShareIcon />, onClick: onShareProjectAction },
          { key: "DELETE", label: t("DELETE"), icon: <Trash />, onClick: onDeleteProjectAction },
        ],
        //className: clsx("action-dropdown-menu", menuClassName),
      }}
      trigger={["click"]}
    >
      <AntButton
        size="compact"
        icon={<MoreVertical />}
        type={BUTTON.GHOST_NAVY}
        disabled={disabledEdit}
        onClick={(e) => dispatch(actions.trackCustomClick(TRACKING_ACTIONS.OPEN_OPTION, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name)))}
      />
    </Dropdown>
    <Share
      isShowModal={isShowModalShare}
      handleCancel={onShareProjectAction}
      queryAccess={{ projectId: projectData._id }}
      name={projectData.projectName}
      owner={projectData.ownerId}
      workspaceId={projectData.workspaceId}
      disabledEdit
    />
  </div>
};


export default ResultHeader;