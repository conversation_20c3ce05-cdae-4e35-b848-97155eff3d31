import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import Slider from "rc-slider";
import { Button } from "antd";
import ReactPlayer from "react-player";

import './AudioPlayer.scss'

import { toast } from "@component/ToastProvider";

import Loading from "@component/Loading";

import { API } from "@api";

import { renderAudioDuration } from "@common/functionCommons";

import SPEAKING_PAUSE from "@src/asset/icon/pause/speaking-pause.svg";
import SPEAKING_PLAY from "@src/asset/icon/play/speaking-play.svg";

const AudioPlayer = ({ audioFileId }) => {
  const { t } = useTranslation();

  const [isLoaded, setLoaded] = useState(false);

  const playerRef = useRef(null);

  const [isPlaying, setPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);

  useEffect(() => {
    setLoaded(false);

    if (!audioFileId) {
      setCurrentTime(0);
      setAudioDuration(0);
      setPlaying(false);
    } else {
    }

  }, [audioFileId]);

  function handleError(a, b, c, d) {
    setLoaded(true);
    return toast.error(t("RECORDING_DOES_NOT_EXIST"));
  }

  return <div id="js-speaking-audio-player" className="speaking-section ">
    <Loading active={!isLoaded && !!audioFileId} className="speaking-recorded-audio-player bg-gray-200">
      <div className="audio-player__play">
        <Button
          shape="circle"
          className="ant-btn-compact"
          icon={isPlaying ? <img src={SPEAKING_PAUSE} alt="" /> : <img src={SPEAKING_PLAY} alt="" />}
          onClick={() => {
            if (!audioDuration) return;
            setPlaying(prevState => !prevState);
          }}
        //disabled={!audioDuration}
        />
      </div>

      <div className="audio-player__time">
        {renderAudioDuration(currentTime)}
      </div>
      <div className="audio-player__seek-bar">
        <Slider
          min={0}
          max={audioDuration}
          value={currentTime}
          onChange={value => {
            if (!audioDuration) return;
            setCurrentTime(value);
            playerRef.current.seekTo(value);
          }}
        />
      </div>
      <div className="audio-player__time">
        {renderAudioDuration(audioDuration)}
      </div>
    </Loading>


    {audioFileId && <ReactPlayer
      ref={playerRef}
      url={API.STREAM_MEDIA.format(audioFileId)}
      playing={isPlaying}
      width="0"
      height="0"
      progressInterval={0}
      onDuration={duration => {
        setAudioDuration(Math.round(duration));
        setLoaded(true);
      }}
      onError={handleError}
      onProgress={({ playedSeconds }) => setCurrentTime(Math.floor(playedSeconds))}
      onEnded={() => {
        console.log("AudioPlayer: onEnded triggered. Current time before reset:", playerRef.current?.getCurrentTime());
        setCurrentTime(0);
        setPlaying(false);
        playerRef.current.seekTo(0);
      }}
    />}
  </div>;
};

export default AudioPlayer;
