.organization-management-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .organization-info-card,
  .organization-filter-card,
  .organization-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .organization-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .organization-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .organization-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create-organization {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  // Status styles
  .status-tag-active,
  .status-tag-locked {
    padding: 2px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
  }

  .status-tag-active {
    background-color: var(--green-light-1) !important;
    color: var(--green-dark) !important;
    border-color: var(--green-light-1) !important;
  }

  .status-tag-locked {
    background-color: var(--red-light-1) !important;
    color: var(--red-dark) !important;
    border-color: var(--red-light-1) !important;
  }

  // Table styles
  .organization-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .organization-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .organization-name-value {
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      font-size: 14px;
    }

    .organization-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 8px;

      .btn-edit-organization,
      .btn-lock-organization,
      .btn-delete-organization {
        &:hover {
          background: var(--background-hover);
        }
      }
    }
  }

  // Search form styles
  .form-filter {
    width: 100%;

    .search-form-item {
      margin-bottom: 0;
    }

    .search-buttons-col {
      display: flex;
      justify-content: flex-end;
    }

    .search-buttons {
      display: flex;
      gap: 16px;
      justify-content: flex-end;
    }

    .ant-form-item {
      margin-bottom: 0;
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .organization-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .btn-create-organization {
        width: 100%;
        justify-content: center;
      }
    }

    .form-filter {
      .search-buttons {
        margin-top: 16px;
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}