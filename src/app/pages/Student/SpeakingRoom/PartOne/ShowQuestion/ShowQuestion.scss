.question__content__loading {
  height: 235px;
}

.question__content__start {
  // gap: 222px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;


  .question__content__start_question {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
  }

  .question__content__start__mode-toggle {

    &__switch {
      &:hover {
        background: none;
        color: inherit;
        cursor: default; // Hoặc "auto"
      }
    }

    &__label {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      margin-left: 8px;
    }
  }

  .ant-switch {
    min-width: 30px;
    width: 30px;
    height: 16px;
    background-color: #c5cbd4;
    opacity: 0.6;
    border-radius: 16px;
    border: none;

    &:hover {
      background-color: #c5cbd4 !important; // giữ nguyên màu khi hover
      opacity: 0.6 !important;
    }

    &.ant-switch-checked:hover {
      background-color: #26d06d !important; // giữ nguyên khi đã bật
      opacity: 1 !important;
    }

    .ant-switch-handle {
      width: 12px;
      height: 12px;
      top: 2px;
      left: 2px;
      box-shadow: none;

      &::before {
        background-color: #7b889a;
        border-radius: 50%;
      }
    }

    &.ant-switch-checked {
      background-color: #26d06d;
      opacity: 1;

      .ant-switch-handle {
        left: calc(100% - 12px - 2px);

        &::before {
          background-color: #ffffff;
        }
      }
    }
  }
}

.question__content__body {
  gap: 48px;
  display: flex;
  justify-content: space-between;
  align-items: self-start;
  width: 100%;
  margin-top: 16px;
  margin-bottom: 16px;


  .question__content__body__left {
    gap: 8px;
    display: flex;
    align-items: self-start;

    &__icon {
      cursor: pointer;
      margin-top: 8px;
    }

    .question__content__body__left__text {
      gap: 8px;
      display: flex;
      justify-content: center;
      align-items: self-start;

      p {
        margin: 0;
        margin-top: 2px;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
      }

      &__part2 {
        display: block;

        h4 {
          margin: 0;
          margin-top: 2px;
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
        }

        ul {
          margin: 0;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
        }

        p {
          margin: 0;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
        }
      }

      &__icon {
        cursor: pointer;

      }

      &__hide {
        margin-top: 7px;
      }

    }
  }

  .question__content__body__right {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
  }
}

.boder-top {
  border-top: 1px solid #DBDBDB;
  width: 100%;
}

.question__content__note {
  // display: flex;
  gap: 8px;
  width: 100%;
  max-height: 256px;
  border-radius: 16px;
  border: 1px solid #DBDBDB;
  padding: 8px 16px 0 16px;
  margin-top: 16px;

  &__header {
    display: flex;
    justify-content: space-between;

    &__right {
      display: flex;
      gap: 4px;
      align-items: center;

      &__text {
        margin: 0;
        font-size: 12px;
        line-height: 16px;
        font-weight: 400;
      }

      &__time {
        margin: 0;
        font-size: 14px;
        line-height: 20px;
        font-weight: 700;
      }
    }

    &__left {
      font-size: 16px;
      line-height: 24px;
      font-weight: 500;
    }
  }

  /* Thêm vào file CSS */
  .custom-textarea {
    width: 100%;
    margin-top: 6px;
    font-size: 16px;
    font-family: sans-serif;
    line-height: 24px;
    resize: vertical;
    border: none;
    outline: none;
    border-radius: 4px;
    box-sizing: border-box;
    overflow-y: auto;
    resize: none;
    // background-color: #ECF0F4;

  }

  /* Ẩn scrollbar arrow buttons trên WebKit browsers */
  .custom-textarea::-webkit-scrollbar-button {
    display: none;
  }

  /* Tuỳ chọn: tuỳ chỉnh thanh cuộn */
  .custom-textarea::-webkit-scrollbar {
    width: 6px;
  }

  .custom-textarea::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 20px;
  }

  .custom-textarea::-webkit-scrollbar-track {
    background-color: transparent;
  }


}

.question__content__note__ban {
  background-color: #ECF0F4;
  gap: 8px;
  width: 100%;
  max-height: 256px;
  border-radius: 16px;
  border: 1px solid #DBDBDB;
  padding: 8px 16px 0 16px;
  margin-top: 16px;

  &__header {
    display: flex;
    justify-content: space-between;

    &__right {
      display: flex;
      gap: 4px;
      align-items: center;

      &__text {
        margin: 0;
        font-size: 12px;
        line-height: 16px;
        font-weight: 400;
      }

      &__time {
        margin: 0;
        font-size: 14px;
        line-height: 20px;
        font-weight: 700;
      }
    }

    &__left {
      font-size: 16px;
      line-height: 24px;
      font-weight: 500;
    }
  }

  .custom-textarea__ban {
    width: 100%;
    margin-top: 6px;
    font-size: 16px;
    font-family: sans-serif;
    line-height: 24px;
    resize: vertical;
    border: none;
    outline: none;
    border-radius: 4px;
    box-sizing: border-box;
    overflow-y: auto;
    background-color: #ECF0F4;
    resize: none;
  }

  /* Ẩn scrollbar arrow buttons trên WebKit browsers */
  .custom-textarea__ban::-webkit-scrollbar-button {
    display: none;
  }

  /* Tuỳ chọn: tuỳ chỉnh thanh cuộn */
  .custom-textarea__ban::-webkit-scrollbar {
    width: 6px;
  }

  .custom-textarea__ban::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 20px;
  }

  .custom-textarea__ban::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.question__content__content {
  width: 100%;
  height: 47px;
  gap: 16px;
  position: relative;
  border-top: 1px solid #DBDBDB;
  margin-bottom: 16px;


  &__box {
    // gap: 96px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    // right: 0;
    bottom: 0;


    &__time {
      margin: 0;
      font-size: 22px;
      font-weight: 600;
      line-height: 30px;
    }

    &__right {
      gap: 4px;
      display: flex;
      align-items: center;
      position: absolute;
      right: 0;
      cursor: pointer;

      &__text {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: #36A6FF;
      }
    }
  }
}

.question__content__footer {
  width: 100%;
  gap: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;

  &__text {
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: #B3B3B3;
    text-align: center;
  }

  &__icon {
    cursor: pointer;
    width: 40px;
    height: 40px;
  }
}

.check-violate {
  display: flex;
  flex-direction: column;
  /* Nếu muốn xếp theo chiều dọc */
  justify-content: center;
  /* Căn giữa theo chiều dọc */
  align-items: center;
  /* Căn giữa theo chiều ngang */
  gap: 3px;
  margin-bottom: 16px;

  &__box {
    max-width: 239px;
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
    display: flex;
    flex-direction: column;
    /* Nếu muốn xếp theo chiều dọc */
    justify-content: center;
    align-items: center;

    &__1 {
      display: flex;
      gap: 3px;

      p {
        margin-top: 0;
        color: #36A6FF;
        cursor: pointer;
      }
    }
  }
}

.popup-skip{
  // position: relative;
  width: 100%;

  &__icon {
    position: absolute;
    top: 115%;
    z-index: 55;
    right: 25%;
    cursor: pointer;
    height: 16px;
    width: 16px;
    background-color: #fff;
    transform: rotate(45deg);
    // clip-path: polygon(0 0, 100% 0, 0 100%); /* Cắt phần tử thành hình tam giác trên trong hệ tọa độ ban đầu */
    box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.1);
  }
}

.popup-skip-question {
  position: absolute;
  top: 49%;
  right: -42%;
  z-index: 50;
  width: 100%;

  &__short {
    position: absolute;
    top: 51%;
    right: -42%;
    z-index: 50;
    width: 100%;
  }

  &__part2 {
    position: absolute;
    top: 32%;
    right: -42%;
    z-index: 50;
    width: 100%;    
  }

  &__container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 24px 28px;
    border-radius: 16px;
    background-color: #fff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.2);
    // margin-bottom: 16px;
    max-width: 408px;
    width: 100%;
  }

  &__content {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    text-align: center;
  }

  &__button {
    display: flex;
    gap: 16px;
    justify-content: center;
    align-items: center;

    &__skip {
      display: flex;
      justify-content: center;
      align-items: center;
  
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #09196B;
      background-color: #E7E5FF;
      border-radius: 8px;
      padding: 8px 16px;
      max-width: 92px;
      width: 100%;
      max-height: 40px;
      height: 100%;
      cursor: pointer;
    }

    &__answer {
      display: flex;
      justify-content: center;
      align-items: center;
      ;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #fff;
      background-color: #09196B;
      border-radius: 8px;
      padding: 8px 16px;
      max-width: 92px;
      width: 100%;
      max-height: 40px;
      height: 100%;
      cursor: pointer;
    }
  }
}