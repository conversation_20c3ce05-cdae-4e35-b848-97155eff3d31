import { convertCamelAndNumberToTitleCase, WRITING_EVALUATION_LIST } from "./functionCommon";
import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";
import { CONSTANT } from "@constant";

const EssayAssessment = ({ essayAssessment = {}, evaluationSelected }) => {
  const { t } = useTranslation();

  if (!essayAssessment
    || (typeof essayAssessment === "object" && !Object.keys(essayAssessment)?.length)
    || !['essayAssessment', CONSTANT.ALL].includes(evaluationSelected)) return null;
  return <div className="evaluation__content__item evaluation-essay-assessment">
    <div
      className="content__item__title evaluation-essay-assessment__title"
      style={{ color: WRITING_EVALUATION_LIST.essayAssessment.color }}
    >
      <img src={WRITING_EVALUATION_LIST.essayAssessment.icon} alt="" />
      {t("ESSAY_ASSESSMENT")}
    </div>
    {typeof essayAssessment === "object" ? Object.entries(essayAssessment).map(([key, values], index) => {
      return (
        <div key={index}>
          <div className="essay-assessment__item-title">{convertCamelAndNumberToTitleCase(key)}</div>
          <ul>
            {values.map((item, itemIndex) => {
              return (<li key={itemIndex}>
                <span className="essay-assessment__item-title">{item.category}: </span>
                {item.feedback}
              </li>);
            })}
          </ul>
        </div>
      );
    }) : <HtmlContent dangerouslySetInnerHTML={{ __html: essayAssessment }} />}

  </div>;
};

export default EssayAssessment;