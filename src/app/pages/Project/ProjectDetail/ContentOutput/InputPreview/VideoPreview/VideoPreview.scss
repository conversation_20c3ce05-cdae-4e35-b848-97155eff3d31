.video-preview-container {
  display: flex;
  flex-direction: row;
  gap: 24px;
  max-width: 100%;
  page-break-inside: avoid;

  .video-preview-inner {
    display: flex;
    flex-direction: row;
    gap: 24px;
    width: calc(100% - 190px - 24px - 24px - 1px);

    .video-preview__thumbnail {
      width: 312px;
      height: 176px;
      border-radius: 8px;
      overflow: hidden;

      > img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .video-preview-info {
      width: calc(100% - 312px - 24px);
      align-items: center;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .video-preview-info__title {
        color: var(--primary-colours-blue-navy);
        font-weight: 600;
      }

      .video-preview-info__preview {
        .video-info-preview__link {
          display: flex;
          max-width: 333px;

          .ant-btn {
            padding: 0 15px;

            .ant-btn-label {
              text-decoration: underline;
            }
          }
        }
      }

      .video-info__time {
        display: flex;
        flex-direction: row;
        row-gap: 8px;
        column-gap: 24px;
        align-self: center;
        color: var(--typo-colours-support-blue-light);
        flex-wrap: wrap;
        justify-content: center;

        .video-info__time-duration {

        }

        .video-info__time-start {

        }

        .video-info__time-end {

        }
      }
    }
  }

  .video-preview-divider {
    display: flex;

    &:before {
      height: 100%;
      width: 1px;
      content: '';
      background-color: var(--support-colours-grey-light);
    }
  }
}


@media screen and (max-width: 1919.98px) {
  .video-preview-container {
    justify-content: space-between;

    .video-preview-inner {
      width: 367px;
      flex-direction: column;
      align-items: center;

      .video-preview-info {
        width: 100%;

        .video-info__preview {
          padding: 0 17px;
        }
      }
    }
  }
}

@media screen and (max-width: 1535.98px) {
  .video-preview-container {
    flex-direction: column;
    align-items: center;

    .video-preview-inner {
      flex-direction: column;
      align-items: center;

      .video-preview-info {
        width: 367px;
      }
    }

    .video-preview-divider {
      display: none;
    }
  }
}