.gpt-model-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .gpt-model-info-card,
  .gpt-model-search-card,
  .gpt-model-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .gpt-model-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .gpt-model-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .gpt-model-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create-gpt-model {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  // Search form styles
  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .ant-form-item {
    margin: 0;
  }

  // Table styles
  .gpt-model-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .gpt-model-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .model-name-value {
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      font-size: 14px;
    }

    .gpt-model-actions {
      display: flex;
      flex-direction: row;
      gap: 8px;
      justify-content: center;

      .btn-edit-gpt-model,
      .btn-delete-gpt-model {
        &:hover {
          background: var(--background-hover);
        }
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .gpt-model-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .btn-create-gpt-model {
        width: 100%;
        justify-content: center;
      }
    }

    .search-buttons {
      margin-top: 16px;
      width: 100%;
      justify-content: space-between;
    }
  }
}