import React, { useContext, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { Checkbox, Dropdown, Form } from "antd";
import clsx from "clsx";

import { useProject } from "@app/pages/Project";
import AntButton from "@component/AntButton";
import AntModal from "@component/AntModal";
import SelectOptions from "@app/pages/Project/DownloadProject/SelectOptions";
import PreviewDownload from "@app/pages/Project/DownloadProject/PreviewDownload";

import { BUTTON, CONSTANT, PREVIEW_TYPE, PROJECT_TYPE } from "@constant";
import { API } from "@api";

import { cleanFileName, cloneObj, downloadUsingBrowser, getFileExtension } from "@common/functionCommons";
import {
  createMultiExam,
  createMultiMarkTests,
  createOneExam,
  createOneMarkTest,
  createProjectFile,
} from "@services/Report";
import { editDocumentTemplate, getAllDocTemplateForProject } from "@services/DocumentTemplate";
import { getDocHeaderForProject } from "@services/DocumentHeader";

import Download from "@component/SvgIcons/Download";
import LOADING_1 from "@src/asset/gif/loading-1.gif";

import "./DownloadProject.scss";
import { extractIds } from "@common/dataConverter";


export const DownloadProjectContext = React.createContext();


function DownloadProject({
                           open, onCancel,
                           downloadType,
                           contentIdInitial,
                           indexSelected,
                         }) {
  const { t } = useTranslation();
  
  const { projectId, contentDownload, projectContentData, projectData, isExam, isMark } = useProject();
  const { examOutputData, totalExam, examOrderSelected } = useProject();
  
  const [formExportPDF] = Form.useForm();
  
  const [isCollapseDownload, setCollapseDownload] = useState(false);
  const [stateDownload, setStateDownload] = useState({
    isDownloading: false,
    fileType: null,
    uploadMethod: null,
  });
  
  const [openDropdown, setOpenDropdown] = useState(false);
  const [examIndexList, setExamIndexList] = useState([]);
  const [examIndexPreview, setExamIndexPreview] = useState(undefined);
  const [markTestIndexPreview, setMarkTestIndexPreview] = useState(0);
  
  const [previewType, setPreviewType] = useState(PREVIEW_TYPE.ALL);
  const [contentSelectedId, setContentSelectedId] = useState([]);
  
  const [docTemplateList, setDocTemplateList] = useState([]);
  const [docTemplateSelected, setDocTemplateSelected] = useState(null);
  
  const [editOptionsState, setEditOptionsState] = useState({
    isShowModal: false,
    optionsSelected: null,
  });
  
  const markTestResponses = useMemo(() => {
    if (!isMark) return [];
    const { responses } = projectContentData[0] || {};
    if (!Array.isArray(responses)) return [];
    const resultData = responses?.reduce(function (grouped, element) {
      grouped[element?.inputId?._id] = element;
      grouped[element?.inputId?._id].studentName = element.inputId?.studentName;
      return grouped;
    }, {});
    return Object.values(resultData);
  }, [projectContentData]);
  
  useEffect(() => {
    setExamIndexPreview(examOrderSelected);
  }, [examOrderSelected]);
  
  useEffect(() => {
    if ((stateDownload.isDownloading && editOptionsState.isShowModal) || !open) {
      setEditOptionsState({ isShowModal: false, optionsSelected: null });
    }
  }, [stateDownload.isDownloading, open]);
  
  async function getDataDocTemplate() {
    const apiResponse = await getAllDocTemplateForProject(projectData?.workspaceId);
    console.log("apiResponse", apiResponse);
    if (Array.isArray(apiResponse)) {
      setDocTemplateList(apiResponse);
    }
  }
  
  useEffect(() => {
    if (open) {
      // exam
      if (projectData.type === PROJECT_TYPE.EXAM_SCHOOL) {
        setExamIndexList([examOrderSelected]);
      }
      // exam
      
      getDataDocTemplate();
      
      if (!isMark) {
        const contentList = downloadType === CONSTANT.CONTENT
          ? [contentIdInitial]
          : CONSTANT.PROJECT ? projectContentData.map(content => content._id) : [];
        
        setContentSelectedId(contentList);
        formExportPDF.resetFields();
        formExportPDF.setFieldsValue({
          contentId: contentList,
          isSelectAllContent: downloadType === CONSTANT.PROJECT,
        });
      }
      if (indexSelected) {
        setMarkTestIndexPreview(indexSelected);
      }
    } else {
      setDocTemplateList([]);
      setExamIndexList([]);
      setDocTemplateSelected(null);
      setMarkTestIndexPreview(0);
    }
  }, [open, indexSelected]);
  
  useEffect(() => {
    setTimeout(() => {
      if (stateDownload.isDownloading) {
        handleDownloadFile();
      }
    }, 500);
  }, [stateDownload.isDownloading]);
  
  
  function onSelectExam(examOrder) {
    examOrder = parseInt(examOrder);
    setExamIndexList(prevState => {
      let newState = cloneObj(prevState).map(x => parseInt(x));
      if (newState.includes(examOrder)) {
        newState = newState.filter(state => state !== examOrder);
      } else {
        newState.push(examOrder);
      }
      return newState;
    });
  }
  
  function changeDownloadState(fileType, uploadMethod) {
    if (!fileType) return;
    setStateDownload({ isDownloading: true, fileType, uploadMethod });
    setOpenDropdown(false);
  }
  
  function handleDownloadFinish() {
    setStateDownload({ isDownloading: false, fileType: null, uploadMethod: null });
  }
  
  async function updateLastUsed() {
    if (!docTemplateSelected?._id) return;
    const updateDocTemplateRequest = {
      _id: docTemplateSelected._id,
      lastUsed: new Date(),
    };
    const updateDocTemplateResponse = await editDocumentTemplate(updateDocTemplateRequest);
    if (updateDocTemplateResponse) {
      setDocTemplateList(prevState => {
        return cloneObj(prevState).map(state => {
          if (state._id === updateDocTemplateResponse._id) {
            state.lastUsed = updateDocTemplateResponse.lastUsed;
          }
          return state;
        });
      });
    }
  }
  
  async function handleCreateProjectFilePreview() {
    const apiRequest = {
      projectId,
      contentIds: contentSelectedId,
      type: previewType,
      isPDF: stateDownload.fileType === CONSTANT.PDF,
    };
    
    if (docTemplateSelected?._id) {
      apiRequest.docxTemplateId = docTemplateSelected._id;
      const headerResponse = await getDocHeaderForProject(docTemplateSelected._id);
      if (!!headerResponse?.customHeader) {
        apiRequest.customHeader = headerResponse.customHeader;
      }
    }
    
    const params = { workspaceId: projectData?.workspaceId };
    return createProjectFile(apiRequest, params);
  }
  
  function handleResponseData() {
    let responseData;
    if (stateDownload.uploadMethod === CONSTANT.SINGLE) {
      responseData = examOutputData[examIndexPreview]
        ?.filter(output => contentSelectedId.includes(output.contentId))
        .map(response => response?._id);
    } else if (stateDownload.uploadMethod === CONSTANT.MULTI) {
      responseData = [];
      examIndexList.forEach(order => {
        if (examIndexList.includes(order)) {
          const responses = examOutputData[order].filter(output => contentSelectedId.includes(output.contentId));
          responseData.push(extractIds(responses));
        }
      });
    }
    return responseData;
  }
  
  async function handleCreateExamFilePreview() {
    const responseData = handleResponseData();
    if (!responseData?.length) return;
    
    const apiRequest = {
      projectId,
      type: previewType,
      isPDF: stateDownload.fileType === CONSTANT.PDF,
    };
    
    if (docTemplateSelected?._id) {
      apiRequest.docxTemplateId = docTemplateSelected._id;
      
      const headerResponse = await getDocHeaderForProject(docTemplateSelected._id);
      if (!!headerResponse?.customHeader) {
        apiRequest.customHeader = headerResponse.customHeader;
      }
    }
    
    if (stateDownload.uploadMethod === CONSTANT.SINGLE) {
      apiRequest.responseIds = responseData;
      return createOneExam(apiRequest);
    } else if (stateDownload.uploadMethod === CONSTANT.MULTI) {
      apiRequest.data = responseData.map(responseIds => ({ responseIds }));
      return createMultiExam(apiRequest);
    }
  }
  
  async function handleCreateMarkTestFilePreview() {
    const apiRequest = {
      projectId,
      type: previewType,
      isPDF: stateDownload.fileType === CONSTANT.PDF,
    };
    if (docTemplateSelected?._id) {
      apiRequest.docxTemplateId = docTemplateSelected._id;
      
      const headerResponse = await getDocHeaderForProject(docTemplateSelected._id);
      if (!!headerResponse?.customHeader) {
        apiRequest.customHeader = headerResponse.customHeader;
      }
    }
    if (stateDownload.uploadMethod === CONSTANT.SINGLE) {
      apiRequest.responseId = markTestResponses[markTestIndexPreview]?._id;
      return createOneMarkTest(apiRequest);
    } else if (stateDownload.uploadMethod === CONSTANT.MULTI) {
      apiRequest.responseIds = markTestResponses.map(response => response._id);
      return createMultiMarkTests(apiRequest);
    }
  }
  
  async function downloadFileUsingBrowser(fileInfo) {
    if (fileInfo?.fileName) {
      await updateLastUsed();
      
      const displayName = cleanFileName(projectData.projectName) + "." + getFileExtension(fileInfo.fileName);
      const workspaceId = projectData?.workspaceId;
      const logParamsString = `&workspaceId=${workspaceId}&projectId=${projectId}`;
      downloadUsingBrowser(API.DOWNLOAD_REPORT_FILE.format(fileInfo.fileName, displayName) + logParamsString, displayName);
      handleDownloadFinish();
    } else {
      setStateDownload({ isDownloading: false, fileType: null, uploadMethod: null });
    }
  }
  
  async function handleDownloadFile() {
    let filePreview;
    switch (projectData.type) {
      case PROJECT_TYPE.NORMAL:
        filePreview = await handleCreateProjectFilePreview();
        break;
      case PROJECT_TYPE.EXAM_SCHOOL:
        filePreview = await handleCreateExamFilePreview();
        break;
      case PROJECT_TYPE.MARK_TEST_SCHOOL:
      case PROJECT_TYPE.MARK_TEST_IELTS:
        filePreview = await handleCreateMarkTestFilePreview();
        break;
      default:
        
        break;
    }
    
    await downloadFileUsingBrowser(filePreview);
  }
  
  const handleOpenChange = (nextOpen, info) => {
    if (info.source === "trigger" || nextOpen) {
      setOpenDropdown(nextOpen);
    }
  };
  
  const examDropdown = useMemo(() => {
    if (!totalExam) return null;
    return [
      ...Object.keys(examOutputData).map(examOrder => {
        return {
          key: `${CONSTANT.EXAM}_${examOrder}`,
          label: t("EXAM_NUMBER").format(examOrder),
          icon: <Checkbox checked={examIndexList.includes(parseInt(examOrder))} />,
          onClick: ({ domEvent }) => {
            domEvent.preventDefault();
            onSelectExam(examOrder);
          },
        };
      }),
      {
        key: CONSTANT.DOWNLOAD,
        className: "download-multi-exam",
        label: <Dropdown
          trigger="click"
          menu={{
            items: [
              {
                key: "DOCX", label: t("DOWLOAD_WITH_DOCX"), icon: <Download />,
                onClick: () => changeDownloadState(CONSTANT.DOCX, CONSTANT.MULTI),
              },
              {
                key: "PDF",
                label: t("DOWLOAD_WITH_PDF"),
                icon: <Download />,
                onClick: () => changeDownloadState(CONSTANT.PDF, CONSTANT.MULTI),
              },
            ],
          }}
          placement="bottomRight"
          disabled={!contentSelectedId?.length || !examIndexList?.length}
        >
          <AntButton size="large" type={BUTTON.DEEP_NAVY}>
            {t("DOWNLOAD")}
          </AntButton>
        </Dropdown>,
        onClick: ({ domEvent }) => domEvent.preventDefault(),
      },
    ];
  }, [projectContentData, examIndexList]);
  
  const disableDownload = (!markTestResponses.length) && (!contentSelectedId?.length || (projectData.type === PROJECT_TYPE.EXAM_SCHOOL && (!examIndexList?.length || !totalExam)));
  return (
    <DownloadProjectContext.Provider value={{
      editOptionsState, setEditOptionsState,
      isCollapseDownload, setCollapseDownload,
      previewType,
      contentSelectedId,
      docTemplateSelected,
      
      //
      getDataDocTemplate,
    }}>
      <AntModal
        title={<div className={"title-download-project"}>
          <span className={"title-modal-export-pdf__title"}>{t("DOWNLOAD_PROJECT")}</span>
          <div className={"title-modal-export-pdf__actions"}>
            <AntButton size="large" type={BUTTON.GHOST_WHITE} onClick={onCancel}>
              {t("CLOSE")}
            </AntButton>
            
            {isExam && <Dropdown
              trigger="click"
              open={openDropdown}
              menu={{
                className: "title-modal-export-pdf__select-multi-exam",
                items: examDropdown,
              }}
              placement="bottomRight"
              disabled={!contentSelectedId?.length || !examDropdown}
              onOpenChange={handleOpenChange}
            >
              <AntButton size="large" type={BUTTON.LIGHT_NAVY} disabled={!examDropdown}>
                {t("DOWNLOAD_MULTIPLE_EXAMS")}
              </AntButton>
            </Dropdown>}
            
            {(isMark && markTestResponses.length > 1) && <Dropdown
              trigger="click"
              menu={{
                items: [
                  {
                    key: "DOCX", label: t("DOWLOAD_WITH_DOCX"), icon: <Download />,
                    onClick: () => changeDownloadState(CONSTANT.DOCX, CONSTANT.MULTI),
                  },
                  {
                    key: "PDF",
                    label: t("DOWLOAD_WITH_PDF"),
                    icon: <Download />,
                    onClick: () => changeDownloadState(CONSTANT.PDF, CONSTANT.MULTI),
                  },
                ],
              }}
              placement="bottomRight"
            >
              <AntButton size="large" type={BUTTON.LIGHT_NAVY}>
                {t("DOWNLOAD_GRADED_ASSIGNMENTS").format(markTestResponses.length)}
              </AntButton>
            </Dropdown>}
            
            <Dropdown
              trigger="click"
              menu={{
                items: [
                  {
                    key: "DOCX", label: t("DOWLOAD_WITH_DOCX"), icon: <Download />,
                    onClick: () => changeDownloadState(CONSTANT.DOCX, CONSTANT.SINGLE),
                  },
                  {
                    key: "PDF",
                    label: t("DOWLOAD_WITH_PDF"),
                    icon: <Download />,
                    onClick: () => changeDownloadState(CONSTANT.PDF, CONSTANT.SINGLE),
                  },
                ],
              }}
              placement="bottomRight"
              disabled={disableDownload}
            >
              <AntButton size="large" type={BUTTON.DEEP_NAVY}>
                {t("DOWNLOAD")}
              </AntButton>
            </Dropdown>
          </div>
        </div>}
        open={open}
        onCancel={onCancel}
        width={isCollapseDownload ? 900 : 1678}
        className={clsx("download-project", { "download-project__collapse": isCollapseDownload })}
        closeIcon={null}
        footerless={true}
        destroyOnClose
      >
        {stateDownload.isDownloading && <div className="download-project__loading">
          <div className="download-project__loading-image">
            <img src={LOADING_1} alt="" />
          </div>
          <div className="download-project__loading-text">
            {t("PROCESSING_DOWNLOAD_FILE")}
          </div>
        </div>}
        
        {!isCollapseDownload && <SelectOptions
          formExportPDF={formExportPDF}
          previewType={previewType} setPreviewType={setPreviewType}
          contentDownload={contentDownload}
          setContentSelectedId={setContentSelectedId}
          docTemplateList={docTemplateList}
          docTemplateSelected={docTemplateSelected}
          setDocTemplateSelected={setDocTemplateSelected}
        />}
        
        <PreviewDownload
          examIndexPreview={examIndexPreview}
          setExamIndexPreview={setExamIndexPreview}
          markTestResponses={markTestResponses}
          markTestIndexPreview={markTestIndexPreview}
          setMarkTestIndexPreview={setMarkTestIndexPreview}
        />
      </AntModal>
    </DownloadProjectContext.Provider>
  );
}

function mapStateToProps(store) {
  return {};
}

const ConnectedDownloadProject = connect(mapStateToProps)(DownloadProject);

const useDownloadProject = () => useContext(DownloadProjectContext);

export { ConnectedDownloadProject as DownloadProject, useDownloadProject };
