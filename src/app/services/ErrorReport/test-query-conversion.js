// Test file để verify query conversion logic
// Chạy file này để test logic conversion tr<PERSON><PERSON><PERSON> khi deploy

import { getAllErrorReports } from './index.js';

// Mock getAllPaginationBase để test logic conversion
const mockGetAllPaginationBase = (api, paging, queryObj, searchFields, populate) => {
  console.log('=== Query Conversion Test ===');
  console.log('Final API Query:', JSON.stringify(queryObj, null, 2));
  return Promise.resolve({ rows: [], total: 0 });
};

// Temporarily replace the import
const originalGetAllPaginationBase = getAllPaginationBase;

// Test cases
const testCases = [
  {
    name: "This Week Selection",
    query: { time: "week", description: "test error" },
    expected: "createdAt: { $gte: startOfWeek, $lte: now }"
  },
  {
    name: "This Month Selection", 
    query: { time: "month", impactLevel: "high" },
    expected: "createdAt: { $gte: startOfMonth, $lte: now }"
  },
  {
    name: "Custom Date Range",
    query: { 
      fromDate: 1672531200, // 01/01/2023 00:00
      toDate: 1672617600,   // 02/01/2023 00:00
      notified: "false"
    },
    expected: "createdAt: { $gte: 1672531200, $lte: 1672617600 }"
  },
  {
    name: "Only From Date",
    query: { fromDate: 1672531200 },
    expected: "createdAt: { $gte: 1672531200 }"
  },
  {
    name: "Only To Date", 
    query: { toDate: 1672617600 },
    expected: "createdAt: { $lte: 1672617600 }"
  },
  {
    name: "No Date Filter",
    query: { description: "test", impactLevel: "medium" },
    expected: "No createdAt field"
  }
];

// Run tests
async function runTests() {
  console.log('🧪 Testing ErrorReport Query Conversion Logic\n');
  
  for (const testCase of testCases) {
    console.log(`\n📋 Test: ${testCase.name}`);
    console.log(`Input Query:`, testCase.query);
    console.log(`Expected:`, testCase.expected);
    
    try {
      await getAllErrorReports(
        { page: 1, pageSize: 20 }, 
        testCase.query, 
        ["description"]
      );
    } catch (error) {
      console.error(`❌ Test failed:`, error.message);
    }
    
    console.log('---');
  }
  
  console.log('\n✅ All tests completed!');
}

// Export for manual testing
export { runTests };

// Auto-run if this file is executed directly
if (typeof window === 'undefined' && import.meta.url === `file://${process.argv[1]}`) {
  runTests();
}

/*
Expected Output Examples:

1. This Week:
{
  "description": "test error",
  "createdAt": {
    "$gte": 1672531200,
    "$lte": 1672617600
  },
  "sort": "-createdAt"
}

2. Custom Range:
{
  "notified": "false", 
  "createdAt": {
    "$gte": 1672531200,
    "$lte": 1672617600
  },
  "sort": "-createdAt"
}

3. No Date Filter:
{
  "description": "test",
  "impactLevel": "medium",
  "sort": "-createdAt"
}
*/
