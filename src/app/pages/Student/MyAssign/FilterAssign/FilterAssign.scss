.filter-assign-container {
  width: 304px;
  display: flex;
  flex-direction: column;
  gap: 24px;

  .filter-assign__header {
    font-size: 24px;
    font-weight: 700;
    color: var(--navy);
  }

  .filter-assign__section {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .filter-assign__section-header {
      font-weight: 500;
      color: var(--navy);
    }

    .filter-assign__section-body {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .ant-checkbox-wrapper {
        display: flex;
        align-items: center;
        color: var(--navy);

        .ant-checkbox {
          height: 24px;
          width: 24px;

          &.ant-checkbox-checked .ant-checkbox-inner {
            background-color: #4B2CC9;
          }

          &:not(.ant-checkbox-indeterminate) .ant-checkbox-inner:after {
            left: 36%;
            top: 46%;
            transition: opacity 0.3s ease, transform 0.3s ease;
          }

          &.ant-checkbox-indeterminate .ant-checkbox-inner:after {
            border-radius: 2px;
            background-color: #4B2CC9;
          }

          .ant-checkbox-inner {
            height: 24px;
            width: 24px;
            border-color: #4B2CC9;
          }
        }
      }

      .filter-assign__section-collapse {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .ant-collapse-header {
          padding: 0;
        }

        .ant-collapse-item:not(.ant-collapse-item-active) {
          .ant-collapse-expand-icon {
            svg {
              rotate: -180deg;
            }

            button:not(:active) {
              svg path {
                stroke: var(--primary-colours-blue-navy);
              }
            }
          }
        }

        .ant-collapse-content-box {
          padding: 16px 0 0px 32px;
        }

        .ant-checkbox-group {
          flex-direction: column;
          gap: 16px;

          .ant-checkbox-wrapper {
            color: unset;
          }
        }

      }

    }
  }
}