import './AllResults.scss';
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import ArrowRight_Icon from "@src/asset/icon/Arrow-Right2.svg"

import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";
import Loading from "@component/Loading";

import Share from "@component/Share";
import MyAssign from "@app/pages/Student/MyAssign";

import { CONSTANT, TYPE_OF_TOOL } from "@constant";

import { handlePagingData } from "@common/dataConverter";
import { calPagingAfterDelete, cloneObj } from "@common/functionCommons";

import { getResultsByStudent, getAllSessions, deleteSession, getSessionDetail } from '@src/app/services/SpeakingRoom';
import { useNavigate } from 'react-router-dom';
import { LINK } from '@link';

const AllResults = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [isLoading, setLoading] = useState(false);
  const [speechesData, setSpeechesData] = useState({
    rows: [],
    paging: {
      page: 1,
      pageSize: 5,
      total: 0,
      totalPages: 0,
    },
    query: { sortType: CONSTANT.LATEST, documentType: "" },
  });

  const [shareState, setShareState] = useState({
    isShowModal: false,
    projectData: null,
  });
  // console.log(shareState)

  function handleShowModal(isShowModal = false, projectData = null) {
    if (isShowModal) {
      setShareState({ isShowModal, projectData });
    } else {
      setShareState(prevState => Object.assign({}, prevState, { isShowModal }));
    }
  }

  async function getSpeakingSpeechesData(
    paging = speechesData.paging,
    query = speechesData.query,
  ) {
    setLoading(true);
    const queryObj = {
      // inputType: `${TYPE_OF_TOOL.AUDIO_STREAM}, ${TYPE_OF_TOOL.STUDENT_SPEAKING}`,
      sort: query.sortType === CONSTANT.LATEST ? "-createdAt" : "createdAt",
    };

    if (query.documentType) queryObj.documentType = query.documentType;
    if (query.tag) queryObj.tag = query.tag;

    // const apiResponse = await getResultsByStudent(paging, queryObj);
    // console.log(queryObj)
    const apiResponse1 = await getAllSessions(queryObj, paging);

    if (apiResponse1) {
      setSpeechesData(handlePagingData(apiResponse1, query));
    }
    setLoading(false);
  }

  function onChangePage(page) {
    getSpeakingSpeechesData(Object.assign({}, speechesData.paging, { page }));
  }

  function onChangeSort(type) {
    getSpeakingSpeechesData(speechesData.paging, { ...speechesData.query, sortType: type });
  }

  function onChangeFilter(dataChange) {
    if (!dataChange) return;
    const query = cloneObj(speechesData.query);

    if (dataChange.hasOwnProperty("documentType")) {
      if (dataChange.documentType?.length === 1) {
        query.documentType = dataChange.documentType[0];
      } else {
        delete query.documentType;
      }
    }
    if (dataChange.tags?.length) {
      query.tag = dataChange.tags.join(",");
    } else {
      delete query.tag;
    }
    // console.log(query)
    getSpeakingSpeechesData({ ...speechesData.paging, page: 1 }, query);
  }

  function onDeleteAssign(projectId) {
    confirm.delete({
      content: t("CONFIRM_DELETE_PROJECT"),
      handleConfirm: async () => {
        const apiResponse = await deleteSession(projectId);
        if (apiResponse) {
          await getSpeakingSpeechesData(calPagingAfterDelete(speechesData));
          toast.success("DELETE_PROJECT_SUCCESS");
        }
      },
    });
  }

  async function onShareAssign(projectId) {
    const apiResponse = await getSessionDetail(projectId);
    if (apiResponse) {
      handleShowModal(true, apiResponse);
    }

  }

  return <>
    <Loading active={isLoading} className="speaking-speeches-container">
      <div className="view-result__header">
        <span onClick={() => navigate(LINK.WRITING)}
          className="view-result__header__cursor-pointer"
        >
          {t('STUDY_HUB')}
        </span>
        <img
          src={ArrowRight_Icon}
          alt=""
        />
        <span onClick={() => navigate(LINK.SPEAKING_ROOM)}
          className="view-result__header__cursor-pointer"
        >
          {t('SPEAKING_ROOM_MENU')}
        </span>
        <img
          src={ArrowRight_Icon}
          alt=""
        />
        <span className="view-result__header__cursor-none">
          {t('SPEAKING_RESULTS')}
        </span>
      </div>

      <MyAssign
        title={t("ALL_SPEAKING_RESULT")}
        studentProjectType={CONSTANT.SPEAKING}
        total={speechesData.paging.total}
        currentPage={speechesData.paging.page}
        dataSource={speechesData.rows}
        sortType={speechesData.query.sortType}
        onChangePage={onChangePage}
        onChangeSort={onChangeSort}
        onDeleteAssign={onDeleteAssign}
        onShareAssign={onShareAssign}
        onChangeFilter={onChangeFilter}
      />
    </Loading>


    <Share
      isShowModal={shareState.isShowModal}
      handleCancel={handleShowModal}
      queryAccess={{ sessionId: shareState.projectData?._id }}
      name={shareState.projectData?.tag}
      owner={shareState.projectData?.userId}
      // workspaceId={shareState.projectData?.workspaceId}
      disabledEdit
    />
  </>;
}

export default AllResults;