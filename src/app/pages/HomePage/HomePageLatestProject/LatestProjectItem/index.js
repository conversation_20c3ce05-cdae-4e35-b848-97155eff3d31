import React, { useState } from "react";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import { Popover } from "antd";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";

import Starred from "@component/Star";
import ProjectActionsDropdown from "@component/ProjectActionsDropdown";

import { API } from "@api";
import { LINK } from "@link";
import { toStar, unStar } from "@services/MySaved";
import { getProjectIcon } from "@src/app/component/FolderProjectList/FolderProjectItem";

import PROJECT_DEFAULT from "@src/asset/image/project-default.svg";

import "./LatestProjectItem.scss";

function LatestProjectItem({ ...props }) {
  const { t } = useTranslation();
  const { projectData } = props;
  const { handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterChangeStarred, handleAfterMove } = props;
  
  const [isLoaded, setLoaded] = useState(false);
  
  const handleChangeStarred = async () => {
    const serviceStar = projectData.isSaved ? unStar : toStar;
    const apiResponse = await serviceStar({ projectId: projectData._id });
    
    if (apiResponse && handleAfterChangeStarred) {
      handleAfterChangeStarred({ ...projectData, isSaved: apiResponse.isSaved });
    }
  };
  
  function onLoadImage() {
    if (!isLoaded) setLoaded(true);
  }
  
  return (
    <div className="latest-project-item">
      <Link to={LINK.PROJECT_DETAIL.format(projectData?._id)} className="latest-project-item__thumbnail">
        <img
          className="latest-project-item__thumbnail-image"
          src={API.STREAM_ID.format(projectData?.imageId?.thumbnailFileId)}
          alt=""
          onLoad={onLoadImage}
        />
        {!isLoaded && <div className="latest-project-item__thumbnail-default">
          <img src={PROJECT_DEFAULT} alt="" />
        </div>}
        <div className="latest-project-item__thumbnail-backdrop">{t("VIEW_DETAIL")}</div>
      </Link>
      
      <div className="latest-project-info">
        <div className="latest-project-info__item">
          <img src={getProjectIcon(projectData)} alt='' className="latest-project-info__icon" />
          <Popover
            className="latest-project-info__name"
            placement="topLeft"
            content={projectData?.projectName}
            trigger="hover"
          >
            {projectData?.projectName}
          </Popover>
          
          <div className="latest-project-info__action">
            <ProjectActionsDropdown
              size="tiny"
              projectData={projectData}
              handleAfterCopy={handleAfterCopy}
              handleAfterRename={handleAfterRename}
              handleAfterDelete={handleAfterDelete}
              handleAfterMove={handleAfterMove}
            />
          </div>
        
        </div>
        <div className="latest-project-info__item">
          <div className="latest-project-info__last-modified">
            {dayjs(projectData?.updatedAt).fromNow()}
          </div>
          <Starred
            className="latest-project-info__star"
            visible={false}
            active={projectData?.isSaved}
            onClick={handleChangeStarred}
          />
        </div>
      
      </div>
    
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(LatestProjectItem);
