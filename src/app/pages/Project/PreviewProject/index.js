import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import { useProject } from "@app/pages/Project";

import { DownloadProject } from "@app/pages/Project/DownloadProject";
import ActionPopover from "@app/pages/Project/ProjectDetail/ActionPopover";

import { CONSTANT, PREVIEW_TYPE, TYPE_OF_TOOL } from "@constant";

import HtmlContent from "@component/HtmlContent";
import Question from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/Question";
import Answer from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/Answer";

import VideoPreview from "@app/pages/Project/ProjectDetail/ContentOutput/InputPreview/VideoPreview";
import AudioPreview from "@app/pages/Project/ProjectDetail/ContentOutput/InputPreview/AudioPreview";
import TextPreview from "@app/pages/Project/ProjectDetail/ContentOutput/InputPreview/TextPreview";
import TextTopicWordPreview from "@app/pages/Project/ProjectDetail/ContentOutput/InputPreview/TextTopicWordPreview";
import ImagePreview from "@app/pages/Project/ProjectDetail/ContentOutput/InputPreview/ImagePreview";
import AbcdQuestion from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/AbcdOutput/AbcdQuestion";

import AbcdAnswer from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/AbcdOutput/AbcdAnswer";
import OptionQuestion
  from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/SelectTitleOutput/OptionQuestion";
import OptionAnswer from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/SelectTitleOutput/OptionAnswer";
import OpenQuestion from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/OpenQuestionOutput/OpenQuestion";
import OpenQuestionAnswer
  from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/OpenQuestionOutput/OpenQuestionAnswer";
import TfQuestion from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TrueFalseOutput/TfQuestion";
import TfAnswer from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TrueFalseOutput/TfAnswer";

import Copy from "@component/SvgIcons/Copy";
import Download from "@component/SvgIcons/Download";

import "./PreviewProject.scss";

function PreviewProject({
                          hideOrder = false,
                          contentPreview,
                          previewType = PREVIEW_TYPE.ALL,
                          allowAction = false,
                          onlyPlaintext = false,
                          showDivider = false,
                        }) {
  const { t } = useTranslation();
  const { isNormal, isExam, examOrderSelected, isMarkSpeaking } = useProject();
  
  const [stateDownload, setStateDownload] = useState({
    isShowModal: false,
    contentDownload: null,
  });
  
  function handleCopy(content) {
    if (isExam) {
      const outputExam = content.responses.find(response => response.examOrder === examOrderSelected);
      navigator.clipboard.writeText(outputExam?.plaintext);
      return;
    }
    
    if (content?.response?.plaintext) {
      navigator.clipboard.writeText(content?.response?.plaintext);
    }
  }
  
  function handleShowDownload(contentDownload = null) {
    if (!!contentDownload) {
      setStateDownload({ isShowModal: true, contentDownload });
    } else {
      setStateDownload(prevState => Object.assign({}, prevState, { isShowModal: false }));
    }
  }
  
  function renderPreviewInput(content) {
    const response = isNormal
      ? content.responses?.find(response => response?.isActivate)
      : isExam ? content.responses?.find(response => response.examOrder === examOrderSelected)
        : null;
    
    if (!response) return null;
    
    const previewProps = {
      inputData: response.inputId?.inputData,
      toolInfo: content.toolId,
    };
    let xhtml;
    switch (content.toolId?.inputType) {
      case TYPE_OF_TOOL.VIDEO:
      case TYPE_OF_TOOL.OFFLINE_VIDEO:
        xhtml = <VideoPreview {...previewProps} />;
        break;
      case TYPE_OF_TOOL.AUDIO:
        xhtml = <AudioPreview {...previewProps} />;
        break;
      case TYPE_OF_TOOL.TEXT:
      case TYPE_OF_TOOL.TEXT_TO_SPEECH:
        xhtml = <TextPreview {...previewProps} />;
        break;
      case TYPE_OF_TOOL.TOPIC:
      case TYPE_OF_TOOL.WORDS:
        xhtml = <TextTopicWordPreview {...previewProps} />;
        break;
      case TYPE_OF_TOOL.IMAGE:
        xhtml = <ImagePreview {...previewProps} />;
        break;
      default:
        return null;
    }
    
    return <div className="preview-project__input">
      {xhtml}
    </div>;
  }
  
  function getResponseFromContent(content) {
    return isExam
      ? content.responses.find(response => response.examOrder === examOrderSelected)
      : content.response;
  }
  
  function renderOutputAnswer(content, index) {
    const response = getResponseFromContent(content);
    
    let xhtml = <></>;
    switch (response?.outputType) {
      case "abcd_question":
      case "multi_choice":
        xhtml = <AbcdAnswer correctAnswers={response.output?.correctAnswers} />;
        break;
      case "summary":
      case "three_titles":
      case "three_ideas":
      case "options":
        const correctOptionText = response.output.options?.find(option => option.optionId === response.output.correctOptionId)?.text;
        if (!correctOptionText) break;
        xhtml = <OptionAnswer
          correctOptionId={response.output.correctOptionId}
          correctOptionText={correctOptionText}
        />;
        break;
      case "open_question":
        xhtml = <OpenQuestionAnswer correctAnswers={response.output.correctAnswers} />;
        break;
      case "tf_question":
        xhtml = <TfAnswer correctAnswers={response.output?.correctAnswers} />;
        break;
      default:
        break;
    }
    
    return <div className="preview-project__answer">
      <div className="project-preview__title">
        {`${index + 1}. ${content.title}`}
      </div>
      {xhtml}
    </div>;
  }
  
  function renderOutput(content) {
    const response = content.response;
    
    if (onlyPlaintext) {
      return response?.plaintext
        ? <div className="preview-project__plaintext">{response?.plaintext}</div>
        : null;
    }
    
    switch (response?.outputType) {
      case "html":
        return <HtmlContent dangerouslySetInnerHTML={{ __html: response.output?.html }} />;
      case "html_questions":
        return <>
          <Question />
          <HtmlContent dangerouslySetInnerHTML={{ __html: response.output?.questionsHtml }} />
          {!!response.output?.answersHtml && <Answer>
            <HtmlContent dangerouslySetInnerHTML={{ __html: response.output.answersHtml }} />
          </Answer>}
        </>;
      case "text":
      case "picture_description":
        return response.output?.text;
      case "abcd_question":
      case "multi_choice":
        return <>
          <Question />
          <AbcdQuestion questions={response.output?.questions} />
          {previewType === PREVIEW_TYPE.ALL && <Answer>
            <AbcdAnswer correctAnswers={response.output?.correctAnswers} />
          </Answer>}
        </>;
      case "summary":
      case "three_titles":
      case "three_ideas":
      case "options":
        const correctOptionText = response.output.options?.find(option => option.optionId === response.output.correctOptionId)?.text;
        return <>
          <Question />
          <OptionQuestion options={response.output?.options} />
          {previewType === PREVIEW_TYPE.ALL && !!correctOptionText && <Answer>
            <OptionAnswer
              correctOptionId={response.output.correctOptionId}
              correctOptionText={correctOptionText}
            />
          </Answer>}
        </>;
      case "open_question":
        return <>
          <div className="font-bold">{t("QUESTION")}</div>
          <OpenQuestion options={response.output?.options} />
          {previewType === PREVIEW_TYPE.ALL && <Answer>
            <OpenQuestionAnswer correctAnswers={response.output.correctAnswers} />
          </Answer>}
        </>;
      case "tf_question":
        return <>
          <div className="font-bold">{t("QUESTION")}</div>
          <TfQuestion questions={response.output?.questions} />
          {previewType === PREVIEW_TYPE.ALL && <Answer>
            <TfAnswer correctAnswers={response.output?.correctAnswers} />
          </Answer>}
        </>;
      case "words":
        return <div>{response?.plaintext}</div>;
      case "audio":
        return <AudioPreview
          inputData={{
            audioId: response.output?.audio?.audioFileId,
            cutStart: 0,
            cutEnd: response.output?.audio?.duration,
          }}
          toolInfo={content.toolId}
        />;
      default:
        return <div>{response?.plaintext}</div>;
    }
  }
  
  
  if (isMarkSpeaking) return null;
  
  return <>
    <div className={clsx("preview-project",
      { "preview-project-plaintext": onlyPlaintext },
      { "preview-project-has-divider": showDivider },
    )}>
      {contentPreview
        .map(content => {
          content.response = getResponseFromContent(content);
          content.outputState = content.response?.state?.toUpperCase();
          return content;
        })
        .filter(content => !!content.response?.inputId?.inputData && content.outputState === CONSTANT.DONE)
        .map((content, index) => {
          return <div
            key={content._id}
            id={`js-project-preview-${content._id}`}
            className="preview-project__item">
            <div className="project-preview__title">
              {hideOrder
                ? content.title
                : `${content.contentIndex}. ${content.title}`}
            </div>
            
            {content.description && <div>{content.description}</div>}
            
            
            {renderPreviewInput(content)}
            
            
            {renderOutput(content)}
            
            {allowAction && content.response?.outputType !== "audio" && <div className="preview-project__action">
              <ActionPopover
                content="DOWNLOAD"
                icon={<Download />}
                onClick={() => handleShowDownload(content)}
              />
              <ActionPopover
                content="COPY"
                icon={<Copy />}
                onClick={() => handleCopy(content)}
              />
            </div>}
          </div>;
        })}
      
      {previewType === PREVIEW_TYPE.ANSWER_IN_BOTTOM &&
        <>
          <div className="font-bold">{t("ANSWER")}</div>
          {contentPreview.map((content, index) => renderOutputAnswer(content, index))}
        </>}
    
    </div>
    
    
    <DownloadProject
      open={stateDownload.isShowModal}
      onCancel={() => handleShowDownload()}
      fileName={stateDownload.contentDownload?.title}
      contentIdInitial={stateDownload.contentDownload?._id}
      downloadType={CONSTANT.CONTENT}
    />
  </>;
}

export default PreviewProject;