.project-segmented-preview {
  float: left;
  user-select: none;

  .ant-segmented {
    padding: 0;
    border-radius: 8px;

    .ant-segmented-thumb {
      border-radius: 8px;
    }

    .ant-segmented-item {
      border-radius: 8px;

      .ant-segmented-item-label {
        font-size: 16px;
        line-height: 1.25;
        padding: 5px 28px;
        display: flex;
        justify-content: center;
        align-items: center;

        .ant-segmented-item-icon {
          height: 20px;
          display: inline-flex;
          align-items: center;
        }
      }


      &:hover:not(.ant-segmented-item-selected):not(.ant-segmented-item-disabled) {
        //color: var(--blue);
      }

      &.ant-segmented-item-selected {
        //background-color: var(--primary-colours-blue);
        //color: var(--white);
      }
    }
  }
}