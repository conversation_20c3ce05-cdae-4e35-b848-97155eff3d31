.create-dataset-container {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .tool-studio-header {
    font-weight: bold;
  }

  .conversations-details {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .conversations-details-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      @media screen and (max-width: 768px) {
        display: block;
      }

      &__title {
        font-size: 16px;
        font-weight: 700;
      }
    }
  }

  .ant-form-item-label {
    label {
      height: unset !important;
    }
  }

  .ant-form-item-required {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
    padding: 0px;
    flex-direction: row-reverse;

    &:after {
      display: none;
    }
  }

}

.modal-createConversation-action {
  display: flex;
  justify-content: center;
  gap: 15px;

}

.form-conversation-details {

  .select-options-item {
    margin-bottom: 20px;
  }

  .ant-space {
    gap: 20px;

    > :first-child {
      width: 15% !important;
    }

    > :last-child {
      width: 5% !important;
    }

    .ant-space-item {
      width: 100%;
    }
  }
}