import React from "react";
import { Switch, Tooltip } from "antd";
import { useTranslation } from "react-i18next";

import { useSpeaking } from "@app/pages/Student/Speaking";
import { SPEAKING_ERROR } from "@constant";

;
import { cloneObj } from "@common/functionCommons";

import INFO_CIRCLE from "@src/asset/icon/info/info-circle.svg";

import "./SpeakingErrors.scss";

function SpeakingErrors() {
  const { t } = useTranslation();

  const { countErrors, showErrors, setShowErrors } = useSpeaking();


  function toggleShowError(errorKey, value) {
    setShowErrors(prevState => {
      const newState = cloneObj(prevState) || {};
      newState[errorKey] = value;
      return newState;
    });
  }

  return <div className="speaking-errors">
    {Object.values(SPEAKING_ERROR).map((speakingError) => {
      const isShow = showErrors?.[speakingError.key];

      return <div key={speakingError.key} className="speaking-errors-item">
        <div
          className="speaking-error__count"
          style={{
            color: speakingError.color,
            backgroundColor: speakingError.backgroundColor,
          }}
        >
          {countErrors?.[speakingError.key] || 0}
        </div>
        <div className="speaking-error__name">
          {t(speakingError.lang)}
        </div>
        <div className="speaking-error__info-icon">
          <Tooltip
            align={{ offset: [-11.5, -6] }}
            overlayClassName="speaking-error__info-tooltip"
            placement="topLeft"
            title={t(speakingError.descLang)}>
            <img src={INFO_CIRCLE} alt="" />
          </Tooltip>
        </div>
        <div
          className="speaking-error__switch"
        >
          <Switch
            size="small"
            value={isShow}
            onChange={() => toggleShowError(speakingError.key, !isShow)}
          />
          <span className="speaking-error__switch-status">
            {t(isShow ? "ON" : "OFF")}
          </span>
        </div>
      </div>;
    })}
  </div>;
}

export default SpeakingErrors;