@import "src/app/styles/scroll";


.speaking-feedback {
  //max-height: calc(100vh - 72px - 16px - 16px - 16px - 16px - 28px - 16px);

  border-radius: 16px;
  box-shadow: 0 10px 16px 0 #00000029;
  overflow: hidden;

  .speaking-feedback__header {
    padding: 16px;
    display: flex;
    flex-direction: row;
    gap: 8px;
    flex-wrap: wrap;
    background-color: #FFFFFF;

    .ant-btn {
      padding-left: 15px;
      padding-right: 15px;
      border-radius: 8px;
    }
  }

  .speaking-feedback__body {
    @extend .scrollbar;
    @extend .scrollbar-show;

    scrollbar-gutter: stable both-edges;
    border: 1px solid #DBDBDB;

    .speaking-feedback__body-content {
      padding: 15px 9px;
      display: flex;
      align-content: baseline;
      flex-direction: row;
      gap: 16px;
      flex-wrap: wrap;
      //background: radial-gradient(14.32% 13.82% at 30.8% 11.99%, #DCD7FF 0%, #E1DDFF 0%, #E4E0FF 0%, #FAFBFF 100%);
      //background: radial-gradient(14.32% 110px at 30.8% 87px, #DCD7FF 0%, #E1DDFF 0%, #E4E0FF 0%, #FAFBFF 100%);


      .speaking-feedback__score {
        align-self: start;
        width: 100%;
        display: flex;
        flex-direction: row;
        gap: 6px;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;


        .speaking-feedback__band-score {
          flex: 1;
          height: 150px;
          min-width: 190px;
          display: flex;
          flex-direction: column;
          text-align: center;
          justify-content: center;
          text-shadow: 0px 0px 90px #2000FF;

          .speaking-feedback-band-score__score {
            font-size: 60px;
            font-weight: 600;
            line-height: 78px;
            color: var(--navy);
          }

          .speaking-feedback-band-score__label {
            font-weight: 500;
            line-height: 20.8px;
          }
        }

        .speaking-chart {
          .speaking-result-chart {
            width: 321px;
          }
        }
      }

      .speaking-feedback__evaluations {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .speaking-feedback-evaluation {
          display: flex;
          flex-direction: column;
          gap: 16px;

          .speaking-feedback-evaluation__name {
            font-size: 22px;
            font-weight: 600;
            line-height: 30px;
            display: flex;
            gap: 7px;

            .speaking-feedback-evaluation__icon {
              height: 30px;
              display: flex;
              align-items: center;

              img {
                height: 24px;
              }
            }
          }

          .speaking-feedback-evaluation__feedback {

          }
        }
      }

      .speaking-feedback__action {
        border-top: 1px solid #C0BBD7;
        width: 100%;
        display: flex;
        flex-direction: row-reverse;
        padding-top: 8px;
        gap: 39px;

        >button {
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          padding: 0;
        }
      }
    }
  }

}