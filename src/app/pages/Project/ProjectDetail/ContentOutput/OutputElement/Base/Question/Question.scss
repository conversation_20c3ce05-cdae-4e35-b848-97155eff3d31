
.question-list {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .question-item {
    display: flex;
    flex-direction: row;
    border: 1px solid var(--lighttheme-content-background-stroke);
    border-radius: 16px;


    .question__move {
      width: 63px;
      border-right: 1px solid var(--lighttheme-content-background-stroke);
      padding: 15px;
      display: flex;
      flex-direction: column;
      gap: 24px;
      justify-content: center;
      align-items: center;


      .question__move-up, .question__move-down {

        &.ant-btn .ant-btn-icon svg {
          width: 24px;
          height: 24px;

          path {
            stroke: #858585;
          }
        }
      }
    }

    .question__content {
      flex: 1;
      padding: 24px;
    }
  }

  .question__add {
    align-self: center;
    box-shadow: var(--shadow-level-2);
    width: 40px;
    height: 40px;

    .ant-btn-icon svg {
      width: 20px;
      height: 20px;
    }
  }
}
