import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { useText } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm/InputForm/Text";

import UploadError from "@component/UploadError";
import DocumentDropZone from "./DocumentDropZone";
import PreviewAndCropImage from "./PreviewAndCropImage";

import { CONSTANT } from "@constant";

function UploadDocument({ ...props }) {
  const { t } = useTranslation();
  const { isShowTextFromDocument } = useText();
  const {previewData, setPreviewData, resetPreviewData, categorySelected} = props;
  
  const [crops, setCrops] = useState(null);
  const imageInputId = `image-input-${new Date().getTime()}`;
  
  const [error, setError] = useState("");
  
  function clearState() {
    setCrops(null);
    setError("");
    resetPreviewData();
  }
  
  function loadDocument(documentData) {
    setCrops(null);
    setError("");
    setPreviewData({ ...documentData, category: categorySelected });
  }
  
  useEffect(() => {
    if (isShowTextFromDocument) {
      clearState();
    }
  }, [isShowTextFromDocument]);
  
  
  const handleTryAgain = () => {
    document.getElementById(imageInputId).click();
  };

  const showPreviewData = previewData?.type === CONSTANT.DOCUMENT && previewData?.category === categorySelected
  
  if (categorySelected !== CONSTANT.MY_COMPUTER) return null;
  return <>
    <DocumentDropZone
      previewData={previewData}
      loadDocument={loadDocument}
      setError={setError}
    />
    
    <PreviewAndCropImage
      crops={crops} setCrops={setCrops}
      previewData={previewData}
      clearState={clearState}
    />
    
    <UploadError
      content={error}
      onCancel={clearState}
      onTryAgain={handleTryAgain}
    />
  </>;
}

export default UploadDocument;