import { createBase, getBase, getAllBase, joinParams } from "@services/Base";
import { API } from "@api";
import axios from "axios";
import { cloneObj, genQueryParam, genSearchFieldParam } from "@src/common/functionCommons";

// export const getWhitelistPagination = async (query = {}) => {
//   const queryObj = cloneObj(query);
//   queryObj.page ||= 1;
//   queryObj.limit ||= 0;
//   return axios
//     .get(API.WHITELIST_MANAGER, { params: queryObj })
//     .then((response) => {
//       if (response.status === 200) return response?.data;
//       return null;
//     })
//     .catch((err) => {
//       console.log("err", err);
//       return null;
//     });
// }

export async function getGroupFeedBack() {
  return getBase(API.FEEDBACK_GROUP);
}

export async function submitFeedback(data) {
  return createBase(API.SUBMIT_FEEDBACK, data);
}


export async function getFeedback(query) {
  return axios
    .get(API.FEEDBACK_STATISTIC, { params: query })
    .then((response) => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return null;
    });
};

export async function getFeedbackAnalysis(query) {
  return axios
    .get(API.FEEDBACK_ANALYSIS, { params: query })
    .then((response) => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return null;
    });
};

export async function getAllGroupFeedback(query) {
  return getAllBase(API.GROUP_FEEDBACK, query);
}

export async function checkAutoFeedback() {
  return getBase(API.FEEDBACK_CHECK_AUTO);
}
