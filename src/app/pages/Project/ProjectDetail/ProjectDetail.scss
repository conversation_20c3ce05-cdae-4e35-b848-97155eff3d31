.project-detail {
  gap: 24px;

  &:not(.project-detail-preview) {
    .preview-project {
      display: none;
    }
  }

  &.project-detail-preview {
    .project-content {
      display: flex;
      flex-direction: column;
      gap: 24px;
      background: var(--white);
      padding: 24px 32px;

      .project-content__inner, .exam-project-content {
        display: none;
      }
    }
  }

  .project-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    flex: 1;
  }

  .project-nodata {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .project-nodata__image {
      width: 160px;
      height: 160px;
    }

    .project-nodata__title {
      color: var(--primary-colours-blue);
      font-size: 32px;
      font-weight: 700;
    }

    .project-nodata__description {
      color: #858585;
      text-align: center;
    }
  }
}

.project-content__inner {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .title-input__content-index {
    display: none;
  }

  &.project-content-preview {
    background: var(--background-light-background-2);
    box-shadow: var(--shadow-level-2);
    border-radius: 8px;
    padding: 24px 32px;

    //.project-content-layout {
    //  background: transparent;
    //  box-shadow: none;
    //  padding: 0;
    //}
    //
    //.content-header, .project-content__header, .project-content-input,
    //.output-status, .download-media-container,
    //.title-input__action, .description-input__action {
    //  display: none;
    //}
    //
    //.preview-project__divider:not(:last-child) {
    //  display: block;
    //}
  }

  .preview-project__divider {
    margin: -8px 0;
    border-top: 1px dashed var(--support-colours-grey-light);
    display: none;
  }
}


// responsive project info mobile
.project-info__mobile {


  .project-info__dropdown-button {
    float: right;

    .ant-btn {
      padding: 5px 23px;
      font-weight: 600;

      .ant-btn-icon svg {
        transition: transform var(--transition-timing-level-2);
      }
    }

    &.ant-dropdown-open .ant-btn .ant-btn-icon svg {
      transform: rotate(-180deg);
    }
  }
}

.project-info__mobile-dropdown {
  z-index: 101;

  .ant-dropdown-menu {
    padding: 0;

    .ant-dropdown-menu-item {
      padding: 0;
      cursor: unset;

      .project__info {
        width: 248px;

        .project__info-content {
          max-height: calc(100vh - 200px);
        }
      }
    }
  }
}

// end responsive project info mobile


// responsive layout project detail


$projectColWidth: 248px;

@media screen and (min-width: 1280px) {
  .project-detail {
    display: flex;

    .project-content {
      width: calc(100% - ($projectColWidth + 24px) * 2);
    }
  }
}

@media screen and (min-width: 1024px) and (max-width: 1279.98px) {
  $projectGapTablet: 16px;

  .project-detail {
    display: flex;
    gap: $projectGapTablet;

    .project-content {
      width: calc(100% - $projectColWidth - $projectGapTablet);
    }
  }
}

@media screen and (max-width: 1023.98px) {
  .project-detail {
    display: flex;
    gap: 16px;

    .project-content {
      width: calc(100% - 24px - 40px);
    }
  }
}


// todo: responsive < 768