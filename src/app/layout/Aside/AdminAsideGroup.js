import React, {useState, useEffect, useMemo} from "react";
import {useTranslation} from "react-i18next";
import {useLocation} from "react-router-dom";
import {Collapse, Badge, Tooltip} from "antd";
import clsx from "clsx";

import "./AdminAsideGroup.scss";

import AsideItem from "./AsideItem";
import {LINK} from "@link";

// Import icons
import TOOLS from "@src/asset/aside/tool.svg";
import TOOLS_ACTIVE from "@src/asset/aside/tool-active.svg";

import TOKEN_INSTRUCTION from "@src/asset/aside/token-instruction.svg";
import TOKEN_INSTRUCTION_ACTIVE from "@src/asset/aside/token-instruction-active.svg";

import TOKEN_TOOLS from "@src/asset/aside/token-tool.svg";
import TOKEN_TOOLS_ACTIVE from "@src/asset/aside/token-tool-active.svg";

import TEMPLATE from "@src/asset/aside/template.svg";
import TEMPLATE_ACTIVE from "@src/asset/aside/template-active.svg";

import SHARE_WITH_ME from "@src/asset/aside/share-with-me.svg";
import SHARE_WITH_ME_ACTIVE from "@src/asset/aside/share-with-me-active.svg";

import PERSONA from "@src/asset/aside/persona.svg";
import PERSONA_ACTIVE from "@src/asset/aside/persona-active.svg";

import GROUP_TOOL from "@src/asset/aside/group-tool.svg";
import GROUP_TOOL_ACTIVE from "@src/asset/aside/group-tool-active.svg";

import PACKAGE_SETTINGS from "@src/asset/aside/package-settings.svg";
import PACKAGE_SETTINGS_ACTIVE from "@src/asset/aside/package-settings-active.svg";

// Group icons
import CONTENT_ICON from "@src/asset/aside/template.svg";
import CONTENT_ICON_ACTIVE from "@src/asset/aside/template-active.svg";

import USER_ICON from "@src/asset/aside/persona.svg";
import USER_ICON_ACTIVE from "@src/asset/aside/persona-active.svg";

import BILLING_ICON from "@src/asset/aside/resource.svg";
import BILLING_ICON_ACTIVE from "@src/asset/aside/resource-active.svg";

import ANALYTICS_ICON from "@src/asset/aside/dashboard.svg";
import ANALYTICS_ICON_ACTIVE from "@src/asset/aside/dashboard-active.svg";

import SYSTEM_ICON from "@src/asset/aside/token-instruction.svg";
import SYSTEM_ICON_ACTIVE from "@src/asset/aside/token-instruction-active.svg";

import EMAIL_ICON from "@src/asset/aside/share-with-me.svg";
import EMAIL_ICON_ACTIVE from "@src/asset/aside/share-with-me-active.svg";

const {Panel} = Collapse;

// Define the groups and their items
const ADMIN_GROUPS = [
  {
    key: "content",
    title: "CONTENT_MANAGEMENT",
    img: CONTENT_ICON,
    imgActive: CONTENT_ICON_ACTIVE,
    items: [
      {
        linkTo: LINK.ADMIN_INSTRUCTION,
        title: "INSTRUCTION",
        img: TOKEN_INSTRUCTION,
        imgActive: TOKEN_INSTRUCTION_ACTIVE
      },
      {
        linkTo: LINK.ADMIN_EXPLAIN,
        title: "EXPLAIN",
        img: TOKEN_INSTRUCTION,
        imgActive: TOKEN_INSTRUCTION_ACTIVE
      },
      {
        linkTo: LINK.ADMIN_TOOL,
        title: "TOOLS",
        img: TOOLS,
        imgActive: TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN_PAGE + LINK.TOOL_GROUP,
        title: "GROUP_TOOL",
        img: GROUP_TOOL,
        imgActive: GROUP_TOOL_ACTIVE
      },
      // {
      //   linkTo: LINK.ADMIN_PAGE + LINK.TOOL_STUDIO_KNOWLEDGE,
      //   title: "KNOWLEDGE",
      //   img: TOKEN_INSTRUCTION,
      //   imgActive: TOKEN_INSTRUCTION_ACTIVE
      // },
      {
        linkTo: LINK.ADMIN_OPTIONS,
        title: "OPTIONS",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN_OUTPUT_TYPE,
        title: "OUTPUT_TYPE",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT,
        title: "DICTATION_AND_SHADOWING",
        img: TEMPLATE,
        imgActive: TEMPLATE_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.SPEAKING_EXERCISE,
        title: "SPEAKING_EXERCISE",
        img: TEMPLATE,
        imgActive: TEMPLATE_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.DOCUMENT_TEMPLATE,
        title: "DOCUMENT_TEMPLATE",
        img: TEMPLATE,
        imgActive: TEMPLATE_ACTIVE
      }
    ]
  },
  {
    key: "user",
    title: "USER_MANAGEMENT",
    img: USER_ICON,
    imgActive: USER_ICON_ACTIVE,
    items: [
      // {
      //   linkTo: LINK.ADMIN.USER,
      //   title: "USER",
      //   img: TOKEN_TOOLS,
      //   imgActive: TOKEN_TOOLS_ACTIVE
      // },
      {
        linkTo: LINK.ADMIN_PAGE + LINK.USER_TRACKING + "?is-developer=false&only-active-user=true&sort=-lastVisit&time=month",
        title: "USER_TRACKING",
        img: SHARE_WITH_ME,
        imgActive: SHARE_WITH_ME_ACTIVE
      },
      {
        linkTo: LINK.ADMIN_PAGE + LINK.WHITELIST,
        title: "WHITELIST",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN_PAGE + LINK.WAITING_LIST,
        title: "WAITING_LIST",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.ORGANIZATION,
        title: "ORGANIZATION",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },

    ]
  },
  {
    key: "billing",
    title: "BILLING_AND_PROMOTION",
    img: BILLING_ICON,
    imgActive: BILLING_ICON_ACTIVE,
    items: [
      {
        linkTo: LINK.ADMIN_CUSTOMER,
        title: "CUSTOMER",
        img: SHARE_WITH_ME,
        imgActive: SHARE_WITH_ME_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.TRANSACTION_HISTORY,
        title: "TRANSACTION_HISTORY",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.DISCOUNT,
        title: "DISCOUNT",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.PROMOTION,
        title: "PROMOTION",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      }
    ]
  },
  {
    key: "analytics",
    title: "ANALYTICS_AND_REPORTING",
    img: ANALYTICS_ICON,
    imgActive: ANALYTICS_ICON_ACTIVE,
    items: [
      // {
      //   linkTo: LINK.ADMIN_PAGE + LINK.AVERAGE_TOKEN,
      //   title: "AVERAGE_TOKEN",
      //   img: TOKEN_TOOLS,
      //   imgActive: TOKEN_TOOLS_ACTIVE
      // },
      {
        linkTo: LINK.ADMIN.FEEDBACK_ANALYSIS,
        title: "FEEDBACK_ANALYSIS",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.FEEDBACK_STATISTIC,
        title: "FEEDBACK_STATISTICS",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN_PAGE + LINK.OPENAI_COST + "?time=month",
        title: "OPENAI_COST",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.ERROR_REPORTS,
        title: "ERROR_REPORTS",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      // {
      //   linkTo: LINK.ADMIN_PAGE + LINK.TOTAL_TOKEN_BY_USER,
      //   title: "TOTAL_TOKEN_BY_USER",
      //   img: TOKEN_TOOLS,
      //   imgActive: TOKEN_TOOLS_ACTIVE
      // }
    ]
  },
  {
    key: "email_marketing",
    title: "EMAIL_MARKETING",
    img: EMAIL_ICON,
    imgActive: EMAIL_ICON_ACTIVE,
    items: [
      {
        linkTo: LINK.ADMIN.EMAIL_CAMPAIGN,
        title: "EMAIL_CAMPAIGN_MANAGEMENT",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.EMAIL_GROUP,
        title: "EMAIL_GROUP_MANAGEMENT",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.EMAIL_TEMPLATE,
        title: "EMAIL_TEMPLATE_MANAGEMENT",
        img: TEMPLATE,
        imgActive: TEMPLATE_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.EMAIL_STATISTICS,
        title: "EMAIL_STATISTICS",
        img: TOKEN_INSTRUCTION,
        imgActive: TOKEN_INSTRUCTION_ACTIVE
      }
    ]
  },
  {
    key: "system",
    title: "SYSTEM_CONFIGURATION",
    img: SYSTEM_ICON,
    imgActive: SYSTEM_ICON_ACTIVE,
    items: [
      {
        linkTo: LINK.PACKAGE,
        title: "PACKAGE_SETTING",
        img: PACKAGE_SETTINGS,
        imgActive: PACKAGE_SETTINGS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN_PAGE + LINK.PERSONA,
        title: "PERSONA",
        img: PERSONA,
        imgActive: PERSONA_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.API_KEY,
        title: "API_KEY",
        img: TEMPLATE,
        imgActive: TEMPLATE_ACTIVE
      },
      {
        linkTo: LINK.ADMIN_PAGE + LINK.GPTMODEL,
        title: "GPT_MODEL",
        img: TOKEN_INSTRUCTION,
        imgActive: TOKEN_INSTRUCTION_ACTIVE
      },
      {
        linkTo: LINK.ADMIN_PAGE + LINK.VOICE_OPTIONS,
        title: "VOICE_OPTIONS",
        img: TOOLS,
        imgActive: TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.SUPPORT_BUSINESS,
        title: "SUPPORT_BUSINESS",
        img: TOKEN_TOOLS,
        imgActive: TOKEN_TOOLS_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.SETTING,
        title: "SETTINGS",
        img: TOKEN_INSTRUCTION,
        imgActive: TOKEN_INSTRUCTION_ACTIVE
      },
    ]
  }
];

const AdminAsideGroup = () => {
  const {t, i18n} = useTranslation();
  const location = useLocation();
  const [activeKeys, setActiveKeys] = useState([]);
  const [activeGroup, setActiveGroup] = useState(null);

  // Find which group contains the current path
  useEffect(() => {
    const currentPath = location.pathname;

    // Find the group that contains the current path
    const foundGroup = ADMIN_GROUPS.find(group =>
      group.items.some(item => currentPath.includes(item.linkTo))
    );

    if (foundGroup && foundGroup.key !== activeGroup) {
      setActiveGroup(foundGroup.key);
      setActiveKeys([foundGroup.key]);
    }
  }, [location.pathname]);

  // Save active keys to localStorage when they change
  useEffect(() => {
    if (activeKeys.length > 0) {
      localStorage.setItem('adminAsideActiveKeys', JSON.stringify(activeKeys));
    }
  }, [activeKeys]);

  // Load active keys from localStorage on component mount
  useEffect(() => {
    const savedKeys = localStorage.getItem('adminAsideActiveKeys');
    if (savedKeys) {
      setActiveKeys(JSON.parse(savedKeys));
    }
  }, []);

  // Reset active keys when navigating away from admin page and back
  useEffect(() => {
    const handleStorageChange = () => {
      const isAdmin = location.pathname.includes(LINK.ADMIN_PAGE);
      if (!isAdmin) {
        localStorage.removeItem('adminAsideActiveKeys');
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const handleCollapseChange = (keys) => {
    setActiveKeys(keys);
  };

  // Define which items should have a "New" badge
  const newItems = [
    // LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT,
    // LINK.ADMIN.SPEAKING_EXERCISE,
    // LINK.ADMIN_PAGE + LINK.USER_TRACKING + "?time=month",
  ];

  // Determine if a group is active
  const isGroupActive = (groupKey) => {
    return activeKeys.includes(groupKey);
  };

  // Render a group header with translation
  const renderGroupHeader = (group) => {
    const isActive = isGroupActive(group.key);
    return (
      <Tooltip title={t(group.title)} placement="right" mouseEnterDelay={1}>
        <div className="group-header">
          <img
            src={isActive ? group.imgActive : group.img}
            alt=""
            className="group-icon"
          />
          <span className="group-title">{t(group.title)}</span>
        </div>
      </Tooltip>
    );
  };

  // Render a menu item with translation
  const renderMenuItem = (item, groupKey, index) => {
    const isNewItem = newItems.includes(item.linkTo);
    return (
      <div key={`${groupKey}-${index}`} className={isNewItem ? 'badge-new' : ''}>
        <AsideItem
          linkTo={item.linkTo}
          title={t(item.title)}
          img={item.img}
          imgActive={item.imgActive}
        />
      </div>
    );
  };

  return (
    <div className="admin-aside-container">
      <Collapse
        activeKey={activeKeys}
        onChange={handleCollapseChange}
        expandIconPosition="end"
        ghost
        className="aside-collapse admin-aside-groups"
      >
        {ADMIN_GROUPS.map(group => (
          <Panel
            key={group.key}
            header={renderGroupHeader(group)}
            className="admin-group-panel"
          >
            <div className="group-items">
              {group.items.map((item, index) => renderMenuItem(item, group.key, index))}
            </div>
          </Panel>
        ))}
      </Collapse>
    </div>
  );
};

export default AdminAsideGroup;
