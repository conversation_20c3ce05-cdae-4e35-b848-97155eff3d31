.speaking-exercise-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .speaking-exercise-info-card,
  .speaking-exercise-search-card,
  .speaking-exercise-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .speaking-exercise-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .speaking-exercise-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .speaking-exercise-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create-exercise {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  // Search form styles
  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .form-filter {
    width: 100%;
  }

  .ant-form-item {
    margin: 0;
  }

  // Tag styles
  .ant-tag {
    padding: 2px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
  }

  // Table styles
  .speaking-exercise-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .speaking-exercise-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .exercise-title-value {
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      font-size: 14px;
    }

    .exercise-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 8px;

      .btn-edit-exercise,
      .btn-delete-exercise {
        &:hover {
          background: var(--background-hover);
        }
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .speaking-exercise-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .btn-create-exercise {
        width: 100%;
        justify-content: center;
      }
    }

    .form-filter {
      .search-buttons {
        margin-top: 16px;
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}
