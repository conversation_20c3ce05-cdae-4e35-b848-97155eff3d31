.voice-modal__content {
  .upload-file-item {
    .ant-form-item-control-input-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .file-upload {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 12px 16px;
      background-color: var(--background-light-background-grey);
      border-radius: 8px;
      
      .file-upload__file-name {
        flex-grow: 1;
        font-weight: 500;
        color: var(--primary-colours-blue-navy);
      }
    }
  }
  
  .voice-upload-container {
    margin-top: 16px;
    width: 100%;
  }
  
  .voice-upload-dropzone {
    border: 2px dashed var(--background-light-background-grey);
    border-radius: 8px;
    padding: 24px;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      border-color: var(--primary-colours-blue-navy);
    }
  }
  
  .voice-upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    padding: 32px 0;
    
    .voice-upload-icon {
      font-size: 48px;
      color: var(--primary-colours-blue-navy-light-2);
    }
    
    .voice-upload-text {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      text-align: center;
      
      p {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
      
      span {
        color: var(--typo-colours-support-grey-light);
      }
    }
    
    .voice-upload-formats {
      color: var(--typo-colours-support-grey-light);
      font-size: 14px;
    }
  }
}

.hidden {
  display: none;
}
