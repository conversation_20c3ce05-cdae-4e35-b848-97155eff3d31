import React from "react";

import './TfQuestion.scss'

function TrueFalseQuestion({questions}){
  
  return <div className="true-false-output__question-container">
    {questions?.map((question, index) => {
      return <div key={index} className="true-false-output__question-item">
        <div className="true-false-output__question-text">
          <div className="true-false-output__question-index">
            <span className={"js-index-question-margin"}> {question.questionId}</span>
          </div>
          {question.question}
        </div>
      </div>;
    })}
  </div>
}

export default TrueFalseQuestion