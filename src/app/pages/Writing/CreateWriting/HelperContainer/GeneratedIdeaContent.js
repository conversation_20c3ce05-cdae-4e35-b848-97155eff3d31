import React from 'react';
import PropTypes from 'prop-types';
import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useTranslation } from 'react-i18next';
import './HelperContainer.scss';

const GeneratedIdeaContent = ({ content, isLoading }) => {
  const { t } = useTranslation();

  if (isLoading) {
    return <div className="generated-idea-content generated-idea-content--loading">{t('GENERATING_IDEAS')}</div>;
  }

  if (typeof content !== 'string') {
    console.error('GeneratedIdeaContent: content prop must be a string, received:', typeof content, content);
    return <div className="generated-idea-content generated-idea-content--error">{t('INVALID_CONTENT')}</div>;
  }

  if (!content.trim()) {
    return null;
  }

  try {
    return (
      <div className="generated-idea-content">
        <Markdown
          remarkPlugins={[remarkGfm]}
        >
          {content}
        </Markdown>
      </div>
    );
  } catch (error) {
    console.error('Error rendering Markdown:', error, 'Content was:', content);
    return <div className="generated-idea-content generated-idea-content--error">{t('MARKDOWN_DISPLAY_ERROR')}</div>;
  }
};

GeneratedIdeaContent.propTypes = {
  content: PropTypes.string,
  isLoading: PropTypes.bool,
};

export default GeneratedIdeaContent;
