
import { useTranslation } from 'react-i18next';
import { Image } from 'antd';
import './ResultInput.scss';
import { API } from '@api';
import { ZoomInOutlined } from '@ant-design/icons';
import { getImageDetail } from '@src/app/services/Image';
import { useEffect, useMemo, useState } from 'react';
import CustomCKEditor from "@src/app/component/CKEditor";
import { stringSplitWithLineBreak } from '@src/common/functionCommons';
import PreviewImageEssay from '../../CreateWriting/PreviewImageEssay';
import PreviewPdfEssay from '../../CreateWriting/PreviewPdfEssay';
import TagItem from '@src/app/pages/Student/MyAssign/FilterAssign/FillterTag/TagItem';

const ResultInput = (props) => {
  const { t } = useTranslation();
  const { inputData, essayText, isShowTopicImage, category, tag } = props;

  const [topicImageFileId, setTopicImageFileId] = useState(null);

  useEffect(() => {
    if (inputData?.topicImageId) {
      getImageData(inputData?.topicImageId);
    }
  }, [inputData]);

  async function getImageData(imageId) {
    const apiResponse = await getImageDetail(imageId);
    setTopicImageFileId(apiResponse.imageFileId);
  }

  const countEssay = useMemo(() => {
    if (!essayText) return 0;
    return stringSplitWithLineBreak(essayText).length;
  }, [essayText]);

  if (!inputData) return null;
  return (
    <div className="result-writing__input">
      <div className="writing-input-section">
        <div className="writing-input-section__title">{t("TOPIC")}</div>
        <div className="writing-input-section__topic">{inputData.topic}</div>
        {(category || tag) && <div className="writing-input-section__extra">
          {category && <div className="writing-input-section__extra_item">
            <span className="extra_item__label">{t("TYPE_OF_ESSAY")}</span>
            <span className="extra_item__category">{category}</span>
          </div>}

          {tag && <div className="writing-input-section__extra_item">
            <span className="extra_item__label">{t("TAG")}</span>
            <TagItem tag={tag} />
          </div>}
        </div>}
      </div>
      {isShowTopicImage && <div className="writing-input-section">
        <div className="writing-input-section__title">{t("REQUIREMENT_IMAGE")}</div>
        <div className="writing-input-section__image-topic">
          <Image
            src={API.STREAM_ID.format(topicImageFileId)}
            preview={{ mask: <ZoomInOutlined className="image-topic__preview__icon" /> }}
          />
        </div>
      </div>}
      <div className="writing-input-section">
        <div className="writing-input-section__title">{t("ESSAY")}</div>
        <div className="writing-input-section__content">
          {inputData?.imageIds?.length && <PreviewImageEssay imageIds={inputData?.imageIds} isResult />}
          {inputData?.fileId
            && <PreviewPdfEssay
              fileId={inputData?.fileId}
              pageRange={{ startPage: inputData?.startPage, endPage: inputData?.endPage }}
              isResult />
          }
          {essayText && <>
            <CustomCKEditor
              value={essayText}
              disabled
            />
            <span className="writing-input-section__content__count">
              {`${countEssay} ${t(countEssay > 1 ? t("WORDS") : t("WORD"))}`}
            </span></>}
        </div>
      </div>
    </div >
  )
}

export default ResultInput