import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Collapse } from "antd";
import { useTranslation } from "react-i18next";
import <PERSON><PERSON> from "lottie-react";
import _ from "lodash";

import { confirm } from "@component/ConfirmProvider";
import { useProject } from "@app/pages/Project";

import Loading from "@component/Loading";
import AntButton from "@component/AntButton";
import ActionPopover from "@app/pages/Project/ProjectDetail/ActionPopover";
import ExamOutputItem from "@app/pages/Project/ProjectDetail/ExamProjectContent/ExamProjectOutput/ExamOutputItem";
import { DownloadProject } from "@app/pages/Project/DownloadProject";

import { BUTTON, CONSTANT } from "@constant";

import { cloneObj } from "@common/functionCommons";
import { extractIds } from "@common/dataConverter";
import { updateProject } from "@services/Project";
import { deleteManyResponse } from "@services/Response";

import Minimize from "@component/SvgIcons/Minimize";
import Maximize from "@component/SvgIcons/Maximize";
import ChevronLeft from "@component/SvgIcons/ChevronLeft";
import ChevronRight from "@component/SvgIcons/ChevronRight";
import Download from "@component/SvgIcons/Download";
import Trash from "@component/SvgIcons/Trash";
import SunIcon from "@component/SvgIcons/SunIcon";

import * as loadingAnimation from "@src/asset/animations/loading.json";

import "./ExamProjectOutput.scss";

function ExamProjectOutput({ isSubmitting }) {
  const { t } = useTranslation();
  const { projectId, setProjectContentData, examOrderSelected, setExamOrderSelected } = useProject();
  const { examOutputData, totalExam } = useProject();
  const [isExpandOutput, setExpandOutput] = useState(true);
  
  const [isShowDownload, setShowDownload] = useState(false);
  
  
  const outputSelected = useMemo(() => examOutputData[examOrderSelected], [examOutputData, examOrderSelected]);
  
  useEffect(() => {
    return () => {
      debouncedUpdateExamActive.cancel();
    };
  }, [debouncedUpdateExamActive]);
  
  const debouncedUpdateExamActive = useCallback(_.debounce(handleUpdateExamActive, 800), []);
  
  async function handleUpdateExamActive(value) {
    const dataRequest = { _id: projectId, activeExam: value };
    await updateProject(dataRequest);
  }
  
  
  async function handleChangeExamActive(type) {
    let examOrderTemp = examOrderSelected;
    switch (type) {
      case CONSTANT.PREV:
        examOrderTemp -= 1;
        //setExamOrderSelected(prevState => prevState - 1)
        break;
      case CONSTANT.NEXT:
        examOrderTemp += 1;
        //setExamOrderSelected(prevState => prevState + 1)
        break;
      default:
        break;
    }
    
    setExamOrderSelected(examOrderTemp);
    
    debouncedUpdateExamActive(examOrderTemp);
  }
  
  function onDeleteOutput() {
    confirm.delete({
      content: t("CONFIRM_DELETE_EXAM"),
      handleConfirm: async () => {
        const responseIds = extractIds(outputSelected);
        const apiResponse = await deleteManyResponse(projectId, responseIds);
        
        if (Array.isArray(apiResponse)) {
          setProjectContentData(prevState => {
            const newState = cloneObj(prevState);
            return newState.map(state => {
              const responseDeleted = apiResponse.find(response => response.contentId === state._id);
              state.responses = state.responses?.filter(response => response._id !== responseDeleted?._id)
                                     .map(response => {
                                       if (response.examOrder > examOrderSelected) {
                                         response.examOrder -= 1;
                                       }
                                       return response;
                                     });
              return state;
            });
          });
          
          if (examOrderSelected === totalExam && examOrderSelected !== 1) {
            setExamOrderSelected(examOrderSelected - 1);
            debouncedUpdateExamActive(examOrderSelected - 1);
          }
        }
      },
    });
  }
  
  if (!totalExam) return null;
  
  return <div id="js-exam-output">
    <Collapse
      className="exam-project-collapse exam-project-output-container"
      ghost
      expandIcon={null}
      activeKey={isExpandOutput ? [CONSTANT.OUTPUT] : []}
      items={[{
        key: CONSTANT.OUTPUT,
        className: "",
        label: <>
          {t("EXAM_NUMBER").format(examOrderSelected)}
          
          <div className="exam-project-output__change-output">
            <AntButton
              size="xsmall"
              type={BUTTON.WHITE_BLUE}
              className="change-output__prev"
              icon={<ChevronLeft />}
              disabled={examOrderSelected === 1}
              //onClick={() => }
              onClick={() => handleChangeExamActive(CONSTANT.PREV)}
            />
            <div className="change-output__index">
              {examOrderSelected}/{totalExam}
            </div>
            <AntButton
              size="xsmall"
              type={BUTTON.WHITE_BLUE}
              className="change-output__next"
              icon={<ChevronRight />}
              disabled={examOrderSelected === totalExam}
              onClick={() => handleChangeExamActive(CONSTANT.NEXT)}
            />
          </div>
          
          <div className="exam-project-output__header-action">
            <ActionPopover
              size="small"
              content="DOWNLOAD"
              icon={<Download />}
              disabled={isSubmitting}
              onClick={() => setShowDownload(true)}
            />
            
            <ActionPopover
              size="small"
              content="DELETE"
              icon={<Trash />}
              disabled={isSubmitting}
              onClick={onDeleteOutput}
            />
            
            <AntButton
              size="small"
              type={BUTTON.LIGHT_NAVY}
              onClick={() => setExpandOutput(prevState => !prevState)}
              icon={isExpandOutput ? <Minimize /> : <Maximize />}
            >
              {t(isExpandOutput ? "COLLAPSE" : "EXPAND")}
            </AntButton>
          </div>
        </>,
        children: isSubmitting
          ? <div className="exam-project-output__loading">
            <div className="exam-project-output__loading-title">
              <SunIcon />
              <span>{t("HOLD_TIGHT!")}</span>
            </div>
            
            <Lottie animationData={loadingAnimation} loop={true} style={{ height: 150, width: 150 }} />
          </div>
          : <Loading active={false} className="exam-project-output__body">
            {outputSelected?.map(output => {
              return <ExamOutputItem key={output._id} output={output} />;
            })}
          </Loading>,
      }]}
    />
    
    
    <DownloadProject
      open={isShowDownload}
      onCancel={() => setShowDownload(false)}
      downloadType={CONSTANT.PROJECT}
    />
  
  </div>;
}

export default ExamProjectOutput;