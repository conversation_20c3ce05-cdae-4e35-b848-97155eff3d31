.resource-video-container {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 24px;

  @media screen and (max-width: 1535.98px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  @media screen and (max-width: 1366px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  @media screen and (max-width: 1100px) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  @media screen and (max-width: 800px) {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .resource-video-item {
    padding: 16px;
    background: #FFFFFF;
    border-radius: 8px;
    box-shadow: var(--shadow-level-3);
    display: flex;
    flex-direction: column;
    gap: 8px;


    .resource-video-embed {
      aspect-ratio: 16 / 9;
      cursor: pointer;
      display: flex;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .resource-video__footer {
      display: flex;
      flex-direction: row;
      gap: 8px;
      justify-content: space-between;

      .resource-video__info {
        .resource-video__title * {
          overflow-wrap: anywhere;
        }

        .resource-video__update-at {
          font-size: 10px;
          color: var(--typo-colours-support-blue-light);
          line-height: 20px;
          margin-top: auto;
        }

        .resource-video__youtube-icon{
          margin-right: 4px;
          margin-bottom: -2px;
        }
      }

      .resource-video__action {
        >* {
          margin-top: 4px;
        }
      }
    }
  }
}