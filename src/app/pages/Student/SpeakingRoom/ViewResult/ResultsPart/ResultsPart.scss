.result-part {

  &__audio {
    // padding: 8px;
    border-radius: 16px;
    background-color: #ECF0F4;
  }

  &__header {
    display: flex;
    gap: 7px;
    align-items: center;

    span {
      font-size: 22px;
      line-height: 30px;
      font-weight: 600;
      color: #3A18CE;
    }
  }

  &__question {
    margin-top: 16px;
    gap: 16px;
    display: flex;
    flex-direction: column;

    &__name {
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
    }
  }

  &__body {
    gap: 16px;
    display: flex;
    flex-direction: column;

    &__show {
      gap: 45px;
      display: flex;
      padding: 0 350px;

      &__box {
        max-width: 293px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        &__result {
          font-weight: 700;
          font-size: 57px;
          line-height: 70px;
          color: #09196B;

          box-shadow: 0px 4px 120px rgba(32, 0, 255, 0.2);
          background-color: rgba(32, 0, 255, 0.04);
          border-radius: 100%;
        }
      }

      &__criteria {
        max-width: 274px;
        width: 100%;
        padding: 16px;
        border-radius: 24px;
        background: radial-gradient(18.71% 33.59% at 50% 8.03%, #f4f3ff 0.01%, #FFFFFF 100%);
        box-shadow: 0px 4px 32px rgba(0, 0, 0, 0.08);

      }
    }

    &__feedback {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      // display: flex;
      // gap: 4px            

      &__header {
        font-weight: 700;
        margin: 0;
        font-size: 16px;
        line-height: 24px;
      }

      &__content {
        display: flex;
        gap: 4px;
        cursor: pointer;
      }
    }


    &__comment {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;

      &__header {
        font-weight: 700;
        margin: 0;
        color: #26D06D;
      }

      &__content {
        p {
          margin: 0;
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
        }

        strong {
          font-weight: 500;
          line-height: 24px;
          font-size: 16px;
        }

        ul {
          margin: 0;
        }

        h3 {
          margin: 0;
          font-weight: 500;
          font-weight: 500;
          line-height: 24px;
          font-size: 16px;
        }

        h4 {
          margin: 0;
          font-weight: 500;
          font-weight: 500;
          line-height: 24px;
          font-size: 16px;
        }

        li {
          margin: 0;
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
        }
      }
    }
  }

  .result-part__body__feedback__header {
    font-weight: 700;
    margin: 0;
    font-size: 16px;
    line-height: 24px;
    // margin-top: 1.5px;
  }

  .result-part__body__feedback__signage {
    display: flex;
    align-items: center;
    gap: 16px;

    .result-part__body__feedback__signag__ingredient {
      display: flex;
      align-items: center;
      gap: 4px;

      span {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
      }
    }
  }

  .result-part__body__feedback__content {
    font-weight: 400;
    margin: 0;
    font-size: 16px;
    line-height: 24px;
    // margin-top: 1.5px;    
  }

  .result-part__no-answer {
    display: flex;
    align-items: center;
    gap: 8px;

    p {
      margin: 0;
    }
  }

}