import React, { useEffect, useMemo, useState } from "react";
import { Col, Form, Input, Row, Table } from "antd";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { EditOutlined, PlusOutlined } from "@ant-design/icons";

import { createKnowledge, deleteKnowledge, getPaginationKnowledge, updateKnowledge } from "@services/Knowledge";
import { handlePagingData } from "@common/dataConverter";
import { handleReplaceUrlSearch, handleSearchParams, orderColumn, paginationConfig } from "@common/functionCommons";

import { toast } from "@component/ToastProvider";
import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import { confirm } from "@component/ConfirmProvider";
import KnowledgeModal from "./KnowledgeModal";

import { BUTTON, PAGINATION_INIT } from "@constant";

import { LINK } from "@link";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import "./Knowledge.scss";

const Knowledge = () => {
  const [knowledgeData, setKnowledgeData] = useState(PAGINATION_INIT);
  const [rowSelected, setRowSelected] = useState(null);
  const [modalState, setModalState] = useState({
    visible: false,
    knowledge: null,
  });

  const { i18n, t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [formFilter] = Form.useForm();


  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getKnowledgeData(paging, query);
  }, [location.search]);

  async function getKnowledgeData(paging = knowledgeData.paging, query = knowledgeData.query) {
    const dataResponse = await getPaginationKnowledge(paging, query);
    if (dataResponse) {
      setKnowledgeData(handlePagingData(dataResponse, query));
      setRowSelected(dataResponse.rows[0]);
    }
  }

  const handleDelete = (knowledgeId) => {
    confirm.delete({
      content: t("CONFIRM_DELETE_KNOWLEDGE"),
      handleConfirm: async (e) => {
        const apiResponse = await deleteKnowledge(knowledgeId, true);
        if (apiResponse) {
          toast.success("DELETE_KNOWLEDGE_SUCCESS");
          let paging = { ...knowledgeData.paging };
          const { total, pageSize, page } = paging;
          if (page > 1 && ((total - 1) === pageSize * (page - 1))) {
            paging.page -= 1;
          }
          await getKnowledgeData(paging);
        }
      },
    });
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, knowledgeData.paging.pageSize, values);
  };

  const handleSelectRow = (record) => {
    setRowSelected(record);
    document.getElementsByClassName("knowledge-content")[0].scrollTop = 0;
  };

  async function onSave(values) {
    if (modalState.knowledge) {
      const dataResponse = await updateKnowledge({ ...values, _id: modalState.knowledge._id }, true);
      if (dataResponse) {
        toast.success("UPDATE_KNOWLEDGE_SUCCESS");
      }
    } else {
      const dataResponse = await createKnowledge(values, true);
      if (dataResponse) {
        toast.success("CREATE_KNOWLEDGE_SUCCESS");
      }
    }
    await getKnowledgeData();
    onToggleModal();
  }

  const onToggleModal = (knowledge) => {
    setModalState(pre => ({ visible: !pre.visible, knowledge: knowledge }));
  }

  const columns = [
    orderColumn(knowledgeData.paging),
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: 400,
      colSpan: 1,
      // render: (value) => <span className="line-clamp-1">{value}</span>,
    },
    {
      title: <div className="knowledge-content-title">
        Content
        {rowSelected && <div className={"knowledge-content-action"}>
          <AntButton
            type={BUTTON.LIGHT_NAVY}
            size="small"
            className={"btn-edit-knowledge"}
            icon={<EditOutlined />}
            onClick={() => onToggleModal(rowSelected)}
          >
            {t("EDIT")}
          </AntButton>
          <AntButton
            type={BUTTON.LIGHT_RED}
            size="small"
            className={"btn-delete-knowledge"}
            icon={<DeleteIcon />}
            onClick={() => handleDelete(rowSelected?._id)}
          >
            {t("DELETE")}
          </AntButton>
        </div>}
      </div>,
      key: "content",
      render: () => (
        <div
          className={`knowledge-content rows-height-${knowledgeData?.rows?.length}`}
          onClick={(e) => e.stopPropagation()}
        >
          {rowSelected.content}
        </div>),


      onCell: (_, index) => ({
        rowSpan: !index ? knowledgeData?.rows?.length : 0,
        colSpan: 10,
      }),
    },
  ];

  const pagination = useMemo(() => paginationConfig(knowledgeData.paging, knowledgeData.query, i18n.language), [navigate, knowledgeData.paging, knowledgeData.query, i18n.language]);

  const clearFilter = () => {
    formFilter.resetFields();
    onSubmitFilter({});
  };
  return (
    <div className="knowledges">
      <Row gutter={24}>
        <Col xs={24} lg={18}>
          <AntForm onFinish={onSubmitFilter} size={"large"} layout="vertical" form={formFilter} className="form-filter">

            <div className={"form-filter__content"}>
              <Col xs={16} style={{ padding: 0 }} >
                <AntForm.Item name="name">
                  <Input placeholder={"Search knowledge"} />
                </AntForm.Item>
              </Col>
              <Col xs={8} >
                <div className={"search-button"}>
                  <AntForm.Item>
                    <AntButton type={BUTTON.GHOST_WHITE} onClick={clearFilter}>
                      {t("CLEAR")}
                    </AntButton>
                  </AntForm.Item>
                  <AntForm.Item>
                    <AntButton type={BUTTON.DEEP_NAVY} htmlType="submit">
                      {t("SEARCH")}
                    </AntButton>
                  </AntForm.Item>
                </div>
              </Col>
            </div>

          </AntForm>
        </Col>
        <Col xs={24} lg={6} className="text-right">
          <AntButton
            type={BUTTON.DEEP_NAVY}
            size="large"
            icon={<PlusOutlined />}
            className={"btn-create-knowledges"}
            onClick={() => onToggleModal()}
          >
            {t("CREATE_KNOWLEDGE")}
          </AntButton>
        </Col>
      </Row>

      <Table
        columns={columns}
        bordered
        dataSource={knowledgeData.rows}
        pagination={{ ...pagination }}
        className={"knowledge-table"}
        onRow={(row) => ({
          onClick: () => handleSelectRow(row),
        })}
        rowClassName={(row) => (rowSelected?._id === row._id ? "row-selected" : "")}
      />

      <KnowledgeModal {...modalState} onCancel={onToggleModal} onFinish={onSave} />
    </div>
  );
};

export default Knowledge;
