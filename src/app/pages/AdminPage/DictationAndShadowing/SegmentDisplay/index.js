import React, { useEffect, useState } from "react";
import { Card, Col, Row, Select, Typography, Input } from "antd";
import { useTranslation } from "react-i18next";
import "./SegmentDisplay.scss";

const { TextArea } = Input;
const { Option } = Select;

const SegmentDisplay = (props) => {
  const { segment, index, onSegmentTextChange } = props;
  const { t } = useTranslation();
  const [editableText, setEditableText] = useState(segment.text);
  const handleTextChange = (e) => {
    setEditableText(e.target.value);
  };
  useEffect(() => {
    if (segment.text !== editableText) {
      setEditableText(segment.text);
    }
  }, [segment.text]);

  const handleTextBlur = () => {
    if (editableText !== segment.text && onSegmentTextChange) {
      onSegmentTextChange(segment._id, editableText, index);
    }
  };

  return (
    <Card key={segment._id} className="segment-display">
      <Row gutter={16}>
        <Col span={16}>
          <Typography.Text strong>{t("SEGMENT")} {index + 1}:</Typography.Text>
          <TextArea
            value={editableText}
            onChange={handleTextChange}
            onBlur={handleTextBlur}
            autoSize={{ minRows: 1, maxRows: 2 }}
          />
        </Col>
        <Col span={8}>
          <Typography.Text strong>{t("SELECT_WORD")}:</Typography.Text>
          <Select
            placeholder={t("SELECT_WORD")}
            style={{ width: "100%" }}
            value={segment.hiddenWord}
            allowClear
            onSelect={(word) => props.handleSelectWord(word, index)}
          >
            {segment.text
                    .replace(/,/g, " ")
                    .split(" ")
                    .filter(word => word.trim() !== "")
                    .map((word, wordIndex) => (
                      <Option key={`${segment._id}-${wordIndex}`} value={word}>{word}</Option>
                    ))}
          </Select>
        </Col>
      </Row>
    </Card>
  );
};

export default SegmentDisplay;