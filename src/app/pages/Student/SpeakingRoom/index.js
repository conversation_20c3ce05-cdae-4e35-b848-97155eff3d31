import React, { useEffect, useState } from 'react';
import './SpeakingRoom.scss';
import DefaultTopicImageUrl from '@src/asset/image/project-default.svg';
import HEADER_ICON from '@src/asset/icon/speaking-room-feedback.svg';
import ARROW_ICON from '@src/asset/icon/arrow/arrow-top-right.svg';
import START_ICON from '@src/asset/icon/start-speech.svg';
import RANDOM_IMG from '@src/asset/image/random.svg';
import SortArrowIconUrl from '@src/assets/icons/sort-arrow.svg';
import SearchIconUrl from '@src/assets/icons/search.svg'
import Subtract from '@src/asset/icon/Subtract.svg';
import { LINK } from '@src/constants/link';
import { useTranslation } from 'react-i18next';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { AntForm } from '@src/app/component/AntForm';
import { But<PERSON>, Card, Col, Empty, Form, Input, Radio, Row, Switch } from 'antd';
import { createSession, getAllWithoutPaginationSpeakingExercise } from '@src/app/services/SpeakingRoom';
import moment from 'moment';
import Loading from '@src/app/component/Loading';
import NoData from '@src/app/component/NoData';


const SpeakingRoom = () => {
  const { t } = useTranslation();
  const [formSpeech] = Form.useForm();
  const location = useLocation();
  const navigate = useNavigate();

  const [selected, setSelected] = useState(0); // lưu index cột được chọn
  const [selectedTopic, setSelectedTopic] = useState(0); // lưu index topic được chọn
  const [selectedTopicId, setSelectedTopicId] = useState(''); // lưu id topic được chọn
  const [sortOrder, setSortOrder] = useState('asc'); // trạng thái sắp xếp
  const [sortByTimeOrder, setSortByTimeOrder] = useState('asc'); // trạng thái sắp xếp

  const [loading, setLoading] = useState(false);

  const [titleTopic, setTitleTopic] = useState(''); // lưu tiêu đề topic

  const [sessionDetail, setSessionDetail] = useState({}); // lưu chi tiết phiên làm việc

  const [mode, setMode] = useState('practice');

  const [topicList, setTopicList] = useState([{
    title: 'Random Topic',
    topic: 'Random Topic'
    // avatar : RANDOM_IMG
  }]);

  const handleChange = e => {
    setMode(e.target.value);
    if (e.target.value === 'real_test') {
      setSelected(0);
    }
  };


  const columns = [`${t('FULL_TEST')}`, `${t('PART_1')}`, `${t('PART_2')}`, `${t('PART_3')}`];

  useEffect(() => {
    if (!topicList || topicList.length === 0) return;

    setSelectedTopicId(sessionDetail?._id);

    if (!selectedTopicId) return;

    let path;
    if (mode === "practice") {
      switch (selected) {
        case 0:
        case 1:
          path = LINK.SPEAKING_ROOM_PRACTICE_PART_ONE.format(selectedTopicId);
          break;
        case 2:
          path = LINK.SPEAKING_ROOM_PRACTICE_PART_TWO.format(selectedTopicId);
          break;
        default:
          path = LINK.SPEAKING_ROOM_PRACTICE_PART_THREE.format(selectedTopicId);
      }
    } else {
      path = LINK.SPEAKING_ROOM_TEST_PART_ONE.format(selectedTopicId);
    }
    navigate(path);
  }, [selectedTopicId, topicList, sessionDetail, mode]);

  useEffect(() => {
    if (mode === 'real_test') {
      setSelected(0); // Tự động chọn FULL TEST khi ở chế độ Real Test
    }
  }, [mode]);

  const toggleSort = () => {
    const newOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    setSortOrder(newOrder);

    const sortValue = newOrder === 'asc' ? 'topic' : '-topic';

    const params = new URLSearchParams(location.search);
    params.set('sort', sortValue); // cập nhật sort
    navigate(`?${params.toString()}`);
  };

  const toggleSortByTime = () => {
    const newOrderByTime = sortByTimeOrder === 'asc' ? 'desc' : 'asc';
    setSortByTimeOrder(newOrderByTime);

    const sortValue1 = newOrderByTime === 'asc' ? 'createdAt' : '-createdAt';

    const params = new URLSearchParams(location.search);
    params.set('sort', sortValue1); // cập nhật sort
    navigate(`?${params.toString()}`);
  };


  const searchParams = new URLSearchParams(location.search);
  const sortParam = searchParams.get('sort');
  const nameParam = searchParams.get('name');

  const timeAgo = (createdAt) => {
    const now = moment();
    const postTime = moment(createdAt);
    const duration = moment.duration(now.diff(postTime));

    const years = duration.years();
    const months = duration.months();
    const days = duration.days();
    const hours = duration.hours();
    const minutes = duration.minutes();

    if (years > 0) {
      return `${years} ${t("YEARS_AGO")}`;
    } else if (months > 0) {
      return `${months} ${t("MONTHS_AGO")}`;
    } else if (days > 0) {
      return `${days} ${t("DAYS_AGO")}`;
    } else if (hours > 0) {
      return `${hours} ${t("HOURS_AGO")}`;
    } else if (minutes > 1) {
      return `${minutes} ${t("MINUTES_AGO")}`;
    } else {
      return `${t("JUST_NOW")}`
    }
  };

  const handleSearch = (value) => {

    const params = new URLSearchParams(location.search);

    if (value) {
      params.set('name', value);
    } else {
      params.delete('name');
    }

    if (!params.get('sort')) {
      params.set('sort', 'topic');
    }

    navigate(`?${params.toString()}`);
  };

  const handlePracticeMode = async () => {
    setLoading(true);

    try {
      // Xác định part theo selected nếu là practice, ngược lại luôn là 'full_test'
      const part =
        mode === 'practice'
          ? selected === 0
            ? 'full_test'
            : selected === 1
              ? 'part1'
              : selected === 2
                ? 'part2'
                : 'part3'
          : 'full_test';

      // Tạo tag (có thể random)
      let tag = titleTopic;
      if (!titleTopic || titleTopic === 'Random Topic') {
        const randomIndex = Math.floor(Math.random() * topicList.length);
        tag = topicList[randomIndex]?.topic;
      }

      const data = { mode, part, tag };
      const result = await createSession(data);
      setSessionDetail(result);

    } catch (error) {
      console.error('Error creating session:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGetAllWithoutPaginationSpeakingExercise = async () => {
    setLoading(true);
    try {
      const query = {
        sort: sortParam || '',
        // title: nameParam || '',
        topic: nameParam || '',
      }
      const result = await getAllWithoutPaginationSpeakingExercise(query);
      if (Array.isArray(result?.rows)) {
        setTopicList(prev => [prev[0], ...result?.rows]); // nối thêm vào danh sách cũ
      } else {
        console.warn('Unexpected response:', result);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }

  const handleSelectedTopic = async (index, title) => {
    setSelectedTopic(index);
    setTitleTopic(title);
  }

  useEffect(() => {
    if (!sortParam) {
      navigate('?sort=topic', { replace: true }); // replace để không thêm vào lịch sử
    }
    if (sortParam === 'topic') {
      setSortOrder('asc');
    } else if (sortParam === '-topic') {
      setSortOrder('desc');
    }
    if (sortParam === 'createdAt') { //  Vẫn cần cập nhật giá trị cho sortOrder1 ở đây
      setSortByTimeOrder('asc');
    } else if (sortParam === '-createdAt') {
      setSortByTimeOrder('desc');
    }
    // Gọi hàm lấy danh sách topic
    if (sortParam || nameParam) {
      handleGetAllWithoutPaginationSpeakingExercise();
    }
  }, [sortParam, nameParam, navigate]);

  return (
    <div className="speaking-room">
      <div className="speaking-room-header">
        <img src={HEADER_ICON} alt="" />
        <span className="speaking-room__header__title">{t('SPEAKING_ROOM_HEADER')}</span>
        <div className='speaking-room__header__result'>
          <Link to={LINK.ALL_RESULTS}>
            <span className=''>{t('SPEAKING_ROOM_RESULT')}</span>
            <img src={ARROW_ICON} alt="" />
          </Link>
        </div>
      </div>
      <div className='speaking-room__content'>
        <div className='speaking-room__content__title'>
          <AntForm form={formSpeech} id="form-speech">
            <div className="speaking-room__mode-toggles">
              <Radio.Group
                onChange={handleChange}
                value={mode}
                optionType="default"
                className=""
              >
                <Radio value="practice" className="mode-toggle">
                  <span className="mode-toggle__label">{t('PRACTICE')}</span>
                </Radio>
                <Radio value="real_test" className="mode-toggle">
                  <span className="mode-toggle__label">{t('REAL_TEST')}</span>
                </Radio>
              </Radio.Group>
            </div>

            <Row gutter={[16, 16]}>
              {columns.map((text, index) => (
                <Col
                  key={index}
                  xs={24}
                  sm={12}
                  md={8}
                  lg={6}
                  onClick={() => {
                    if (mode === 'real_test' && index !== 0) {
                      // Không cho chọn các Part khác khi ở chế độ Real Test
                      return;
                    }
                    setSelected(index);
                  }}
                >
                  <div className={`box ${selected === index ? 'selected' : 'default'} ${mode === 'real_test' && index !== 0 ? 'disabled' : ''}`}>
                    {text}
                  </div>
                </Col>
              ))}
            </Row>

            <div className='speaking-room-topic-screen__search'>
              <Input
                className="total-topic-screen__search-input"
                placeholder={t("SEARCH_TOPIC")}
                suffix={<img src={SearchIconUrl} alt="Search" className="total-topic-screen__search-icon" />}
                onChange={(e) => handleSearch(e.target.value)}
                value={nameParam || ''}
              />
              <div className='total-topic-screen__sort-button-container'>
                <Button
                  className="total-topic-screen__sort-button"
                  onClick={toggleSortByTime}
                >
                  {sortByTimeOrder === 'asc' ? t('NEWEST_TO_OLDEST') : t('OLDEST_TO_NEWEST')}
                  <img
                    src={SortArrowIconUrl}
                    alt="Sort direction"
                    className={`total-topic-screen__sort-icon}`}
                  />
                </Button>
                <Button
                  className="total-topic-screen__sort-button"
                  onClick={toggleSort}
                >
                  {sortOrder === 'asc' ? 'A-Z' : 'Z-A'}
                  <img
                    src={SortArrowIconUrl}
                    alt="Sort direction"
                    className={`total-topic-screen__sort-icon}`}
                  />
                </Button>
              </div>

            </div>
            {loading || topicList.length === 1 ? (
              <div className="total-topic-screen__loading">
                <Loading active transparent />
              </div>
            ) : (
              <div className='speaking-room-topic-screen'>
                {topicList?.map((topic, index) => (
                  <div key={index} onClick={() => handleSelectedTopic(index, topic?.topic)}
                    className={`speaking-room-topic-screen__topic-card
                                                    ${selectedTopic === index ? 'topic-selected' : 'topic-default'}`
                    }

                  >
                    {topic?.topic === 'Random Topic' && topicList[0].topic === 'Random Topic' ? (
                      <img
                        className="speaking-room-topic-image"
                        src={RANDOM_IMG}
                        alt={topic?.title}
                      />
                    ) : (
                      <img
                        className="speaking-room-topic-image"
                        src={topic.avatarId ? `${window.location.origin}/api/files/content/${topic.avatarId}` : DefaultTopicImageUrl}
                        alt={topic?.title}
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = DefaultTopicImageUrl;
                        }}
                      />
                    )}
                    <div className='speaking-room-topic-screen__topic-content'>
                      <h3 className={`speaking-room-topic-title ${selectedTopic === index ? 'topic-selected-title' : 'topic-default-title'}`}>
                        {topic?.topic}
                      </h3>
                      {topic?.isCompleted === true && (
                        <img
                          src={Subtract}
                          alt='icon'
                          className=''
                        />
                      )}
                    </div>
                    {topic?.createdAt && (
                      <p className="speaking-room-topic-level">{t("UPLOADED")}: {timeAgo(topic?.createdAt)} </p>
                    )}
                  </div>
                ))}

              </div>
            )}


          </AntForm>

        </div>
        <div className='speaking-room-topic-screen__footer'>
          <Button className='speaking-room-topic-screen__footer__button'
            onClick={() => {
              handlePracticeMode();
            }}
          >
            {t('LETS_START')}
            <img src={START_ICON} alt="" />
          </Button>
        </div>
      </div>

    </div>

  );
}

export default SpeakingRoom;
