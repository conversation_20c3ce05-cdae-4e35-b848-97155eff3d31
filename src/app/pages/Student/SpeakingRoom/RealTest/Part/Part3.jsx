import { useEffect, useRef, useState } from 'react';
import './Part.scss';
import RECORD_ICON from "@src/asset/icon/record.svg";
import Warning_icon from "@src/asset/icon/warning.svg"
import STOP_ICON from "@src/asset/icon/stop-recording.svg";
import LoadingStart from '../../PartOne/ShowQuestion/LoadingStart/LoadingStart';
import { useTranslation } from 'react-i18next';
import CustomLiveAudioVisualizer from '../../PartOne/ShowQuestion/CustomLiveAudioVisualizer';
import { useDispatch, useSelector } from 'react-redux';
import { CONSTANT, LANGUAGE, RECORD_STATUS } from '@constant';
import { TRACKING_ACTIONS } from '@src/ducks/tracking.duck';
import { io } from "socket.io-client";
import { API } from '@api';
import ReactPlayer from 'react-player';
import { LINK } from '@link';
import { useNavigate, useParams } from 'react-router-dom';
import LoadingButton from '../../PartOne/LoadingButton';
import { submitCompleteSession } from '@src/app/services/SpeakingRoom';
import CustomLanguageSelector from '../../CustomLanguageSelector';

const Part3 = ({ detailsTopic, setIsPlaying }) => {
  const { t } = useTranslation();
  const { id } = useParams();

  const user = useSelector(state => state.auth.user);
  const studentTools = useSelector(state => state.tool.studentTools);

  const playerRef = useRef(null);
  const socket = useRef(null);
  const audioSourceRef = useRef(null);
  const scriptProcessorRef = useRef(null);
  const timeRef = useRef(null);

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState(60);
  const [isRecording, setIsRecording] = useState(false);
  const [stopTime, setStopTime] = useState(false);

  const [audioSource, setAudioSource] = useState(null);
  const [isPlaying, setPlaying] = useState(true); // mặc định là true để phát âm thanh đầu tiên

  const [recordStatus, setRecordStatus] = useState(RECORD_STATUS.STOPPED);

  const [timeRecorded, setTimeRecorded] = useState(0);
  const [hasMicAccess, setHasMicAccess] = useState(true);

  const [checkedContentSpeech, setCheckedContentSpeech] = useState('')
  const [stopLoading, setStopLoading] = useState(false)
  const [startLoading, setStartLoading] = useState(false)
  const [loading, setLoading] = useState(false);
  const [showResult, setShowResult] = useState(false)

  const [aiAudioFrameForVisualizer, setAiAudioFrameForVisualizer] = useState(null)
  const [currentOutputLanguage, setCurrentOutputLanguage] = useState(LANGUAGE.VI);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleChangeOutputLanguage = (langKey) => {
    setCurrentOutputLanguage(langKey);
  };


  const countWords = (text) => {
    if (!text || typeof text !== 'string') {
      return 0;
    }
    const trimmedText = text.trim();
    if (trimmedText === '') {
      return 0;
    }
    // Tách chuỗi bằng một hoặc nhiều khoảng trắng làm dấu phân cách
    const words = trimmedText.split(/\s+/);
    return words.length;
  };

  useEffect(() => {
    if (timeLeft <= 0 || !isRecording || stopTime) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer); // clear khi component unmount
  }, [isRecording, timeLeft, stopTime]);

  useEffect(() => {
    if (timeLeft <= 0 && isRecording) {
      // setIsRecording(false);
      setStopTime(true) // Tự động dừng ghi âm khi hết thời gian
      setStopLoading(true)
      handleStopRecordClick()
    }
    if (timeLeft < 50 && !isRecording && countWords(checkedContentSpeech) > 20) {
      setCurrentQuestionIndex(prevIndex => {
        if (prevIndex < detailsTopic?.length - 1) {
          // setIsPlaying(true)
          setPlaying(true)
          setTimeLeft(60); // Reset thời gian khi chuyển sang câu hỏi mới
          setCheckedContentSpeech('')
          return prevIndex + 1; // Tự động chuyển sang câu hỏi tiếp theo
        } else
          if (prevIndex === detailsTopic?.length - 1) {
            // setIsStart(false)
            setShowResult(true)
            return prevIndex; // Stay at the last question
          } else {
            return prevIndex; // Stay at the last question
          }
      });

    }
  }, [timeLeft, isRecording, detailsTopic?.length, checkedContentSpeech]);

  useEffect(() => {
    setAudioSource(API.STREAM_MEDIA.format(detailsTopic[currentQuestionIndex]?.audioId));
  }, [currentQuestionIndex, detailsTopic]);

  const handleViewResult = async () => {
    setLoading(true);
    const data = {
      language: currentOutputLanguage,
    }
    try {
      const result = await submitCompleteSession(id, data);
      // console.log(result);            
    } catch (error) {
      console.log(error)
    } finally {
      navigate(LINK.SPEAKING_RESULT.format(id))
      setLoading(false);
      setShowResult(false);
    }
  }

  const handleEnded = () => {
    setPlaying(false);
    setStopTime(false);
    setStartLoading(true);
    setCheckedContentSpeech('')
    setTimeLeft(60); // Reset thời gian khi bắt đầu ghi âm
    handleStartRecord()

  };

  // Hàm định dạng số: 30 -> "00:30"
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const secs = (seconds % 60).toString().padStart(2, "0");
    return `${mins}:${secs}`;
  };

  const handleToggleRecording = () => {
    if (isRecording) {
      setStopLoading(true);
      setStopTime(true);
      setCheckedContentSpeech('')
      handleStopRecordClick()
    } else {
      // setStopTime(false);
      // setStartLoading(true);
      // setCheckedContentSpeech('')
      // setTimeLeft(30); // Reset thời gian khi bắt đầu ghi âm
      // handleStartRecord()
    }
  }

  const handleRecordAgain = () => {
    setTimeLeft(60)
    setCheckedContentSpeech('')
    setPlaying(true)
    // setIsPlaying(true)
  }

  function handleDataResponse(socket) {
    socket.on("error", (err) => {
      socket.disconnect();

    });

    socket.on("finish-recognition", () => {
      setStopLoading(false)
      setIsRecording(false)
      socket.disconnect();

    });
    socket.on("message", (message) => {
      //if (message.state !== "recognizing")
      //console.log("Message from server:", message);

      switch (message.state) {

        case "recognized_text":
          console.log("recognized_text", message);
          setCheckedContentSpeech(message.recognizedText)

          break;

        default:
          break;
      }
    });
  }

  useEffect(() => {
    // Dọn dẹp khi component unmount
    return () => {
      if (socket.current) socket.current.disconnect();
    };
  }, []);

  useEffect(() => {
    if (timeRecorded >= CONSTANT.MAX_RECORD) {
      setRecordStatus(RECORD_STATUS.STOPPED);
    }
  }, [timeRecorded]);

  async function handleStartRecord() {
    setRecordStatus(RECORD_STATUS.PREPARE_RECORD);

    setTimeRecorded(0);
    dispatch(actions.trackCustomClick(TRACKING_ACTIONS.INPUT_ESSAY));
  }

  function handleStopRecord() {
    clearInterval(timeRef.current);
    setRecordStatus(RECORD_STATUS.STOPPED);
    setAiAudioFrameForVisualizer(null)

    console.log("stopRecording");
    if (scriptProcessorRef.current) {
      scriptProcessorRef.current.onaudioprocess = null; // Hủy bỏ callback để ngừng xử lý âm thanh
      scriptProcessorRef.current.disconnect(); // Ngừng kết nối scriptProcessor
    }

    if (audioSourceRef.current) {
      console.log("stopRecording audioSteam");
      audioSourceRef.current.disconnect();
      audioSourceRef.current.mediaStream?.getTracks()?.forEach(track => track.stop());
    }

    socket.current?.emit("close-recording");
  }

  const handleStopRecordClick = () => {
    if (recordStatus === RECORD_STATUS.RECORDING) {
      setRecordStatus(RECORD_STATUS.STOPPED);
    }
  }

  useEffect(() => {
    console.log("recordStatus", recordStatus);

    switch (recordStatus) {
      case RECORD_STATUS.PREPARE_RECORD:
        handlePrepareRecord();
        break;
      case RECORD_STATUS.RECORDING:
        handleDataResponse(socket.current);
        handleRecordAudio();
        break;
      case RECORD_STATUS.STOPPED:
        handleStopRecord();
        break;
      default:
        break;
    }

  }, [recordStatus]);

  async function handlePrepareRecord() {
    socket.current = io("/speaking-advanced", { transports: ["websocket"], path: "/socket" });

    socket.current.on("connect", () => {
      console.log("Connected to WebSocket server");
      // Không gửi dữ liệu ở đây, chỉ log kết nối thành công
    });

    socket.current.on("server_ready", () => {
      setRecordStatus(RECORD_STATUS.RECORDING);
    });

    socket.current.on("error", (err) => {
      console.log("socket error", err);
      socket.current.disconnect();
      //   setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
    });
  }


  async function handleRecordAudio() {
    // const topic = speakingForm?.getFieldValue("topic");
    const instructionId = studentTools.speaking.instructionIds?.[0]?._id;

    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      .then(stream => {
        console.log("Đã truy cập micrô:", stream);
        setIsRecording(true)
        // setIsPlaying(false)
        setStartLoading(false)

        return stream; // Trả về stream
      })
      .catch(error => {
        console.error("Lỗi truy cập micrô:", error);
        return null; // Trả về null nếu có lỗi
      });

    if (!stream) {
      setRecordStatus(RECORD_STATUS.STOPPED);
      //   setRecognizeStatus(RECOGNIZE_STATUS.NOT_STARTED);
      setHasMicAccess(false);
      return toast.error(t("MICROPHONE_NOT_ACCESSIBLE"), { replace: true });
    }

    // 2. Tạo AudioContext
    const audioContext = new AudioContext({ sampleRate: 16000 });

    // 3. Kết nối stream với AudioContext
    const source = audioContext.createMediaStreamSource(stream);
    audioSourceRef.current = source;
    // 4. Tạo ScriptProcessorNode
    const bufferSize = 4096; // Kích thước khối xử lý
    const scriptProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);
    scriptProcessorRef.current = scriptProcessor;
    // 5. Xử lý dữ liệu PCM trong callback
    scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
      const inputBuffer = audioProcessingEvent.inputBuffer;
      const channelData = inputBuffer.getChannelData(0); // Lấy dữ liệu kênh đầu tiên (mono)
      const channelDataCopy = new Float32Array(channelData);
      setAiAudioFrameForVisualizer(channelDataCopy);

      // Chuyển đổi Float32 (-1.0 -> 1.0) sang Int16 (-32768 -> 32767)
      const pcmInt16Array = new Int16Array(channelData.length);
      for (let i = 0; i < channelData.length; i++) {
        pcmInt16Array[i] = Math.max(-1, Math.min(1, channelData[i])) * 0x7FFF;
      }

      // Tạo Uint8Array từ Int16Array để gửi qua socket
      const pcmUint8Array = new Uint8Array(pcmInt16Array.buffer);

      // Gửi dữ liệu qua socket
      const inputData = {
        part: detailsTopic[currentQuestionIndex]?.part,
        questionId: detailsTopic[currentQuestionIndex]?._id,
        sessionId: detailsTopic[currentQuestionIndex]?.sessionId,
        questionText: detailsTopic[currentQuestionIndex]?.questionText,
        questionIndex: detailsTopic[currentQuestionIndex]?.questionIndex,
      };

      socket.current?.emit("audio", {
        buffer: pcmUint8Array,
        inputData,
        userId: user._id,
      });
    };

    // 6. Kết nối các nodes
    source.connect(scriptProcessor);
    scriptProcessor.connect(audioContext.destination);

    timeRef.current = setInterval(() => {
      setTimeRecorded(prevState => prevState + 1);
    }, 1000);
  }

  return (
    <div className="Part__container">
      <div className='Part__container__time'>
        {formatTime(timeLeft)}
      </div>
      <div className="Part__container__body">
        {!stopLoading && (checkedContentSpeech.length === 0 || isRecording) && (
          <>
            {startLoading ? (
              <LoadingStart isLoading={startLoading}>
              </LoadingStart>
            ) : (
              <img
                src={isRecording ? STOP_ICON : RECORD_ICON}
                alt="icon"
                className={`${isRecording ? 'stop-recording' : ''}`}
                onClick={handleToggleRecording}
              />
            )}
            <p className='question__content__footer__text'>
              {(!isRecording && timeLeft === 60) ? t('STRAT_RECORDING') : t('RECORDING')}
            </p>
          </>
        )}

        {stopLoading ? (
          <p className='question__content__footer__text'>
            {t('AUDIO_RECORDED_LOADING_NEXT_QUESTION')}
          </p>
        ) : (
          ""
        )}

        {isRecording && aiAudioFrameForVisualizer && (
          <CustomLiveAudioVisualizer
            audioData={aiAudioFrameForVisualizer}
          />
        )}

        {audioSource && (
          <ReactPlayer
            ref={playerRef}
            url={audioSource}
            playing={isPlaying}
            controls={false}
            width="0"
            height="0"
            muted={false}
            onEnded={handleEnded}
          />
        )}
        {showResult && (
          <div className="Part__container__body__language">
            <CustomLanguageSelector
              disabled={loading}
              value={currentOutputLanguage}
              onChange={handleChangeOutputLanguage}
            />
          </div>
        )}
        {showResult && (
          <>
            {loading ? (
              <LoadingButton isLoading={loading}>
                <div className='Part__container__body__next'>{t("SUBMIT")}</div>
              </LoadingButton>
            ) : (
              <div className='Part__container__body__border'
                onClick={handleViewResult}
              >
                <div className='Part__container__body__next'>{t("SUBMIT")}</div>
              </div>
            )}
          </>
        )}
      </div>
      {(((timeLeft >= 50 && timeLeft < 60) || (countWords(checkedContentSpeech) <= 20 && checkedContentSpeech.length > 0)) && !isRecording) && (
        <div className="Part__container__warning">
          <img
            src={Warning_icon}
            alt=""
            className=""
          />
          <div className="Part__container__warning__box">
            <div className="Part__container__warning__box__1">
              {t('YOUR_SPEECH_IS_NOT_LONG_ENOUGH_TO_SUBMIT')}
            </div>
            <div className="Part__container__warning__box__1">
              {t('PLEASE_RECORD_AGAIN')} <span className='Part__container__warning__box__record' onClick={handleRecordAgain}>{t('RECORD')}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Part3;