import React from "react";
import { useTranslation } from "react-i18next";

import HtmlContent from "@component/HtmlContent";
import TypingEffect from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TypingEffect";


function MarkTestWritingOutputValue({ responseSelected }) {
  const { t } = useTranslation();
  
  const { output } = responseSelected || {};
  const { evaluation, score } = output || {};
  
  const textHtml = (
    <div className="mark-test-writing-value">
      <HtmlContent dangerouslySetInnerHTML={{ __html: evaluation }}/>
      <span className="mark-test-writing-value__score">{t("SCORE")}: {score}</span>
    </div>
  );
  
  if (responseSelected.typingEffect) return <TypingEffect html={textHtml}/>;
  return textHtml;
}

export default MarkTestWritingOutputValue;