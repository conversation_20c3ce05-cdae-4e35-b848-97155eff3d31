.pricing-container {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: center;

  padding: 32px 0;
  gap: 24px;
  border-radius: 24px;
  background: radial-gradient(18.71% 33.59% at 50% 8.03%, #F4F3FF 0.01%, #FFFFFF 100%);
  box-shadow: 0px 4px 20px 0px #0000001A;

  .pricing-header {
    display: flex;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 8px;

    .pricing-header__title {
      font-size: 36px;
      line-height: 44px;
      letter-spacing: -0.25px;
      text-align: center;

    }

    .pricing-header__terms {
      color: var(--typo-colours-support-blue);
    }

    .pricing-header__info {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 16px;
      line-height: 24px;
      text-align: center;

      .pricing-header__info__text {
        display: flex;
        gap: 4px;
      }
    }
  }

  .pay-per-submission {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 657px;

    .pay-per-submission__title {
      font-size: 36px;
      line-height: 44px;
      letter-spacing: -0.25px;
      text-align: center;
    }

    .pay-per-submission__text {
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0px;
      text-align: center;
    }
  }

  .pricing-type {
    display: flex;
    gap: 8px;

    button {
      border-radius: 12px;
      padding: 8px 24px;

      &:not(.ant-btn-deep-navy) {

        &:not(:hover),
        &:active {
          background: unset !important;
          border-color: transparent !important;
        }
        color: unset !important;
        font-weight: 400;
      }
    }
  }

  .pricing-checked-icon {
    width: 24px;
    height: 24px;
    background: #3A18CE;
    flex-shrink: 0;

    .ant-avatar-string {
      display: flex;
      align-items: center;
    }

    svg {
      width: 9px;
      height: 8.25px;
      border: 2px;

      path {
        stroke: #FFFFFF;
        stroke-width: 3;
      }
    }
  }
}