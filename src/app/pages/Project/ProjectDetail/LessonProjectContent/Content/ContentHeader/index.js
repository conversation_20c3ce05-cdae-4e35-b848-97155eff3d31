import React from "react";
import InfoCircle from "@component/SvgIcons/InfoCircle";
import { useProject } from "@app/pages/Project";

import "./ContentHeader.scss";

function ContentHeader({
                         toolInfo,
                         isMinimize,
                         contentIndex = undefined,
                         actions = null,
                         showDesc = true,
                       }) {
  const { setToolDemoState } = useProject();
  const handleOpenToolDemo = () => {
    setToolDemoState(prevState => ({ ...prevState, open: true, link: toolInfo.linkYoutube }));
  };
  
  return <div className="project-content__header">
    <div className="project-content__title">
      <div className="project-content__title-text">
        {toolInfo?.linkYoutube && !isMinimize && <div
          className={"project-content__title-video-tutorial"}
          onClick={handleOpenToolDemo}
        >
          <InfoCircle />
        </div>}
        {contentIndex ? `${contentIndex}. ` : ""}
        {toolInfo?.name}
      </div>
      {!!actions && <div className="project-content__title-action">
        {actions}
      </div>}
    </div>
    {showDesc && <div className="project-tool-item__description">
      {toolInfo?.description}
    </div>}
  </div>;
}

export default ContentHeader;