.error-report-detail-modal {
  .ant-modal-content {
    border-radius: 8px;
    box-shadow: var(--shadow-level-2);
  }

  .ant-modal-header {
    border-radius: 8px 8px 0 0;
    background: var(--primary-colours-blue-navy);

    .ant-modal-title {
      color: var(--typo-colours-support-white);
      font-weight: 600;
    }
  }

  .ant-modal-close {
    .ant-modal-close-x {
      color: var(--typo-colours-support-white);

      &:hover {
        color: var(--typo-colours-support-white);
        opacity: 0.8;
      }
    }
  }
}

.error-report-detail {
  .basic-info-card {
    margin-bottom: 16px;
    border: 1px solid var(--lighttheme-content-background-stroke);
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);

    .ant-descriptions-item-label {
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      width: 120px;
    }

    .ant-descriptions-item-content {
      color: var(--typo-colours-support-blue-light);
    }
  }

  .description-card {
    margin-bottom: 16px;
    border: 1px solid var(--lighttheme-content-background-stroke);
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);

    .ant-card-head {
      background-color: var(--background-light-background-1);
      border-bottom: 1px solid var(--lighttheme-content-background-stroke);
    }

    .ant-typography {
      margin-bottom: 0;
      line-height: 1.6;
      color: var(--typo-colours-primary-black);
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .media-card {
    margin-bottom: 16px;
    border: 1px solid var(--lighttheme-content-background-stroke);
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);

    .ant-card-head {
      background-color: var(--background-light-background-1);
      border-bottom: 1px solid var(--lighttheme-content-background-stroke);
    }

    .media-content {
      .images-section {
        margin-bottom: 24px;

        .ant-typography-title {
          margin-bottom: 12px;
          color: var(--typo-colours-primary-black);
        }

        .images-gallery {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;

          .error-image {
            border: 2px solid var(--background-light-background-1);
            transition: all var(--transition-timing);
            cursor: pointer;

            &:hover {
              border-color: var(--primary-colours-blue);
              transform: scale(1.05);
            }
          }
        }
      }

      .video-section {
        .ant-typography-title {
          margin-bottom: 12px;
          color: var(--typo-colours-primary-black);
        }

        .video-player {
          .error-video {
            border-radius: 8px;
            border: 1px solid var(--lighttheme-content-background-stroke);
          }
        }
      }

      .no-media {
        text-align: center;
        padding: 40px 0;
        color: var(--typo-colours-support-blue-light);
        font-style: italic;
      }
    }
  }

  .admin-actions-card {
    border: 1px solid var(--lighttheme-content-background-stroke);
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);

    .ant-card-head {
      background-color: var(--primary-colours-blue-light-1);
      border-bottom: 1px solid var(--lighttheme-content-background-stroke);
    }

    .action-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;

      .ant-switch {
        &.ant-switch-checked {
          background-color: var(--support-colours-green);
        }
      }
    }

    .admin-notes-section {
      border-top: 1px solid var(--background-light-background-1);
      padding-top: 16px;

      .ant-typography {
        margin-bottom: 8px;
        color: var(--typo-colours-primary-black);
      }

      .ant-input {
        border-radius: 6px;

        &:focus {
          border-color: var(--primary-colours-blue);
          box-shadow: 0 0 0 2px var(--primary-colours-blue-light-2);
        }
      }
    }
  }
}

// Image preview customization
.ant-image-preview-wrap {
  .ant-image-preview-img {
    max-height: 80vh;
    object-fit: contain;
  }
}

// Responsive design
@media (max-width: 768px) {
  .error-report-detail-modal {
    .ant-modal {
      width: 95% !important;
      margin: 10px;
    }

    .ant-modal-content {
      .ant-modal-body {
        padding: 16px;
      }
    }
  }

  .error-report-detail {
    .basic-info-card {
      .ant-descriptions {
        .ant-descriptions-item {
          padding-bottom: 8px;
        }
      }
    }

    .media-content {
      .images-gallery {
        justify-content: center;

        .error-image {
          width: 100px !important;
          height: 100px !important;
        }
      }

      .video-player {
        .error-video {
          height: 200px;
        }
      }
    }

    .admin-actions-card {
      .action-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }
  }
}

// Animation for loading states
.ant-spin-spinning {
  .error-report-detail {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Custom scrollbar for modal content
.error-report-detail-modal {
  .ant-modal-body {
    max-height: 70vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}
