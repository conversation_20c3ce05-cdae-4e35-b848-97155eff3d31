import React, { useState, useEffect, useRef } from 'react';
import { Input, Select, Card, Button, Spin, Empty } from 'antd';
import SortArrowIconUrl from '@src/assets/icons/sort-arrow.svg';
import SearchIconUrl from '@src/assets/icons/search.svg';
import DefaultTopicImageUrl from '@src/asset/image/project-default.svg';
import { useNavigate, useLocation } from 'react-router-dom';
import { LINK } from '@src/constants/link';
import { useTranslation } from "react-i18next";
import { getAllPublishedExercises } from '@src/app/services/DictationShadowing';
import './TotalTopicScreen.scss';
import Loading from '@src/app/component/Loading';

const { Option } = Select;
const PAGE_SIZE = 240;
const START_PAGE = 1;

export const TotalTopicScreen = () => {
  const { t } = useTranslation();
  const [topics, setTopics] = useState([]);
  const [allTopics, setAllTopics] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLevel, setSelectedLevel] = useState(null);
  const [sortOrder, setSortOrder] = useState('asc');
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(START_PAGE);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const navigate = useNavigate();
  const location = useLocation();
  const searchTimeout = useRef(null);

  const buildQuery = (overrides = {}) => {
    const query = {};

    // Use override values if provided, otherwise use state values
    const currentSearchQuery = overrides.searchQuery !== undefined ? overrides.searchQuery : searchQuery;
    const currentLevel = overrides.level !== undefined ? overrides.level : selectedLevel;
    const currentSortOrder = overrides.sortOrder !== undefined ? overrides.sortOrder : sortOrder;

    if (currentSearchQuery.trim()) {
      query.name = currentSearchQuery;
    } else {
      delete query.name;
    }

    if (currentLevel) {
      query.difficulty = currentLevel;
    } else {
      delete query.difficulty;
    }

    if (currentSortOrder === 'desc') {
      query.sort = '-name';
    } else {
      query.sort = 'name';
    }

    return query;
  };

  // Build pagination object
  const buildPaging = (overrides = {}) => {
    const currentPage = overrides.currentPage !== undefined ? overrides.currentPage : page;

    return {
      page: currentPage,
      limit: PAGE_SIZE
    };
  };

  // Update URL with current filters and pagination
  const updateUrlParams = (params, paging) => {
    const searchParams = new URLSearchParams();

    if (params.name) searchParams.set('name', params.name);
    if (params.difficulty) searchParams.set('level', params.difficulty);
    if (params.sort) searchParams.set('sort', params.sort);

    const newUrl = `${location.pathname}${searchParams.toString() ? '?' + searchParams.toString() : ''}`;
    navigate(newUrl, { replace: true });
  };

  const fetchTopics = async (query = {}, loadMore = false, pagingOverrides = {}) => {
    setLoading(true);
    try {
      // Build pagination object
      const paging = buildPaging(pagingOverrides);

      // Update URL with the current query params and pagination
      updateUrlParams(query, paging);

      const response = await getAllPublishedExercises(query, paging);
      if (response && response.rows) {
        const formattedTopics = response.rows.map(topic => ({
          id: topic._id,
          title: topic.name,
          level: topic.difficulty,
          exercises: topic.numberExercise || 0,
          image: topic.avatarId ? `${window.location.origin}/api/files/content/${topic.avatarId}` : DefaultTopicImageUrl,
          type: topic.type
        }));

        // Update total count and determine if there are more items to load
        if (response.total !== undefined) {
          setTotalCount(response.total);

          // Check if current page is less than total pages
          // If totalPages is available, use it directly
          if (response.totalPages !== undefined) {
            setHasMore(response.page < response.totalPages);
          }
          // Otherwise calculate based on total items and page size
          else {
            const calculatedTotalPages = Math.ceil(response.total / (response.pageSize || PAGE_SIZE));
            setHasMore(response.page < calculatedTotalPages);
          }
        }

        if (loadMore) {
          setTopics(prevTopics => [...prevTopics, ...formattedTopics]);
        } else {
          setTopics(formattedTopics);
        }

        if (!query.name) {
          setAllTopics(prev => loadMore ? [...prev, ...formattedTopics] : formattedTopics);
        }
      }
    } catch (error) {
      console.error("Error fetching topics:", error);
    } finally {
      setLoading(false);
    }
  };

  // Parse query parameters from URL on initial load
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const nameParam = searchParams.get('name');
    const levelParam = searchParams.get('level');
    const sortParam = searchParams.get('sort');

    if (nameParam) setSearchQuery(nameParam);
    if (levelParam) setSelectedLevel(levelParam);
    if (sortParam) setSortOrder(sortParam === '-name' ? 'desc' : 'asc');

    // Always start with START_PAGE when loading from URL
    setPage(START_PAGE);

    // Only reset topics on initial load or when filter parameters change
    const newSearch = nameParam || '';
    const newLevel = levelParam || null;
    const newSort = sortParam === '-name' ? 'desc' : 'asc';

    // Always reset data when filters change or on initial load
    setTopics([]);

    // Initial fetch using URL parameters
    fetchTopics(
      buildQuery({
        searchQuery: nameParam || '',
        level: levelParam || null,
        sortOrder: sortParam === '-name' ? 'desc' : 'asc',
      }),
      false, // Never load more on initial load
      { currentPage: START_PAGE }
    );
  }, [location.search]);

  const handleSearch = (value) => {
    setSearchQuery(value);
    // Reset page to 0 when search changes
    setPage(START_PAGE);
    // Reset topics list when search changes
    setTopics([]);

    // Clear any existing timeout
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    // Set a new timeout to fetch data after delay
    searchTimeout.current = setTimeout(() => {
      fetchTopics(
        buildQuery({ searchQuery: value }),
        false,
        { currentPage: START_PAGE }
      );
    }, 200);
  };

  // Clear timeout on component unmount
  useEffect(() => {
    return () => {
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }
    };
  }, []);

  const handleLevelChange = (value) => {
    setSelectedLevel(value);
    setPage(START_PAGE); // Reset page to 0 when filter changes
    setTopics([]); // Reset topics when filter changes
    fetchTopics(
      buildQuery({ level: value || null }),
      false,
      { currentPage: START_PAGE }
    );
  };

  const toggleSort = () => {
    const newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    setSortOrder(newSortOrder);
    setPage(START_PAGE); // Reset page to 0 when sort changes
    setTopics([]); // Reset topics when sort changes
    fetchTopics(
      buildQuery({ sortOrder: newSortOrder }),
      false,
      { currentPage: START_PAGE }
    );
  };

  const handleLoadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    fetchTopics(
      buildQuery(),
      true,
      { currentPage: nextPage }
    );
  };

  const handlePracticeDictation = (topicId) => {
    navigate(LINK.DICTATION.format(topicId));
  };

  const handlePracticeShadowing = (topicId) => {
    navigate(LINK.SHADOWING.format(topicId));
  };

  return (
    <div className="total-topic-screen">
      <div className="total-topic-screen__container">
        <div className="total-topic-screen__header">
          <div className="total-topic-screen__search-container">
            <Input
              className="total-topic-screen__search-input"
              placeholder={t("SEARCH_TOPIC")}
              suffix={<img src={SearchIconUrl} alt="Search" className="total-topic-screen__search-icon" />}
              onChange={(e) => handleSearch(e.target.value)}
              value={searchQuery}
            />
            <Select
              className="total-topic-screen__level-select"
              placeholder={t("LEVEL")}
              onChange={handleLevelChange}
              allowClear
              value={selectedLevel}
            >
              <Option value="A1">Level A1</Option>
              <Option value="A2">Level A2</Option>
              <Option value="B1">Level B1</Option>
              <Option value="B2">Level B2</Option>
              <Option value="C1">Level C1</Option>
              <Option value="C2">Level C2</Option>
            </Select>
          </div>
          <Button
            className="total-topic-screen__sort-button"
            onClick={toggleSort}
          >
            {sortOrder === 'asc' ? 'A-Z' : 'Z-A'}
            <img
              src={SortArrowIconUrl}
              alt="Sort direction"
              className={`total-topic-screen__sort-icon}`}
            />
          </Button>
        </div>

        {loading && page === 0 ? (
          <div className="total-topic-screen__loading">
            <Loading active transparent />
          </div>
        ) : topics.length === 0 ? (
          <Empty
            description={t("NO_TOPICS_FOUND")}
            className="total-topic-screen__empty"
          />
        ) : (
          <>
            <div className="total-topic-screen__topics-grid">
              {topics.map((topic) => (
                <Card key={topic.id} className="total-topic-screen__topic-card">
                  <img
                    className="topic-image"
                    src={topic.image}
                    alt={topic.title}
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = DefaultTopicImageUrl;
                    }}
                  />
                  <h3 className="topic-title">{topic.title}</h3>
                  <p className="topic-level">Level {topic.level} - {topic.exercises} {t("EXERCISES")}</p>
                  <Button
                    className="total-topic-screen__action-button total-topic-screen__action-button--dictation"
                    onClick={() => handlePracticeDictation(topic.id)}
                    disabled={topic.type !== 'dictation' && topic.type !== 'dictation_shadowing'}
                  >
                    {t("PRACTICE_DICTATION")}
                  </Button>
                  <Button
                    className="total-topic-screen__action-button total-topic-screen__action-button--shadowing"
                    onClick={() => handlePracticeShadowing(topic.id)}
                    disabled={topic.type !== 'shadowing' && topic.type !== 'dictation_shadowing'}
                  >
                    {t("PRACTICE_SHADOWING")}
                  </Button>
                </Card>
              ))}
            </div>

            {hasMore && (
              <div className="total-topic-screen__load-more">
                <Button
                  onClick={handleLoadMore}
                  loading={loading}
                  className="total-topic-screen__load-more-button"
                >
                  {t("LOAD_MORE")}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
