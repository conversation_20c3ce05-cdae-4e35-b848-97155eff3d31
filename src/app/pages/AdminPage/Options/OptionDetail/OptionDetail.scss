.option-detail {
  display: flex;
  font-family: Segoe UI;
  flex-direction: column;
  gap: 24px;
  border-radius: 8px;
  background-color: var(--background-light-background-2);
  padding: 24px;

  .option-detail__title {
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
  }

  .select-options-item {
    display: flex;
    gap: 24px;
    padding-bottom: 8px;
    align-items: center;
    font-weight: 600;
    align-items: flex-start;

    .add-option-button {
      padding: 5px 10px;
    }
  }

  .ant-row {
    width: 100%;
  }

  .select-options-item__btnRemove {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    padding-right: 10px;
  }

  .btn-cancel-add-options {
    box-shadow: var(--shadow-level-2);
  }

  .add-options-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding-bottom: 20px;

    button {
      box-shadow: var(--shadow-level-2);
      color: var(--primary-colours-blue) !important;
    }
  }

  .ant-form-item-label {
    label {
      height: unset !important;
    }
  }

  .ant-form-item-required {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
    padding: 0px;
    flex-direction: row-reverse;

    &:after {
      display: none;
    }
  }

  .instruction-localization-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .instruction-localization-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;


  }

  .items-baseline {
    width: 100%;
  }

  .instruction-item-action {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 5px;
  }

  .first-actions {
    align-items: center;
    margin-top: 33px;
  }

  .btn-cancel-add-options-detail {
    box-shadow: var(--shadow-level-2);
  }

  .ant-form-item.localization {
    width: 100%;
    margin-bottom: 0;
  }

  .ant-form-item.save-button {
    margin-bottom: 0;
  }

  .add-options-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    button {
      box-shadow: var(--shadow-level-2);
      color: var(--primary-colours-blue) !important;
    }
  }

  .save-button {
    text-align: center;
    padding-top: 10px;
  }
}

.modal-createConversation {
  .btn-cancel-add-conversation {
    box-shadow: var(--shadow-level-2);
  }

  .btn-add-conversation {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding-bottom: 20px;

    button {
      box-shadow: var(--shadow-level-2);
      color: var(--primary-colours-blue) !important;
    }
  }
}