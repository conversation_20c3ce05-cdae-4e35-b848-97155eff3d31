import React from "react";

import MarkTestWritingOutputForm from "./MarkTestWritingOutputForm";
import MarkTestWritingOutputValue from "./MarkTestWritingOutputValue";

import "./MarkTestWritingOutput.scss";

function MarkTestWritingOutput({ ...props }) {
  
  if (props.isEditing) return <MarkTestWritingOutputForm {...props} />;
  return <MarkTestWritingOutputValue {...props} />;
  
}

export default MarkTestWritingOutput;