$resourceColor: (
        video: var(--red),
        audio: var(--green-dark),
        image: var(--blue),
        document: var(--yellow),
        empty: var(--support-colours-grey-light),
);

.resource-capacity {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .resource-capacity__chart {
    height: 14px;
    border-radius: 4px;
    overflow: hidden;

    * {
      display: flex;
    }

    .g2-tooltip {
      display: none;
    }

  }

  .resource-capacity__legend {
    display: flex;
    flex-direction: row;
    column-gap: 24px;
    row-gap: 8px;
    flex-wrap: wrap;

    .resource-capacity__legend-item {
      display: flex;
      gap: 8px;

      @each $resource-type, $color in $resourceColor {
        &.resource-capacity__legend-#{$resource-type} .resource-capacity__legend-item__dot:before {
          background-color: $color;
        }
      }

      .resource-capacity__legend-item__dot {
        display: flex;
        align-items: center;

        &:before {
          display: block;
          content: '';
          height: 12px;
          width: 12px;
          border-radius: 50%;
        }
      }

      .resource-capacity__legend-item__text {

      }
    }
  }
}