import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { useSpeaking } from "@app/pages/Student/Speaking";
import StudentScoreChart from "@component/StudentScoreChart";

import { CONSTANT } from "@constant";

import "./SpeakingChart.scss";

function SpeakingChart() {
  const { t } = useTranslation();
  const { recordResult } = useSpeaking();
  
  
  const { pronScore, contentScore } = useMemo(() => {
    const scoreTotals = {
      pronunciationAssessment: {},
      contentAssessment: {},
    };
    
    const pronScore = {};
    const contentScore = {};
    
    if (!Array.isArray(recordResult)) return;
    
    recordResult.forEach(item => {
      item.nBest.forEach(nBestItem => {
        
        
        Object.entries(nBestItem).forEach(([assessmentType, assessment]) => {
          if (scoreTotals[assessmentType]) {
            Object.entries(assessment).forEach(([key, value]) => {
              if (key.toLowerCase().includes("score")) {
                if (!scoreTotals[assessmentType][key]) {
                  scoreTotals[assessmentType][key] = { total: 0, count: 0 };
                }
                scoreTotals[assessmentType][key].total += value;
                scoreTotals[assessmentType][key].count += 1;
              }
            });
          }
        });
      });
    });
    
    const averages = {};
    Object.entries(scoreTotals).forEach(([assessmentType, scores]) => {
      averages[assessmentType] = {};
      Object.entries(scores).forEach(([scoreType, { total, count }]) => {
        averages[assessmentType][scoreType] = Math.round(total / count);
      });
    });
    
    pronScore.accuracyScore = averages?.pronunciationAssessment?.accuracyScore;
    pronScore.fluencyScore = averages?.pronunciationAssessment?.fluencyScore;
    pronScore.prosodyScore = averages?.pronunciationAssessment?.prosodyScore;
    
    
    contentScore.grammarScore = averages?.contentAssessment?.grammarScore;
    contentScore.topicScore = averages?.contentAssessment?.topicScore;
    contentScore.vocabularyScore = averages?.contentAssessment?.vocabularyScore;
    
    
    return { pronScore, contentScore };
  }, [recordResult]);
  
  return <div className="speaking-chart">
    <StudentScoreChart
      chartType={CONSTANT.AZURE}
      label={t("PRONUNCIATION_SCORE")}
      scoreBreakdown={[
        { lang: "ACCURACY_SCORE", value: pronScore.accuracyScore },
        { lang: "FLUENCY_SCORE", value: pronScore.fluencyScore },
        { lang: "PROSODY_SCORE", value: pronScore.prosodyScore },
      ]}
    />
    <StudentScoreChart
      chartType={CONSTANT.AZURE}
      label={t("CONTENT_SCORE")}
      scoreBreakdown={[
        { lang: "GRAMMAR_SCORE", value: contentScore.grammarScore },
        { lang: "VOCABULARY_SCORE", value: contentScore.vocabularyScore },
        { lang: "TOPIC_SCORE", value: contentScore.topicScore },
      ]}
    />
  </div>;
}

export default SpeakingChart;