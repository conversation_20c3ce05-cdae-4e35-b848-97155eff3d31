import React from "react";

import OpenQuestionValue
  from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/OpenQuestionOutput/OpenQuestionValue";
import OpenQuestionForm
  from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/OpenQuestionOutput/OpenQuestionForm";

import "./OpenQuestionOutput.scss";

function OpenQuestionOutput({ ...props }) {
  
  if (props.isEditing) return <OpenQuestionForm  {...props} />;
  return <OpenQuestionValue  {...props} />;
}

export default OpenQuestionOutput;