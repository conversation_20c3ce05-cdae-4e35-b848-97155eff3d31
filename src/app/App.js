import React, { Suspense, useEffect } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import { connect } from "react-redux";
import { withTranslation, useTranslation } from "react-i18next";
import dayjs from "dayjs";

import AuthInit from "./auth/components/AuthInit";

import Theme from "@app/theme";
import { SetupNavigation } from "@navigation";

import { ConfirmProvider, confirmRef } from "@app/component/ConfirmProvider";
import { ToastProvider, toastRef } from "@app/component/ToastProvider";

import * as app from "@src/ducks/app.duck";
import * as auth from "@src/ducks/auth.duck";
import * as tracking from "@src/ducks/tracking.duck";

import("dayjs/locale/en");
import("dayjs/locale/vi");

import RenewPackagePopup from './component/RenewPackagePopup';
import useRenewPackagePopup from './hooks/useRenewPackagePopup';

function App({ user, moduleApp, history, trackCustomView, ...props }) {
  const navigate = useNavigate();
  const { i18n } = useTranslation();
  const location = useLocation();
  const { showPopup, closePopup, popupType } = useRenewPackagePopup(user);
  dayjs.locale(i18n.language);

  useEffect(() => {
    props.setLanguage(i18n.language);
    handleLogoutAllTab();
  }, []);

  useEffect(() => {
    trackCustomView();
  }, [location]);


  function handleLogoutAllTab() {
    window.addEventListener("storage", (event) => {
      if (event.storageArea === localStorage && event.key === window.location.host + "logout") {
        let isLogout = localStorage.getItem(window.location.host + "logout");
        if (isLogout) {
          props.removeUserState();
        } else {
          props.requestUser();
        }
      }
    }, false);
  }

  return (
    <Theme>
      <SetupNavigation />
      <ConfirmProvider ref={confirmRef}>
        <ToastProvider ref={toastRef}>
          <Suspense fallback={<></>}>
            <AuthInit>
              <Outlet />
            </AuthInit>
          </Suspense>
          <RenewPackagePopup 
            isOpen={showPopup} 
            onClose={closePopup}
            type={popupType}
          />
        </ToastProvider>
      </ConfirmProvider>
    </Theme>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...auth.actions,
  ...app.actions,
  ...tracking.actions,
};

export default withTranslation()(connect(mapStateToProps, mapDispatchToProps)(App));

// export default App
