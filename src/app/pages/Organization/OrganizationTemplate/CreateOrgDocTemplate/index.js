import React, { useEffect, useState } from "react";
import { Form, Input } from "antd";
import { useTranslation } from "react-i18next";

import { toast } from "@component/ToastProvider";
import { useOrganization } from "@app/pages/Organization";

import { AntForm } from "@component/AntForm";
import AntModal from "@component/AntModal";
import Dropzone from "@component/Dropzone";
import UploadProgress from "@component/UploadProgress";

import { CONSTANT } from "@constant";
import RULE from "@rule";

import { uploadOrgDocTemplate } from "@services/DocumentTemplate";

import "./CreateOrgDocTemplate.scss";


function CreateOrgDocTemplate({
                                isOpen, handleCancel,
                                onFinishCreateDoc,
                              }) {
  const { t } = useTranslation();
  
  const { orgId } = useOrganization();
  
  const [formCreateOrgDocTemplate] = Form.useForm();
  
  const [isLoading, setLoading] = useState(false);
  const [fileTemplate, setFileTemplate] = useState(null);
  const [uploadPercent, setUploadPercent] = useState(0);
  
  useEffect(() => {
    if (isOpen) {
      formCreateOrgDocTemplate.resetFields();
      setFileTemplate(null);
    }
  }, [isOpen]);
  
  function onDrop(files) {
    if (files[0]) {
      setFileTemplate(files[0]);
      setUploadPercent(0);
    }
  }
  
  function handleCancelUpload() {
    setFileTemplate(null);
  }
  
  async function createDocumentTemplate(values) {
    if (!fileTemplate) {
      toast.warning({ description: t("FILE_MUST_NOT_BE_MISSING") });
      return;
    }
    
    setLoading(true);
    values.organizationId = orgId;
    const serviceConfig = {
      onUploadProgress: function (progressEvent) {
        let percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        setUploadPercent(Math.min(percentCompleted, 99));
      },
    };
    
    const apiResponse = await uploadOrgDocTemplate(values, fileTemplate, serviceConfig);
    if (apiResponse) {
      setUploadPercent(100);
      setTimeout(() => {
        onFinishCreateDoc(apiResponse);
      }, 300);
    } else {
      setUploadPercent(0);
    }
    setLoading(false);
  }
  
  return <>
    <AntModal
      width={1056}
      formId="create-org-doc-template"
      title={t("UPLOAD_FILE")}
      className="create-org-doc-template-container"
      open={isOpen}
      onCancel={handleCancel}
      footerAlign={CONSTANT.CENTER}
      confirmLoading={isLoading}
      destroyOnClose
    >
      <AntForm
        id="create-org-doc-template"
        form={formCreateOrgDocTemplate}
        layout="vertical"
        requiredMark={true}
        onFinish={createDocumentTemplate}
        disabled={isLoading}
      >
        <AntForm.Item label={t("FILE_NAME")} name="name" rules={[RULE.REQUIRED]}>
          <Input placeholder={t("ENTER_NAME")} />
        </AntForm.Item>
      </AntForm>
      
      {fileTemplate
        ? <UploadProgress
          fileBlob={fileTemplate}
          percent={uploadPercent}
          onCancel={handleCancelUpload}
          disabledCancel={isLoading}
        />
        : <Dropzone
          type={CONSTANT.DOCUMENT}
          onDrop={onDrop}
          hideDesc
        />}
    
    </AntModal>
  </>;
}

export default CreateOrgDocTemplate;