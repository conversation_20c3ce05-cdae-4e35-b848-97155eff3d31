import React, { useEffect, useState } from "react";

import { deleteGroupTool, getGroupTool } from "@services/Tool";
import { Card, Col, Form, Input, Row, Tooltip } from "antd";

import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";

import { BUTTON } from "@constant";
import { LINK } from "@link";
import AntButton from "@component/AntButton";
import { EditOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { handleReplaceUrlSearch, handleSearchParams } from "@common/functionCommons";

import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";
import { usePageViewTracker } from "@src/ga";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import "./GroupTool.scss";

GroupTool.propTypes = {};

function GroupTool(props) {
  usePageViewTracker("GroupTool");

  const { t, i18n } = useTranslation();
  const [formFilter] = Form.useForm();

  const location = useLocation();

  const [allToolGroup, setAllToolGroup] = useState([]);
  const [isLoading, setIsLoading] = useState(false);


  useEffect(() => {
    getAllToolGroup();
  }, [location.search]);

  const getAllToolGroup = async () => {
    setIsLoading(true);
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    const response = await getGroupTool(query);
    if (response) {
      setAllToolGroup(response);
    }
    setIsLoading(false);
  };


  const handleDelete = (toolGroupId, groupName) => {
    confirm.delete({
      title: t("DELETE_GROUP_TOOL"),
      content: t("DELETE_GROUP_TOOL_CONFIRM", { name: groupName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async (e) => {
        setIsLoading(true);
        const apiResponse = await deleteGroupTool(toolGroupId, true);
        if (apiResponse) {
          toast.success(t("DELETE_GROUP_TOOL_SUCCESS"));
          const newDataToolGroup = allToolGroup.filter((item) => item?._id !== apiResponse?._id);
          setAllToolGroup(newDataToolGroup);
        } else {
          toast.error(t("DELETE_GROUP_TOOL_ERROR"));
          setIsLoading(false);
        }
      },
    });
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, 10, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    onSubmitFilter({});
  };


  const columns = [
    {
      title: t("GROUP_NAME"),
      dataIndex: "groupName",
      key: "groupName",
      render: (text) => <span className="group-name-value">{text}</span>,
    },
    {
      title: t("DESCRIPTION"),
      dataIndex: "description",
      key: "description",
    },
    {
      title: t("CODE"),
      dataIndex: "code",
      key: "code",
    },
    {
      title: t("ACTION"),
      key: "action",
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className="tool-group-actions">
          <Tooltip title={t("EDIT_GROUP_TOOL")}>
            <Link to={LINK.ADMIN_PAGE + LINK.DETAIL_GROUP_TOOL.format(record?._id)}>
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="small"
                className={"btn-edit-group-tool"}
                icon={<EditOutlined/>}
              />
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_GROUP_TOOL")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-group-tool"}
              icon={<DeleteIcon/>}
              onClick={() => handleDelete(record?._id, record?.groupName)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];


  return (
    <Loading active={isLoading} transparent>
      <div className="group-tool">
        <Card className="group-tool-info-card">
          <div className="group-tool-info-header">
            <div>
              <h1 className="group-tool-title">{t("GROUP_TOOL_MANAGEMENT")}</h1>
              <p className="group-tool-description">{t("GROUP_TOOL_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN_PAGE + LINK.ADMIN_CREATE_GROUP_TOOL}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create-group-tool"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_GROUP_TOOL")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="group-tool-search-card">
          <Form form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={12}>
                    <Form.Item name="groupName" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_GROUP_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={12} lg={12}>
                    <Form.Item name="code" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_GROUP_CODE_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </Form>
        </Card>

        <Card className="group-tool-table-card">
          <TableAdmin
            dataSource={allToolGroup}
            columns={columns}
            scroll={{ x: 1000 }}
            className={"tool-group-table"}
            rowClassName={() => "group-tool-table-row"}
            locale={{ emptyText: t("NO_GROUP_TOOLS_FOUND") }}
          />
        </Card>
      </div>
    </Loading>
  );
}

export default GroupTool;
