import React, { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import ImageView from "@component/ImageView";

import { API } from "@api";
import { formatTimeDate } from "@common/functionCommons";

import CLICKEE_AVATAR from "@src/asset/logo/clickee-avatar.svg";
import PROJECT_DEFAULT from "@src/asset/image/project-default.svg";
import BUILDING_ICON from "@src/asset/icon/building/building.svg";


function OrgDocTemplateItem({ sourceItem, itemSelected, onSelectItem, publicField }) {
  const { t } = useTranslation();
  
  const [isLoaded, setLoaded] = useState(false);
  
  function onLoadImage() {
    if (!isLoaded) setLoaded(true);
  }
  
  const orgDocAvatarSrc = useMemo(() => {
    return !!sourceItem?.organizationId
      ? API.STREAM_ID.format(sourceItem?.organizationId?.avatarId?.thumbnailFileId)
      : CLICKEE_AVATAR;
  }, [sourceItem]);
  return <>
    <div
      className={clsx("org-doc-template-item",
        { "org-doc-template-item__active": itemSelected?._id === sourceItem?._id },
      )}
      onClick={() => onSelectItem(sourceItem)}
    >
      {!!sourceItem[publicField] && <div className="org-doc-template-item__published">
        {t("PUBLIC")}
      </div>}
      <div className="org-doc-template__preview">
        <img
          className="org-doc-template__thumbnail-image"
          src={API.STREAM_ID.format(sourceItem?.customThumbnail?._id || sourceItem?.templateThumbnailId?._id)} alt=""
          onLoad={onLoadImage}
        />
        {!isLoaded && <div className="org-doc-template__thumbnail-default">
          <img src={PROJECT_DEFAULT} alt=""/>
        </div>}
      </div>
      <div className="org-doc-template__info">
        <ImageView
          className="org-doc-template__info-img"
          src={orgDocAvatarSrc}
          srcDefault={BUILDING_ICON}
        />
        <div className="org-doc-template__info-name">
          {sourceItem.name}
        </div>
      </div>
      {!!sourceItem.lastUsed && <div className="org-doc-template__last-used">
        {t("LAST_USED")}: {formatTimeDate(sourceItem.lastUsed)}
      </div>}
    </div>
  </>;
}

export default OrgDocTemplateItem;