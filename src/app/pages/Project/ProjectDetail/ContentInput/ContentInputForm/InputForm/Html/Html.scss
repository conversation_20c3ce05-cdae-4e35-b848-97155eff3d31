.form-html-input {
  .ant-form-item {
    margin-bottom: 0;

    .ant-form-item-explain-error {
      margin-bottom: 0 !important;
    }

    //.ck-editor {
    //  .ck-sticky-panel__content {
    //    border-radius: 4px 4px 0 0 !important;
    //    border: solid 1px #F1F1F1 !important;
    //    border-bottom: none !important;
    //  }
    //
    //  .ck-content {
    //    border-radius: 0 0 4px 4px !important;
    //    border-width: 1px !important;
    //    border-style: solid !important;
    //    background-color: #ffffff !important;
    //    border-color: #F1F1F1 !important;
    //    padding: 0 15px !important;
    //  }
    //
    //  &:hover,
    //  &:focus-within {
    //    .ck-sticky-panel__content {
    //      border-top-color: #09196B !important;
    //      border-right-color: #09196B !important;
    //      border-left-color: #09196B !important;
    //    }
    //
    //    .ck-content {
    //      border-bottom-color: #09196B !important;
    //      border-right-color: #09196B !important;
    //      border-left-color: #09196B !important;
    //    }
    //  }
    //
    //  &:focus-within {
    //    box-shadow: 0 0 0 2px rgba(3, 15, 45, 0.4);
    //    outline: 0;
    //    border-radius: 4px;
    //  }
    //}
  }
}