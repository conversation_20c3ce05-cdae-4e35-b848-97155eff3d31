import { Switch } from "antd";
import { useTranslation } from 'react-i18next';
import React, { use, useEffect, useRef, useState } from 'react';
import { io } from "socket.io-client";
import "./ShowQuestion.scss";
import PAUSE_ICON from "@src/asset/icon/pause.svg";
import PLAY_ICON from "@src/asset/icon/PLAY_ICON.svg";
// import HIDE_ICON from "@src/asset/icon/HIDE_ICON.svg";
import ShowIcon from '@src/assets/icons/show.svg';
import HideIcon from '@src/assets/icons/hide.svg';
import START_ICON from "@src/asset/icon/Arrow _Right_ICON.svg";
import Arrow_Right_Square_ICON from "@src/asset/icon/Arrow-Right-Square.svg";
import Arrow_Right_Square_Default_ICON from "@src/asset/icon/Arrow-Right-Square-default.svg";
import Refresh_ICON from "@src/asset/icon/refresh.svg";
import STAR_ICON from "@src/asset/icon/Start-blue.svg";
import RECORD_ICON from "@src/asset/icon/record.svg";
import STOP_ICON from "@src/asset/icon/stop-recording.svg";
import Disable_icon from "@src/asset/gif/rolling.gif"
import Warning_icon from "@src/asset/icon/warning.svg"
import Animation from '@src/asset/gif/Animation-Loading.gif'
import clsx from "clsx";
import ReactPlayer from "react-player";
import { API } from "@api";
import { useDispatch, useSelector } from "react-redux";
import { CONSTANT, RECOGNIZE_STATUS, RECORD_STATUS } from "@constant";
import { actions, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";
import { changeQuestion } from "@src/app/services/SpeakingRoom";
import { toast } from "@src/app/component/ToastProvider";
import LoadingStart from "./LoadingStart/LoadingStart";
import CustomLiveAudioVisualizer from "./CustomLiveAudioVisualizer";
import { useNavigate, useParams } from "react-router-dom";
import { LINK } from "@link";


const ShowQuestionPart2 = ({ detailsTopic, isCheckFullTest, isShowHint, contentHint, handleGetSessionDetail, totalDetailsTopic, fullTest, isAutoNextQuestion, setIsAutoNextQuestion, setIsStart }) => {
  const { t } = useTranslation();
  const { id } = useParams();
  const user = useSelector(state => state.auth.user);
  const studentTools = useSelector(state => state.tool.studentTools);

  const playerRef = useRef(null);
  const socket = useRef(null);
  const audioSourceRef = useRef(null);
  const scriptProcessorRef = useRef(null);

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState(120);
  const [timeWait, setTimeWait] = useState(60)
  const [countdown, setCountDown] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isShowAnswer, setIsShowAnswer] = useState(false);
  // const [isAutoNextQuestion, setIsAutoNextQuestion] = useState(false);
  const [audioSource, setAudioSource] = useState(null);
  const [isPlaying, setPlaying] = useState(true); // mặc định là true để phát âm thanh đầu tiên
  const [shouldRestart, setShouldRestart] = useState(false); // để xử lý phát lại từ đầu

  const [aiAudioFrameForVisualizer, setAiAudioFrameForVisualizer] = useState(null)

  const [recordStatus, setRecordStatus] = useState(RECORD_STATUS.STOPPED);
  const timeRef = useRef(null);
  const [timeRecorded, setTimeRecorded] = useState(0);
  const [hasMicAccess, setHasMicAccess] = useState(true);

  const [checkedContentSpeech, setCheckedContentSpeech] = useState('')
  const [stopLoading, setStopLoading] = useState(false)
  const [showPopup, setShowPopup] = useState(false)
  const [stopTime, setStopTime] = useState(false);
  const [startLoading, setStartLoading] = useState(false)

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const lines = detailsTopic[currentQuestionIndex]?.questionText?.split(/\n+/);

  const countWords = (text) => {
    if (!text || typeof text !== 'string') {
      return 0;
    }
    const trimmedText = text.trim();
    if (trimmedText === '') {
      return 0;
    }
    // Tách chuỗi bằng một hoặc nhiều khoảng trắng làm dấu phân cách
    const words = trimmedText.split(/\s+/);
    return words.length;
  };

  useEffect(() => {
    if (timeLeft <= 0 || !isRecording || stopTime) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer); // clear khi component unmount
  }, [isRecording, timeLeft, stopTime]);

  useEffect(() => {
    if (timeWait <= 0 || !countdown) return;

    const timer = setInterval(() => {
      setTimeWait(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer); // clear khi component unmount
  }, [countdown, timeWait]);

  useEffect(() => {
    if (timeLeft <= 0 && isRecording) {
      setStopTime(true)
      setStopLoading(true);
      handleStopRecordClick()
      // setStopLoading(false);
    }
    if (timeLeft < 100 && !isRecording && countWords(checkedContentSpeech) > 40) {
      if(isAutoNextQuestion) {
        navigate(LINK.SPEAKING_ROOM_PRACTICE_PART_THREE.format(id))
        isShowHint(false)
        setIsStart(false)
        setShowPopup(false)  
        isCheckFullTest(false)   
      } else {
        isCheckFullTest(true)
      }
    }
  }, [timeLeft, isRecording, detailsTopic?.length, checkedContentSpeech, isAutoNextQuestion]);

  useEffect(() => {
    setAudioSource(API.STREAM_MEDIA.format(detailsTopic[currentQuestionIndex]?.audioId));
  }, [currentQuestionIndex, detailsTopic]);


  const handlePlay = () => {
    if (shouldRestart || !isPlaying) {
      playerRef.current.seekTo(0); // phát lại từ đầu
      setShouldRestart(false);     // sau khi chạy lại từ đầu thì đặt lại cờ
    }
    setPlaying(true);
  };

  const handlePause = () => {
    setPlaying(false);
    setShouldRestart(true); // đánh dấu là lần sau phải phát lại từ đầu
  };

  const handleEnded = () => {
    setPlaying(false);
    setShouldRestart(true); // khi phát xong cũng phải phát lại từ đầu nếu nhấn play
    setCountDown(true);
  };

  // Hàm định dạng số: 30 -> "00:30"
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const secs = (seconds % 60).toString().padStart(2, "0");
    return `${mins}:${secs}`;
  };

  const handleToggleRecording = () => {
    if (isRecording) {
      setStopLoading(true);
      setStopTime(true);
      setCheckedContentSpeech('')
      handleStopRecordClick()
    } else {
      isCheckFullTest(false)
      setStopTime(false);
      setStartLoading(true);
      setCheckedContentSpeech('')
      setTimeLeft(120); // Reset thời gian khi bắt đầu ghi âm
      setTimeWait(0)
      handleStartRecord()
    }
  }

  const handleNextQuestion = () => {
    setCurrentQuestionIndex(prevIndex => {
      if (!isRecording && !isAutoNextQuestion && timeLeft < 100 && countWords(checkedContentSpeech) > 40) {
        isShowHint(false)
        isCheckFullTest(true)
        return prevIndex; // Stay at the last questio                
      } else if (totalDetailsTopic > 5) {
        setShowPopup(true)
        return prevIndex;
      } else {
        return prevIndex;
      }
    });
  }

  const handleSkipQuestion = () => {
    setCurrentQuestionIndex(prevIndex => {
      if (prevIndex < detailsTopic?.length - 1) {
        setShowPopup(false)
        setPlaying(true)
        isShowHint(false)
        setTimeLeft(120); // Reset thời gian khi chuyển sang câu hỏi mới
        return prevIndex + 1;
      } else {
        // isCheckFullTest(true)
        navigate(LINK.SPEAKING_ROOM_PRACTICE_PART_THREE.format(id))
        isShowHint(false)
        setIsStart(false)
        setShowPopup(false)
        return prevIndex;
      }
    });
  }

  const handleChangeQuestion = async () => {
    const data = {
      questionId: detailsTopic[currentQuestionIndex]?._id,
      part: detailsTopic[currentQuestionIndex]?.part,
    }
    try {
      await changeQuestion(detailsTopic[currentQuestionIndex]?.sessionId, data)
      handleGetSessionDetail();
    } catch (error) {
      console.log(error);
    }
  }

  const handleShowhint = (value) => {
    isShowHint(true);
    contentHint(value)
  }

  const handleRecordAgain = () => {
    setTimeLeft(120)
    setCheckedContentSpeech('')

  }

  function handleDataResponse(socket) {
    socket.on("error", (err) => {
      socket.disconnect();

    });

    socket.on("finish-recognition", () => {
      setStopLoading(false);
      setIsRecording(false);
      socket.disconnect();

    });
    socket.on("message", (message) => {
      //if (message.state !== "recognizing")
      //console.log("Message from server:", message);

      switch (message.state) {

        case "recognized_text":
          console.log("recognized_text", message);
          setCheckedContentSpeech(message.recognizedText)

          break;

        default:
          break;
      }
    });
  }

  useEffect(() => {
    // Dọn dẹp khi component unmount
    return () => {
      if (socket.current) socket.current.disconnect();
    };
  }, []);

  useEffect(() => {
    if (timeRecorded >= CONSTANT.MAX_RECORD) {
      setRecordStatus(RECORD_STATUS.STOPPED);
    }
  }, [timeRecorded]);

  async function handleStartRecord() {
    setRecordStatus(RECORD_STATUS.PREPARE_RECORD);

    setTimeRecorded(0);
    dispatch(actions.trackCustomClick(TRACKING_ACTIONS.INPUT_ESSAY));
  }

  function handleStopRecord() {
    clearInterval(timeRef.current);
    setRecordStatus(RECORD_STATUS.STOPPED);
    setAiAudioFrameForVisualizer(null)

    console.log("stopRecording");
    if (scriptProcessorRef.current) {
      scriptProcessorRef.current.onaudioprocess = null; // Hủy bỏ callback để ngừng xử lý âm thanh
      scriptProcessorRef.current.disconnect(); // Ngừng kết nối scriptProcessor
    }

    if (audioSourceRef.current) {
      console.log("stopRecording audioSteam");
      audioSourceRef.current.disconnect();
      audioSourceRef.current.mediaStream?.getTracks()?.forEach(track => track.stop());
    }

    socket.current?.emit("close-recording");
  }

  const handleStopRecordClick = () => {
    if (recordStatus === RECORD_STATUS.RECORDING) {
      setRecordStatus(RECORD_STATUS.STOPPED);
    }
  }

  useEffect(() => {
    console.log("recordStatus", recordStatus);

    switch (recordStatus) {
      case RECORD_STATUS.PREPARE_RECORD:
        handlePrepareRecord();
        break;
      case RECORD_STATUS.RECORDING:
        handleDataResponse(socket.current);
        handleRecordAudio();
        break;
      case RECORD_STATUS.STOPPED:
        handleStopRecord();
        break;
      default:
        break;
    }

  }, [recordStatus]);

  async function handlePrepareRecord() {
    socket.current = io("/speaking-advanced", { transports: ["websocket"], path: "/socket" });

    socket.current.on("connect", () => {
      console.log("Connected to WebSocket server");
      // Không gửi dữ liệu ở đây, chỉ log kết nối thành công
    });

    socket.current.on("server_ready", () => {
      setRecordStatus(RECORD_STATUS.RECORDING);
    });

    socket.current.on("error", (err) => {
      console.log("socket error", err);
      socket.current.disconnect();
      //   setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
    });
  }


  async function handleRecordAudio() {
    // const topic = speakingForm?.getFieldValue("topic");
    const instructionId = studentTools.speaking.instructionIds?.[0]?._id;

    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      .then(stream => {
        console.log("Đã truy cập micrô:", stream);
        setIsRecording(true)
        setStartLoading(false)
        return stream; // Trả về stream
      })
      .catch(error => {
        console.error("Lỗi truy cập micrô:", error);
        return null; // Trả về null nếu có lỗi
      });

    if (!stream) {
      setRecordStatus(RECORD_STATUS.STOPPED);
      //   setRecognizeStatus(RECOGNIZE_STATUS.NOT_STARTED);
      setHasMicAccess(false);
      return toast.error(t("MICROPHONE_NOT_ACCESSIBLE"), { replace: true });
    }

    // 2. Tạo AudioContext
    const audioContext = new AudioContext({ sampleRate: 16000 });

    // 3. Kết nối stream với AudioContext
    const source = audioContext.createMediaStreamSource(stream);
    audioSourceRef.current = source;
    // 4. Tạo ScriptProcessorNode
    const bufferSize = 4096; // Kích thước khối xử lý
    const scriptProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);
    scriptProcessorRef.current = scriptProcessor;
    // 5. Xử lý dữ liệu PCM trong callback
    scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
      const inputBuffer = audioProcessingEvent.inputBuffer;
      const channelData = inputBuffer.getChannelData(0); // Lấy dữ liệu kênh đầu tiên (mono)
      const channelDataCopy = new Float32Array(channelData);
      setAiAudioFrameForVisualizer(channelDataCopy);

      // Chuyển đổi Float32 (-1.0 -> 1.0) sang Int16 (-32768 -> 32767)
      const pcmInt16Array = new Int16Array(channelData.length);
      for (let i = 0; i < channelData.length; i++) {
        pcmInt16Array[i] = Math.max(-1, Math.min(1, channelData[i])) * 0x7FFF;
      }

      // Tạo Uint8Array từ Int16Array để gửi qua socket
      const pcmUint8Array = new Uint8Array(pcmInt16Array.buffer);

      // Gửi dữ liệu qua socket
      const inputData = {
        part: detailsTopic[currentQuestionIndex]?.part,
        questionId: detailsTopic[currentQuestionIndex]?._id,
        sessionId: detailsTopic[currentQuestionIndex]?.sessionId,
        questionText: detailsTopic[currentQuestionIndex]?.questionText,
        questionIndex: detailsTopic[currentQuestionIndex]?.questionIndex,
      };

      socket.current?.emit("audio", {
        buffer: pcmUint8Array,
        inputData,
        userId: user._id,
      });
    };

    // 6. Kết nối các nodes
    source.connect(scriptProcessor);
    scriptProcessor.connect(audioContext.destination);

    timeRef.current = setInterval(() => {
      setTimeRecorded(prevState => prevState + 1);
    }, 1000);
  }

  const TextQuestion = () => {
    if(!lines) {
      return;
    }
    return (
      <div className="question__content__body__left__text__part2">
        <h4>{lines[0]} {lines[1]}</h4>
        <ul style={{ paddingLeft: '20px' }}>
          {lines.slice(2, 5).map((line, idx) => (
            <li key={idx}>{line.replace('• ', '')}</li>
          ))}
        </ul>
        <p>{lines[5]}</p>
      </div>
    )
  }

  return (
    <>
      <>
        <div className='question__content__start'>
          <p className='question__content__start_question'>
            {t('QUESTION')} {currentQuestionIndex + 1}/{detailsTopic?.length}
          </p>
          <div className="question__content__start__mode-toggle">
            <Switch
              checked={isAutoNextQuestion}
              onChange={() => setIsAutoNextQuestion(!isAutoNextQuestion)}
            />
            <span className="question__content__start__mode-toggle__label">{t('QUESTION_AUTO_PLAY')}</span>
          </div>
        </div>
        <div className='question__content__body'>
          <div className='question__content__body__left'>
            <img
              src={Refresh_ICON}
              alt="icon"
              className='question__content__body__left__icon'
              onClick={handleChangeQuestion}
            />
            <div className='question__content__body__left__text'>
              <img
                src={isPlaying ? PLAY_ICON : PAUSE_ICON}
                alt="icon"
                className='question__content__body__left__text__icon'
                onClick={isPlaying ? handlePause : handlePlay}
              />
              {isShowAnswer ?
                <div style={{ marginTop: '8px' }}>
                  {'*'.repeat(39)}
                </div> :
                <TextQuestion />
              }

              <img
                src={!isShowAnswer ? ShowIcon : HideIcon}
                alt="icon"
                className='question__content__body__left__text__hide'
                onClick={() => setIsShowAnswer(!isShowAnswer)}
              />
            </div>
          </div>
          {startLoading || fullTest || (timeLeft >= 100 && timeLeft < 120) || (countWords(checkedContentSpeech) <= 40 && countWords(checkedContentSpeech) > 0) || isRecording || isAutoNextQuestion || (currentQuestionIndex === detailsTopic?.length - 1 && ((timeLeft >= 25 && timeLeft <= 30) || (countWords(checkedContentSpeech) <= 10 && countWords(checkedContentSpeech) >= 0)) && totalDetailsTopic <= 5) ? (
            <div className='question__content__body__right'>
              <img
                src={Arrow_Right_Square_Default_ICON}
                alt="icon"
                className='question__content__body__right__icon'
                style={{ cursor: "not-allowed" }}
              />
            </div>
          ) : (
            <div className='question__content__body__right'
              onClick={() => handleNextQuestion()}
            >
              <img
                src={Arrow_Right_Square_ICON}
                alt="icon"
                className='question__content__body__right__icon'
              />
              {showPopup && (
                <div className="popup-skip__icon"></div>
              )}
            </div>
          )}
        </div>
        {showPopup && (
          <div className="popup-skip">
            <div className={`popup-skip-question${detailsTopic[currentQuestionIndex]?.questionText.length > 39 && detailsTopic.length !== 1 ? '' : detailsTopic.length === 1 ? "__part2" : '__short'}`}>
              <div className="popup-skip-question__container">
                <div className="popup-skip-question__content">
                  {t('CONTENT_POPUP')}
                </div>
                <div className="popup-skip-question__button">
                  <div className="popup-skip-question__button__skip" onClick={() => handleSkipQuestion()}>
                    {t('SKIP')}
                  </div>
                  <div className="popup-skip-question__button__answer" onClick={() => setShowPopup(false)}>
                    {t('ANSWER_SKIP')}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        <div className="boder-top"></div>
        <div className={`question__content__note${isRecording || timeWait <= 0 ? '__ban' : ''}`}>
          <div className="question__content__note__header">
            <div className="question__content__note__header__left">
              {t('NOTE')}
            </div>
            <div className="question__content__note__header__right">
              <p className="question__content__note__header__right__text">
                {t('PREPARING_TIMER')}
              </p>
              <p className="question__content__note__header__right__time">
                {formatTime(timeWait)}
              </p>
            </div>
          </div>
          <textarea
            rows={4}
            placeholder={t("ENTER_YOUR_NOTES")}
            className={`custom-textarea${isRecording || timeWait <= 0 ? '__ban' : ''}`}
            disabled={isRecording || timeWait <= 0}
            onInput={(e) => {
              e.target.style.height = "auto"; // Reset trước khi tính lại
              const newHeight = Math.min(e.target.scrollHeight, 150); // Giới hạn chiều cao tối đa
              e.target.style.height = newHeight + "px";
            }}
          />
        </div>
        <div className="question__content__content"
          style={{ borderTop: 'none' }}
        >
          <div className='question__content__content__box'>
            <p className="question__content__content__box__time">
              {formatTime(timeLeft)}
            </p>
            <div className="question__content__content__box__right"
              onClick={() => handleShowhint(detailsTopic[currentQuestionIndex]?.hint)}
            >
              <img
                src={STAR_ICON}
                alt="icon"
                className='question__content__content__box__right__icon'
              />
              <span className="question__content__content__box__right__text">
                {t('HINT_ANSWER')}
              </span>
            </div>
          </div>
        </div>
        <div className="question__content__footer">
          {stopLoading ? (
            <img
              src={Disable_icon}
              alt="icon"
              className='question__content__footer__icon'
              style={{ cursor: "not-allowed" }}
            />
          ) : (
            <>
              {startLoading ? (
                <LoadingStart isLoading={startLoading}>
                </LoadingStart>
              ) : (
                <img
                  src={isRecording ? STOP_ICON : RECORD_ICON}
                  alt="icon"
                  className='question__content__footer__icon'
                  onClick={handleToggleRecording}
                />
              )}
            </>
          )}
          <p className='question__content__footer__text'>
            {(!isRecording && timeLeft === 120) ? t('STRAT_RECORDING') : (!isRecording && timeLeft !== 120) ? t('CLICK_TO_RECORD') : (stopLoading) ? t('PROCESSING_AUDIO') : t('RECORDING')}
          </p>
          {isRecording && aiAudioFrameForVisualizer && (
            <CustomLiveAudioVisualizer
              audioData={aiAudioFrameForVisualizer}
            />
          )}
        </div>

        {(((timeLeft >= 100 && timeLeft < 120) || (countWords(checkedContentSpeech) <= 40 && checkedContentSpeech.length > 0)) && !isRecording) && (
          <div className="check-violate">
            <img
              src={Warning_icon}
              alt=""
              className=""
            />
            <div className="check-violate__box">
              <div className="check-violate__box__1">
                {t('YOUR_SPEECH_IS_NOT_LONG_ENOUGH_TO_SUBMIT')}
              </div>
              <div className="check-violate__box__1">
                {t('PLEASE_RECORD_AGAIN')}
              </div>
            </div>
          </div>
        )}

        {audioSource && (
          <ReactPlayer
            ref={playerRef}
            url={audioSource}
            playing={isPlaying}
            controls={false}
            width="0"
            height="0"
            muted={false}
            onEnded={handleEnded}
          />
        )}
      </>
    </>
  );
}

export default ShowQuestionPart2;