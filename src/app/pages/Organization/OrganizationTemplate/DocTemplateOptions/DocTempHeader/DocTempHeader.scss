.doc-template-header-content {
  box-shadow: var(--shadow-level-2);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .doc-template-header__option-title {
    font-weight: 700;
  }

  .doc-template-header__option-form {
    display: flex;
    flex-direction: row;

    .doc-template-header__option-form-label {
      max-width: 30%;

      .option-form-label-item {
        line-height: 32px;
        margin-bottom: 16px;
        font-weight: 600;
        display: flex;

        .option-form-label-item__text {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
        }

        &:after {
          content: ':';
          margin-block: 0;
          margin-inline-start: 2px;
          margin-inline-end: 8px;
        }
      }
    }

    .doc-template-header__option-form-value {
      flex: 1;
    }
  }

  .doc-template-header__option-action {
    display: flex;
    justify-content: center;
    gap: 8px;
  }
}