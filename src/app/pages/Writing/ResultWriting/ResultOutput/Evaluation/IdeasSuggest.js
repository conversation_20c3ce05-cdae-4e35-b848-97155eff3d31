import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";
import { CONSTANT } from "@constant";
import { WRITING_EVALUATION_LIST } from "./functionCommon";

const IdeasSuggest = ({ ideas = [], evaluationSelected }) => {
  const { t } = useTranslation();

  if (!ideas?.length || !['ideas', CONSTANT.ALL].includes(evaluationSelected)) return null;

  return <div className="evaluation__content__item evaluation-improved-essay">
    <div
      className="content__item__title evaluation-improved-essay__title"
      style={{ color: WRITING_EVALUATION_LIST.ideas.color }}
    >
      <img src={WRITING_EVALUATION_LIST.ideas.icon} alt="" />
      {t("IDEA_SUGGESTION")}
    </div>
    {Array.isArray(ideas) ? <ul>
      {ideas.map((idea, index) => (
        <li key={index}>{idea}</li>
      ))}
    </ul>
      : <HtmlContent dangerouslySetInnerHTML={{ __html: ideas }} />}
  </div>;
};

export default IdeasSuggest;