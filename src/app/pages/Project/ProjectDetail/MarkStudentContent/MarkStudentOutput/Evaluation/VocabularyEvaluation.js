import { List } from "antd";
import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";

const VocabularyEvaluation = ({ vocabularies = [] }) => {
  const { t } = useTranslation();
  if (!vocabularies?.length) return null;
  
  return <div className="evaluation__content__item evaluation-vocabulary">
    <div className="content__item__title evaluation-vocabulary__title">{t("VOCABULARY")}</div>
    
    {Array.isArray(vocabularies) ? <List
      bordered
      dataSource={vocabularies}
      renderItem={(item) => (
        <List.Item>
          <div className="vocabulary-item">
            <span className="vocabulary-item__word">{`${item.word}: `}</span>
            {`(${item.translation}) ${item.description}`}
            <div>{`Example: ${item.example}`}</div>
          </div>
        </List.Item>
      )}
    /> : <HtmlContent dangerouslySetInnerHTML={{ __html: vocabularies }}/>}
  
  </div>;
};


export default VocabularyEvaluation;