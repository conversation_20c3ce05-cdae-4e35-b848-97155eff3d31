import React, { useEffect, useState } from "react";
import { Col, Form, Input, InputNumber, Popover, Row, Select } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import { useDropzone } from "react-dropzone";

import AntButton from "@component/AntButton";
import CancelIcon from "@component/SvgIcons/CancelIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";
import Upload from "@component/SvgIcons/Upload";
import CustomModal from "@component/CustomModal";

import { cloneObj, coverLangArrayToObject, coverLanguageObjectToArray } from "@common/functionCommons";

import { BUTTON, INPUT_TYPE, LANG_OPTIONS, RULES } from "@constant";

import "./VoiceDetailModal.scss";
import { AntForm } from "@src/app/component/AntForm";


const VoiceDetailModal = ({ ...props }) => {
  const { t } = useTranslation();
  const { isShowModal, voiceOptionData } = props;
  const [formOption] = Form.useForm();

  const [file, setFile] = useState(null);

  useEffect(() => {
    if (voiceOptionData) {
      formOption.setFieldsValue(voiceOptionData);
    }
  }, [voiceOptionData]);

  const onFinish = async (values) => {
    let data = { ...values };
    if (voiceOptionData) {
      data = { ...data, _id: voiceOptionData._id };
    }
    await props.handleOk(data, file);
    formOption.resetFields();
    setFile(null);
  };

  const handleUpload = (files) => {
    const file = files[0];
    if (file) {
      setFile(file);
    }
  }

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop: handleUpload,
    noClick: true,
    accept: {
      "audio/mp3": [".mp3"],
      "audio/wav": [".wav"],
    },
    multiple: false,
  });

  const handleCancel = () => {
    formOption.resetFields();
    props.handleCancel();
  };

  const handleDeleteFile = () => {
    setFile(null);
  }

  return (
    <CustomModal
      isShowModal={isShowModal}
      closeIcon
      handleCancel={handleCancel}
      form="voice-form"
      footerAlign="center"
      width={800}
      okText={voiceOptionData ? t("UPDATE") : t("CREATE")}
      title={voiceOptionData ? t("EDIT_VOICE_OPTION") : t("CREATE_VOICE_OPTION")}
    >
      <div className="voice-modal__content">
        <Form id="voice-form" onFinish={onFinish} layout="vertical" size={"large"} form={formOption}>
          <Row gutter={24}>
            <Col xs={24}>
              <Form.Item
                label={t("NAME")}
                name="name"
                rules={[{ required: true, message: t("NAME_REQUIRED") }]}
              >
                <Input placeholder={t("ENTER_NAME")} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                label={t("CODE")}
                name="code"
                rules={[{ required: true, message: t("CODE_REQUIRED") }]}
              >
                <Input placeholder={t("ENTER_CODE")} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                label={t("IS_PUBLIC")}
                name="isPublic"
                rules={[{ required: true, message: t("IS_PUBLIC_REQUIRED") }]}
              >
                <Select
                  options={[
                    { value: true, label: t("YES") },
                    { value: false, label: t("NO") },
                  ]}
                  placeholder={t("SELECT_IS_PUBLIC")}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={t("VOICE_FILE")}
                className="upload-file-item"
              >
                <div className="voice-upload-container">
                  <div className="voice-upload-dropzone" {...getRootProps()}>
                    {!file ? (
                      <div className="voice-upload-placeholder">
                        <div className="voice-upload-icon">
                          <i className="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div className="voice-upload-text">
                          <p>{t("DRAG_DROP_AUDIO")}</p>
                          <span>{t("OR")}</span>
                          <AntButton type={BUTTON.DEEP_NAVY} size="large" onClick={open}>
                            {t("BROWSE_FILES")}
                          </AntButton>
                        </div>
                        <div className="voice-upload-formats">
                          <p>{t("SUPPORTED_FORMATS")}: MP3, WAV</p>
                        </div>
                      </div>
                    ) : (
                      <div className="file-upload">
                        <span className="file-upload__file-name">{file?.name}</span>
                        <Popover content={t("DELETE_FILE")} placement="top" trigger="hover">
                          <AntButton
                            type={BUTTON.WHITE_RED}
                            size="small"
                            icon={<DeleteIcon />}
                            onClick={handleDeleteFile}
                          >
                            {t("REMOVE")}
                          </AntButton>
                        </Popover>
                      </div>
                    )}
                  </div>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="hidden">
        <input {...getInputProps()} />
      </div>
    </CustomModal>
  );
};

export default VoiceDetailModal;
