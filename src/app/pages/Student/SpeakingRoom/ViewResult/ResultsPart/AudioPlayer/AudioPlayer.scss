.speaking-recorded-audio-player {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;

  height: 48px;
  background-color: #F6F8FA;
  border-radius: 16px;
  padding: 8px;


  .audio-player__play {
    height: 28px;

    .ant-btn {
      overflow: hidden;

      .ant-btn-icon img {
        width: 32px;
        height: 32px;
      }
    }
  }

  .audio-player__time {
    align-self: center;
  }

  .audio-player__seek-bar {
    flex: 1;
    align-self: center;
    padding: 0 4px;
  }
}


.rc-slider-rail {
    position: absolute;
    width: 100%;
    height: 4px;
    background-color: #c8cfd9;
    border-radius: 6px;
}