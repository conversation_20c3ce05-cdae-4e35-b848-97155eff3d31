.tool-detail {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  background-color: var(--background-light-background-2);
  border-radius: 8px;
  font-family: Segoe UI;

  .tool-detail-header__title {
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    color: var(--typo-colours-primary-black);
  }

  .ant-form-item-label {
    label {
      height: unset !important;
    }
  }

  .form-detail-tool {
    gap: 24px;
  }

  .ant-form-item-required {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
    padding: 0px;
    flex-direction: row-reverse;

    &:after {
      display: none;
    }
  }

  .tool-detail-localization-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .tool-detail-localization-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;


  }

  .items-baseline {
    width: 100%;
  }

  .tool-detail-item-action {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 5px;
  }

  .first-actions {
    align-items: center;
    margin-top: 33px;
  }

  .btn-cancel-add-tool-detail {
    box-shadow: var(--shadow-level-2);
  }

  .add-tool-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    button {
      box-shadow: var(--shadow-level-2);
      color: var(--primary-colours-blue) !important;
    }
  }
}