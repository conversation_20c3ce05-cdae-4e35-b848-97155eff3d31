.wait-list-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .wait-list-info-card,
  .wait-list-search-card,
  .wait-list-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .wait-list-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .wait-list-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .wait-list-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }
  }

  // Search form styles
  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .form-filter {
    width: 100%;
  }

  .ant-form-item {
    margin: 0;
  }

  // Table styles
  .wait-list-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .wait-list-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .wait-list-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 8px;
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .wait-list-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .search-buttons {
      margin-top: 16px;
      width: 100%;
      justify-content: space-between;
    }
  }
}