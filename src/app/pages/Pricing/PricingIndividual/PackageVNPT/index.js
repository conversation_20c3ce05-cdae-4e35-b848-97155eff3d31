import { useEffect, useState } from "react";
import './PackageVinaphone.scss'
import PackageVinaphoneItem from "./PackageVinaphoneItem";
import { getAllFeature } from "@src/app/services/Package";
import { getAvailablePromotions } from "@src/app/services/Promotion";
import { getCurrentPackage } from "@src/app/services/Subscription";

const PackageVinaphone = ({dataVNPT}) => {
  const [dataVinaphone, setDataVinaphone] = useState([])
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      const isMobileUA = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      setIsMobile(isMobileUA);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  useEffect(() => {
    getData()
  }, [dataVNPT])

  const getData = async () => {
    const [
      featuresResponse,
      promotionResponse,
      currentPackageResponse] = await Promise.all([
        getAllFeature(),
        getAvailablePromotions(),
        getCurrentPackage()
      ]);
    if (dataVNPT.length) {
      const reparePackage = dataVNPT.sort((a, b) => a.order - b.order).map((item) => {
        const featuresData = [];
        if (featuresResponse.length) {
          Object.entries(item.features).forEach(([featureId, value]) => {
            const feature = featuresResponse.find((item) => item._id === featureId);
            featuresData.push({
              name: feature?.localization?.description,
              unit: feature?.unit,
              count: feature?.type === "Boolean" ? 0 : value,
            });
          });
        }
        if (item?.prices.length && promotionResponse) {
          const promotionPackage = promotionResponse?.[item._id] || {};
          Object.entries(promotionPackage).forEach(([key, value]) => {
            item.prices[key - 1] = {
              ...item.prices[key - 1],
              promotion: value?.discount,
              promotionType: value?.type,
              priceIndex: key
            }
          })
        }
        if (currentPackageResponse?.length && currentPackageResponse.some((current) => current.packageId._id === item._id)) {
          item.isUsingPackage = true;
        }
        return {
          ...item,
          features: featuresData,
        };
      });
      setDataVinaphone(reparePackage);
    }
  };

  if (!dataVinaphone?.length) return null;
  return (
    <div className="pricing-package-vinaphone">
      {dataVinaphone?.map((item, index) => (
        <PackageVinaphoneItem key={index} packageData={item} isMobile={isMobile} />
      ))}
    </div>
  );
}

export default PackageVinaphone;