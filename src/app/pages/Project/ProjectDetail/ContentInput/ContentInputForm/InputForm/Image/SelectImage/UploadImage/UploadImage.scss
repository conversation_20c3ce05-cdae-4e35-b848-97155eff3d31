.upload-image-container {
  background: var(--primary-colours-blue-navy-light-2);
  color: var(--primary-colours-blue-navy);

  * {
    outline: none;
  }

  &:not(.disabled-upload) .upload-image__preview img {
    cursor: pointer;
  }

  .upload-image__preview {
    display: flex;

    img {
      user-select: none;
      width: 100%;
      aspect-ratio: 2.2 / 1;
      object-fit: contain;
    }
  }

  .upload-image-content {
    display: flex;
    justify-content: center;

    .upload-image-inner {
      max-width: 500px;
      padding: 80px 20px;
      display: flex;
      text-align: center;
      gap: 24px;
      flex-direction: column;


      .upload-image__title {
        font-size: 24px;
        font-weight: 700;
      }

      .upload-image__description {

      }
    }
  }
}