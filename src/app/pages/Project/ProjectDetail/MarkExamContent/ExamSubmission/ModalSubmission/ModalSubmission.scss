.exam-submission-modal {
  @media screen and (max-width: 767.98px) {
    width: 100% !important;
  }

  @media screen and (min-width: 768px) and (max-width: 1023.98px) {
    width: 90% !important;
  }

  @media screen and (min-width: 1024px) and (max-width: 1535.98px) {
    width: 70% !important;
  }

  @media screen and (min-width: 1536px) {
    width: 60% !important;
  }

  .form-add-submission {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .ant-form-item {
      margin-bottom: 0;

      .submission-content-textarea {
        textarea {
          max-height: calc(100vh - 450px);
        }
      }
    }

    .form-add-submission__content {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .content-title {
        font-weight: 600;
      }

      .content-type {
        display: flex;
        gap: 8px;

        .ant-btn {
          font-weight: 400;
        }
      }
    }
  }

  .exam-submission-modal-actions {
    margin-top: 24px;
    justify-content: center;
    display: flex;
    gap: 24px;
  }

  table {
    .ant-table-cell {
      padding: 10px 16px !important;
    }
  }

  .images-content {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 36px 24px;
    padding: 24px;
    border: 1px solid var(--lighttheme-content-background-stroke);

    &.has-image {
      padding-bottom: 36px;
    }

    .sortable-container {
      display: contents;
    }

    .images-content__item {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      aspect-ratio: 1/1;
      border: 1px solid var(--lighttheme-content-background-stroke);

      &.warning-exceed-image {
        border-color: var(--support-colours-red);

        .item__name{
          color: var(--support-colours-red);
        }
      }

      img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 100%;
        max-height: 100%;
        object-fit: cover;
        object-position: center;
      }

      .item__name {
        position: absolute;
        top: calc(100% + 4px);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 100%;
        text-align: center;
      }

      .item__order {
        position: absolute;
        top: 0;
        left: 0;
        margin: 2px;
        width: 23px;
        height: 23px;
        border: 1px solid #F5F5F5;
        background-color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .item__backdrop {
        cursor: pointer;
        position: absolute;
        top: -1px;
        bottom: -1px;
        left: -1px;
        right: -1px;
        background-color: #000000B2;


        display: flex;
        align-items: center;
        justify-content: center;

        .item__delete {
          position: absolute;
          top: 8px;
          right: 8px;
          width: 24px !important;
          min-width: 24px !important;
          height: 24px !important;
          background-color: unset;
          border: none !important;

          svg {
            width: 24px;
            height: 24px;
          }

          .ant-btn-icon svg:not(.svg-fill) path,
          svg:not(.svg-fill) path {
            stroke: var(--white);
          }

          .ant-btn-icon svg.svg-fill path,
          svg.svg-fill path {
            fill: var(--white);
          }
        }
      }

      &:hover {
        .item__order {
          display: none;
        }
      }

      &:not(:hover) {
        .item__backdrop {
          display: none !important;
        }
      }
    }

    &:has(.sortable-ghost),
    .previewing {
      .item__backdrop {
        background-color: unset;

        img {
          display: none;
        }

        button {
          display: none;
        }
      }

      .item__order {
        display: flex !important;
      }
    }

    .images-content__upload {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      width: 100%;
      background-color: #ECF1F4CC;
      color: var(--primary-colours-blue-navy);
      aspect-ratio: 1/1;
    }
  }

  .limit-images-notice {
    opacity: 0.6;
    font-style: italic;
    font-size: 14px;

    &::before {
      content: "* ";
    }

    &.limit-images-error{
      color: var(--support-colours-red);
    }
  }

  .pdf-content-container {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .preview-pdf__remove-file {
      margin-left: auto;

      svg {
        width: 24px;
        height: 24px;
      }
    }

    .pdf-content {
      display: flex;
      flex-direction: column;
      border: 1px solid var(--lighttheme-content-background-stroke);
      padding: 24px;


      &.previewing {
        background: var(--lighttheme-content-background-stroke);
      }

      .pdf-content__upload {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8px;
        cursor: pointer;
        width: calc(20% - 19.2px);
        background-color: #ECF1F4CC;
        color: var(--primary-colours-blue-navy);
        aspect-ratio: 1/1;
      }

      .preview-pdf {
        display: flex;
        flex-direction: column;
        width: 100%;
        align-items: center;
        gap: 16px;


        .preview-pdf__preview {
          width: 100%;
          background: unset !important;

          position: relative;
          padding-top: 45%;

          .preview-pdf__preview-inner {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            display: flex;
            flex-direction: column;
            gap: 16px;
            justify-content: center;
            align-items: center;

            .preview-file__document {
              height: calc(100% - 20px - 16px);
              display: flex;
              justify-content: center;

              .react-pdf__Page {
                height: 100%;

                >* {
                  width: 100% !important;
                  height: 100% !important;
                }
              }

              .react-pdf__Page__textContent {
                display: none;
              }
            }

            .preview-file__page-select {
              display: flex;
              gap: 16px;
            }
          }
        }

        .preview-pdf__select-page {
          display: flex;
          flex-direction: column;
          gap: 8px;
          align-items: center;

          #form-select-page {
            display: flex;
            gap: 16px;
            align-items: center;

            .limit-pages-number {
              padding: 9px 48px;
              border-radius: 8px;
              background: var(--support-colours-grey-light);
              border: 1px solid var(--lighttheme-content-background-stroke)
            }

            .ant-form-item {
              margin: 0;

              .ant-col {
                flex: unset !important;
              }

              .ant-form-item-row {
                align-items: center;
              }

              .ant-form-item-label label {
                height: 40px;
                font-weight: 400;
              }

              .ant-input-number {
                width: 107px;
                box-shadow: none;
                border-radius: 8px;

                &:not(:hover) {
                  &:not(:focus-within) {
                    border: 1px solid var(--lighttheme-content-background-stroke);
                  }
                }

                input {
                  text-align: center;
                  height: 40px;
                }
              }
            }
          }

          .preview-pdf__error {
            display: flex;
            align-items: center;
            gap: 4px;
            color: var(--support-colours-yellow-dark);
          }
        }
      }
    }
  }
}

.modal-progress-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  justify-content: center;

  .progress {
    display: flex;
    gap: 8px;
    width: 100%;

    .ant-progress-inner {
      border-radius: 0 !important;
    }

    .process-error {
      .ant-progress-text {
        display: none;
      }
    }
  }
}