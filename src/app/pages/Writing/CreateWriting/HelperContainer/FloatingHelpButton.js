import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import './HelperContainer.scss';

const FloatingHelpButton = ({ toggleHelperCollapse, removeIcon, mode = 'help', onRemove }) => {
  const { t } = useTranslation();

  // Xác định nội dung button dựa trên mode
  const buttonText =
    mode === 'generate-idea' ? t('GENERATE_IDEA') :
    mode === 'find-vocab' ? t('FIND_VOCAB') :
    t('HELP_ME_WRITE');

  // Xử lý khi click vào icon remove
  const handleRemoveClick = (e) => {
    e.stopPropagation(); // Ngăn không cho sự kiện click lan truyền lên parent
    if (onRemove) {
      onRemove();
    }
  };

  return (
    <div
      className={`floating-help-button floating-help-button--${mode}`}
      onClick={toggleHelperCollapse}
    >
      <p className="floating-help-button__text">{buttonText}</p>
      <img
        src={removeIcon}
        alt={t('REMOVE')}
        className="remove-icon"
        onClick={handleRemoveClick}
      />
    </div>
  );
};

FloatingHelpButton.propTypes = {
  toggleHelperCollapse: PropTypes.func.isRequired,
  removeIcon: PropTypes.string.isRequired,
  mode: PropTypes.oneOf(['help', 'generate-idea', 'find-vocab']),
  onRemove: PropTypes.func,
};

export default FloatingHelpButton;
