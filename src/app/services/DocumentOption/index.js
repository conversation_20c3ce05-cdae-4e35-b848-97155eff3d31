import { API } from "@api";
import { createBase, deleteBase, getAllBase, getAllPaginationBase, getDetailBase, updateBase } from "@services/Base";


export function createDocumentOption(data) {
  return createBase(API.DOCUMENT_OPTION, data);
}

export function getAllDocumentOption(query) {
  return getAllBase(API.DOCUMENT_OPTION, query);
}

export function getPaginationDocumentOption(paging, query) {
  return getAllPaginationBase(API.DOCUMENT_OPTION, paging, query);
}

export function getDocumentOptionDetail(id) {
  return getDetailBase(API.DOCUMENT_OPTION_ID, id);
}

export function editDocumentOption(data) {
  return updateBase(API.DOCUMENT_OPTION_ID, data);
}

export function deleteDocumentOption(id) {
  return deleteBase(API.DOCUMENT_OPTION_ID, id);
}




