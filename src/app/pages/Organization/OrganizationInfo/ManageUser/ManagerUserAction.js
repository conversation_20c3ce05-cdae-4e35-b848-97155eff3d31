import React from "react";
import { Dropdown } from "antd";
import {useTranslation} from "react-i18next";
import Edit from "@component/SvgIcons/Edit";
import Trash from "@component/SvgIcons/Trash";
import clsx from "clsx";
import MoreVerticalIcon from "@component/SvgIcons/MoreVertical";

ManagerUserAction.propTypes = {};

function ManagerUserAction({ onEditAction, onDeleteAction, className, menuClassName, disableDelete }) {
  const {t} = useTranslation();
  return (
    <>
      <Dropdown
        className={className}
        menu={{
          items: [
            { key: "EDIT", label: t("EDIT"), icon: <Edit/>, onClick: onEditAction },
            ...disableDelete ? [] : [{ key: "DELETE", label: t("DELETE"), icon: <Trash/>, onClick: onDeleteAction }],
          ],
          className: clsx("action-dropdown-menu", menuClassName),
        }}
        trigger={["click"]}
        placement="rightTop"
      >
        <div className="more-vertical-btn">
          <MoreVerticalIcon/>
        </div>
      </Dropdown>
    </>
  );
}

export default ManagerUserAction;
