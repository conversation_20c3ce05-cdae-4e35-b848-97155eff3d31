import React, { useEffect } from "react";
import { Input } from "antd";

import { useProject } from "@app/pages/Project";
import { AntForm } from "@component/AntForm";
import TypingEffect from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TypingEffect";

import RULE from "@rule";

import "./PlaintextOutput.scss";

function PlaintextOutput({ responseSelected, isEditing, setEditing, ...props }) {
  const { handleSubmitResponse } = useProject();
  const { content, outputForm } = props;
  
  useEffect(() => {
    outputForm.setFieldsValue({ response: responseSelected?.plaintext });
  }, [responseSelected]);
  
  useEffect(() => {
    if (!isEditing) {
      outputForm.setFieldsValue({ response: responseSelected?.plaintext });
    }
  }, [isEditing]);
  
  function onFinishResponse(values) {
    handleSubmitResponse(content._id, { _id: responseSelected._id, output: { text: values.response } })
      .then(() => setEditing(false));
  }
  
  function renderForm() {
    if (!isEditing) return null;
    return <AntForm
      id={`form-response-${responseSelected._id}`}
      className="plaintext-response__output-form"
      form={outputForm}
      onFinish={onFinishResponse}
    >
      <AntForm.Item name="response" rules={[RULE.REQUIRED]}>
        <Input.TextArea className="plaintext-response__output-text" autoSize />
      </AntForm.Item>
    </AntForm>;
  }
  
  function renderValue() {
    if (isEditing) return null;
    const plaintextHtml = <div className="plaintext-response__output-text !p-0">
      {responseSelected?.plaintext}
    </div>;
    
    return responseSelected.typingEffect
      ? <TypingEffect html={plaintextHtml} />
      : plaintextHtml;
  }
  
  return <>
    {renderForm()}
    {renderValue()}
  </>;
}

export default PlaintextOutput;