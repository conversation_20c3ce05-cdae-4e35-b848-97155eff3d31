import React, { useEffect, useMemo, useRef, useState } from "react";
import { Form, Input, Select } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import { toast } from "@component/ToastProvider";
import { useProject } from "@app/pages/Project";

import { BUTTON, CONSTANT, PROJECT_TYPE } from "@constant";
import RULE from "@rule";

import { createTemplate } from "@services/Template";
import { getAllFolder } from "@services/Folder";

import AntButton from "@component/AntButton";
import PlusIcon from "@component/SvgIcons/PlusIcon";
import { AntForm } from "@src/app/component/AntForm";
import AntModal from "@src/app/component/AntModal";
import { cloneObj } from "@src/common/functionCommons";

function TemplateForm({ user, setTemplateList, onCaptureScreen }) {
  const { t } = useTranslation();
  const { projectId, projectData } = useProject();
  const templateNameRef = useRef();
  const [templateForm] = Form.useForm();

  const [isShowModal, setShowModal] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [gradeList, setGradeList] = useState([]);

  const projectType = projectData?.type || PROJECT_TYPE.NORMAL;
  const isLessonTemplate = projectType === PROJECT_TYPE.NORMAL;

  const locationList = useMemo(() => {
    const options = [{
      value: CONSTANT.PERSONAL,
      label: isLessonTemplate ? t("MY_LESSON_TEMPLATE") : t("MY_EXAM_TEMPLATE")
    }];
    if (user.isSystemAdmin) {
      options.push({
        value: CONSTANT.SYSTEM,
        label: isLessonTemplate ? t("CLICKEE_LESSON_TEMPLATE") : t("CLICKEE_EXAM_TEMPLATE")
      });
    }
    if (user.organizationId && [CONSTANT.ADMIN, CONSTANT.CONTRIBUTOR].includes(user.role)) {
      options.push({ value: CONSTANT.ORGANIZATION, label: user.organizationId.name });
    }
    return options;
  }, [user, isLessonTemplate, t]);

  useEffect(() => {
    if (!isLessonTemplate) getListFolder();
  }, [projectType]);

  useEffect(() => {
    if (isShowModal) {
      templateForm.resetFields();
      if (locationList.length === 1) templateForm.setFieldsValue({ type: locationList[0].value });
      setTimeout(() => {
        if (templateNameRef.current) templateNameRef.current.focus();
      }, 100);
    }
  }, [isShowModal, locationList]);

  useEffect(() => {
    if (isShowModal) {
      const dataSet = {
        field: isLessonTemplate ? CONSTANT.NORMAL : CONSTANT.EXAM,
        ...!isLessonTemplate ? { projectType: projectType } : {}
      }
      templateForm.setFieldsValue(dataSet);
    }
  }, [isShowModal, isLessonTemplate, projectType]);

  useEffect(() => {
    if (isShowModal && gradeList.length && !isLessonTemplate) {
      const defaultFolderId = gradeList.find(item => item.code === projectData?.examCode)?._id;
      templateForm.setFieldsValue({ folderId: defaultFolderId });
    }
  }, [isShowModal, gradeList, projectData]);

  const getListFolder = async () => {
    const query = {
      ownerId: user._id,
      type: projectType
    }
    const apiResponse = await getAllFolder(query);
    if (apiResponse) {
      const sorted = cloneObj(apiResponse)?.sort((a, b) => a?.folderName?.localeCompare(b?.folderName, undefined, { numeric: true }));
      setGradeList(sorted);
    }
  }

  function toggleModal() {
    setShowModal(prevState => !prevState);
  }

  async function onFinish(values) {
    setLoading(true);
    const dataRequest = cloneObj(values);
    dataRequest.projectId = projectId;
    delete dataRequest.field;
    delete dataRequest.folderId;
    if (dataRequest.type === CONSTANT.PERSONAL) {
      dataRequest.userId = user._id;
    } else if (dataRequest.type === CONSTANT.ORGANIZATION) {
      dataRequest.organizationId = user.organizationId._id;
    }

    if (!isLessonTemplate) {
      dataRequest.folderCode = gradeList.find(item => item?._id === values?.folderId)?.code;
      if (dataRequest.type == CONSTANT.PERSONAL) {
        dataRequest.folderId = values.folderId;
      }
    }
    toggleModal();
    const apiResponse = await createTemplate(dataRequest);
    if (apiResponse) {
      await onCaptureScreen(apiResponse._id);
      setTemplateList(prevState => [apiResponse, ...prevState]);
      toast.success("CREATE_TEMPLATE_SUCCESS");
      setLoading(false);
    } else {
      setLoading(false);
    }
  }
  const label = isLessonTemplate ? t("CREATE_LESSON_TEMPLATE") : t("CREATE_EXAM_TEMPLATE");

  return <div className="project-activity-item project-activity__template">
    <div className="project-activity-label">
      {label}
      <AntButton
        icon={<PlusIcon />}
        size="mini"
        type={BUTTON.WHITE_BLUE}
        onClick={toggleModal}
        loading={isLoading}
      />
    </div>

    <AntModal
      title={t("SAVE_TEMPLATE")}
      open={isShowModal}
      onCancel={toggleModal}
      formId={"template-form"}
      footerAlign={CONSTANT.CENTER}
    >
      <AntForm
        form={templateForm}
        layout="vertical"
        onFinish={onFinish}
        id="template-form"
      >
        <AntForm.Item name="name" rules={[RULE.REQUIRED]} label={t("TEMPLATE_NAME")} >
          <Input size="large" placeholder={t("ENTER_TEMPLATE_NAME")} ref={templateNameRef} autoComplete="off" />
        </AntForm.Item>
        <AntForm.Item name="type" rules={[RULE.REQUIRED]} label={t("TEMPLATE_STORAGE_LOCATION")}>
          <Select
            size="large"
            popupClassName="template-type-popup"
            placeholder={`${t("SELECT")} ${t("TEMPLATE_STORAGE_LOCATION").toLowerCase()}`}
            options={locationList}
          />
        </AntForm.Item>
        <AntForm.Item name="field" label={t("FIELD")}>
          <Select
            size="large"
            disabled
            popupClassName="template-type-popup"
            options={[
              { label: t("CREATE_NEW_PROJECT_LESSON"), value: CONSTANT.NORMAL },
              { label: t("CREATE_NEW_PROJECT_EXAM"), value: CONSTANT.EXAM }
            ]}
          />
        </AntForm.Item>
        {!isLessonTemplate && <>
          <AntForm.Item
            name="projectType"
            label={t("PROJECT_EXAM_TYPE")}>
            <Select
              size="large"
              disabled
              options={[
                { label: t("HIGH_SCHOOL_LEVEL_TEST"), value: PROJECT_TYPE.EXAM_SCHOOL },
                { label: t("IELTS_TEST"), value: PROJECT_TYPE.EXAM_IELTS }
              ]}
            />
          </AntForm.Item> <AntForm.Item
            name="folderId"
            rules={[RULE.REQUIRED]}
            label={t("LEVEL")}>
            <Select
              size="large"
              placeholder={t("SELECT_LEVEL")}
              options={gradeList.map(grade => ({ value: grade._id, label: t(grade.code?.toUpperCase()) || grade.folderName }))}
            />
          </AntForm.Item>
        </>}
      </AntForm>
    </AntModal>
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(TemplateForm);