import { useState } from "react";
import { Dropdown } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import clsx from "clsx";

import Share from "@component/Share";
import Move from "@component/Move";
import RenameDescription from "../RenameDescription";

import DownloadRotate from "@component/SvgIcons/DownloadRotate";
import MoreVerticalIcon from "@component/SvgIcons/MoreVertical";
import Copy from "@component/SvgIcons/Copy";
import ShareIcon from "@component/SvgIcons/ShareIcon";
import Edit from "@component/SvgIcons/Edit";
import Trash from "@component/SvgIcons/Trash";

import { copyProject, deleteProject, updateProject } from "@services/Project";
import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import * as workspaceRedux from "@src/ducks/workspace.duck";
import AntButton from "@component/AntButton";
import { BUTTON } from "@constant";

const ProjectActionDropdown = ({ user, availableWorkspaces, size = "xsmall", ...props }) => {
  
  
  const { projectData, className, menuClassName, iconActions } = props;
  const { handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterMove } = props;
  const workspaceId = projectData?.workspaceId?._id || projectData?.workspaceId;
  
  const { t } = useTranslation();
  
  const [isShowModalShare, setShowmodalShare] = useState(false);
  const [isShowModalMove, setIsShowModalMove] = useState(false);
  const [isShowModalRename, setShowmodalRename] = useState(false);
  
  const onCopyProjectAction = async (e) => {
    const dataRequest = { projectId: projectData._id };
    const apiResponse = await copyProject(dataRequest, true, { workspaceId });
    if (apiResponse) {
      const newWorkspaces = availableWorkspaces.map(workspace => {
        return (workspace?._id === apiResponse?.workspaceId ? {
          ...workspace,
          projects: workspace.projects + 1,
        } : workspace);
      });
      props.setAvailableWorkspaces(newWorkspaces);
      
      if (handleAfterCopy) {
        handleAfterCopy(apiResponse);
      }
      const toMyWorkspace = apiResponse?.workspaceId !== workspaceId;
      toast.success(toMyWorkspace ? "COPY_PROJECT_TO_MY_WORSKSPACE_SUCCESS" : "COPY_PROJECT_SUCCESS");
    }
  };
  
  const onDeleteProjectAction = () => {
    confirm.delete({
      content: t("CONFIRM_DELETE_PROJECT"),
      handleConfirm: async () => {
        const apiResponse = await deleteProject(projectData._id, true, { workspaceId });
        if (apiResponse) {
          if (handleAfterDelete) {
            handleAfterDelete(apiResponse);
          }
          const newWorkspaces = availableWorkspaces.map(workspace => {
            return (workspace?._id === projectData?.workspaceId ? {
              ...workspace,
              projects: workspace.projects - 1,
            } : workspace);
          });
          props.setAvailableWorkspaces(newWorkspaces);
          toast.success("DELETE_PROJECT_SUCCESS");
        }
      },
    });
  };
  
  const onShareProjectAction = (e) => {
    setShowmodalShare(prevState => !prevState);
  };
  
  const onRenameProjectAction = (e) => {
    setShowmodalRename(prevState => !prevState);
  };
  
  const renameProject = async (newProjectName) => {
    const dataRequest = { _id: projectData._id, projectName: newProjectName };
    const dataResponse = await updateProject(dataRequest, true, { workspaceId });
    if (dataResponse) {
      handleAfterRename(dataResponse);
      toast.success("UPDATE_PROJECT_SUCCESS");
      onRenameProjectAction();
    }
  };
  
  const onMoveProjectAction = (e) => {
    setIsShowModalMove(prevState => !prevState);
  };
  
  let items = [
    { key: "COPY", label: t("COPY"), icon: <Copy />, onClick: onCopyProjectAction },
    { key: "MOVE", label: t("MOVE"), icon: <DownloadRotate />, onClick: onMoveProjectAction },
    { key: "SHARE", label: t("SHARE"), icon: <ShareIcon />, onClick: onShareProjectAction },
    { key: "RENAME", label: t("RENAME"), icon: <Edit />, onClick: onRenameProjectAction },
    { key: "DELETE", label: t("DELETE"), icon: <Trash />, onClick: onDeleteProjectAction },
  ];
  
  return <><Dropdown
    className={className}
    menu={{
      items: items,
      className: clsx("action-dropdown-menu", menuClassName),
    }}
    trigger={["click"]}
  >
    <AntButton
      size={size}
      fullIcon
      type={BUTTON.GHOST_WHITE}
      icon={iconActions || <MoreVerticalIcon />}
      onClick={e => {
        e.preventDefault();
        e.stopPropagation();
      }}
    />
  </Dropdown>
    
    <Share
      isShowModal={isShowModalShare}
      handleCancel={onShareProjectAction}
      queryAccess={{ projectId: projectData._id }}
      name={projectData?.projectName}
      owner={projectData?.ownerId}
      workspaceId={workspaceId}
    />
    <Move
      isShowModal={isShowModalMove}
      handleCancel={onMoveProjectAction}
      projectId={projectData._id}
      handleAfterMove={handleAfterMove}
      workspaceId={workspaceId}
    />
    <RenameDescription
      isShowModal={isShowModalRename}
      initialValue={projectData?.projectName}
      id={projectData._id}
      handleClose={onRenameProjectAction}
      handleSubmit={renameProject}
      placeholder={t("PROJECT_NAME_PLACEHOLDER")}
      required
    />
  </>;
};

function mapStateToProps(store) {
  const { user } = store.auth;
  const { availableWorkspaces } = store.workspace;
  return { user, availableWorkspaces };
}

const mapDispatchToProps = {
  ...workspaceRedux.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(ProjectActionDropdown);
