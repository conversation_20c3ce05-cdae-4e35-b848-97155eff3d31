import React, { useEffect, useMemo, useState } from "react";
import { connect } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Dropdown } from "antd";

import { usePageViewTracker } from "@src/ga";
import { toast } from "@component/ToastProvider";

import SearchInput from "@component/SearchInput";
import AntButton from "@src/app/component/AntButton";
import FolderInfo from "./FolderInfo";
import NoData from "@component/NoData";
import Loading from "@component/Loading";
import NeedAccess from "@component/NeedAccess";
import FolderListTable from "./FolderListTable";
import FolderListGrid from "./FolderListGrid";

import { BUTTON, CONSTANT, LAYOUT_TYPE, PERMISSION, LANGUAGE } from "@constant";
import { LINK } from "@link";

import { getFolderDetail } from "@services/Folder";
import { createProject, getAllProjects } from "@services/Project";

import GRID_LAYOUT_ICON from "@src/asset/icon/button-newui/grid-layout-icon.svg";
import TABLE_LAYOUT_ICON from "@src/asset/icon/button-newui/table-layout-icon.svg";
import ADD_ICON_PRIMARY from "@src/asset/icon/button/add-icon-white.svg";

import * as app from "@src/ducks/app.duck";
import * as folderRedux from "@src/ducks/folder.duck";

import "./Folder.scss";
import ModalCreateNew from "@src/app/component/CreateNewModal";

function Folder({ theme, user, ...props }) {
  usePageViewTracker("Folder");
  const id = useParams()?.id;
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const [layoutType, setLayoutType] = useState(localStorage.getItem('folderViewMode') || LAYOUT_TYPE.TABLE);
  const [folderData, setFolderData] = useState({});
  const [projectsData, setProjectsData] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [searchValue, setSearchValue] = useState("");
  const [modalCreateState, setModalCreateState] = useState({
    open: false,
    createType: null
  });

  const permission = folderData?.permission;

  useEffect(() => {
    localStorage.setItem('folderViewMode', layoutType);
  }, [layoutType]);

  useEffect(() => {
    if (id) {
      getAllData();
    }
  }, [id]);
  const dataFolderFilter = useMemo(() => {
    return projectsData.filter((project) => {
      return project?.projectName?.toLowerCase().includes(searchValue.toLowerCase());
    });
  }, [projectsData, searchValue]);

  const getAllData = async () => {
    const allRequest = [
      getFolderDetail(id),
      getAllProjects({
        folderId: id,
        sort: "-updatedAt",
      }),
    ];
    const [folderResponse, ProjectsData] = await Promise.all(allRequest);
    if (folderResponse) {
      switch (folderResponse.code) {
        case 200:
          setFolderData(folderResponse.data);
          props.setBreadcrumb({ folder: folderResponse.data });
          break;
        case 403:
          setFolderData({ permission: PERMISSION.NO_PERMISSION });
          break;
        case 404:
          setFolderData({ permission: CONSTANT.NOT_FOUND });
          break;
      }
    }
    if (ProjectsData) {
      setProjectsData(ProjectsData);
    }
    setLoading(false);
  };

  const getProjectsData = async () => {
    const query = { folderId: id, sort: "-updatedAt" };
    const dataResponse = await getAllProjects(query);
    if (dataResponse) {
      setProjectsData(dataResponse);
    }
  };

  const handleChangeLayoutType = () =>
    setLayoutType(layoutType === LAYOUT_TYPE.TABLE ? LAYOUT_TYPE.GRID : LAYOUT_TYPE.TABLE);

  const handleAfterChangeProjectStar = (record) => {
    const newData = [...projectsData];
    const index = newData.findIndex((item) => item._id === record._id);
    if (index !== -1) {
      newData[index].isSaved = !newData[index].isSaved;
      setProjectsData(newData);
    }
  };

  async function onCreateProject(data) {
    const projectParams = {
      ...data,
      folderId: id,
      workspaceId: folderData?.workspaceId?._id || folderData?.workspaceId
    };
    const apiResponse = await createProject(projectParams);
    if (apiResponse) {
      navigate(LINK.PROJECT_DETAIL.format(apiResponse._id));
      const messageLang = modalCreateState.createType === CONSTANT.PROJECT_LESSON ? "CREATE_PROJECT_LESSON_SUCCESS" : "CREATE_PROJECT_EXAM_SUCCESS";
      toast.success(messageLang);
      toggleModalCreateNew(null);
    }
  }

  const toggleModalCreateNew = (type) => {
    setModalCreateState(pre => ({ open: !pre.open, createType: type }));
  };

  const handleAfterCopyProject = (project) => {
    if (project?.folderId?._id === folderData?._id) {
      setProjectsData([project, ...projectsData]);
    }
  };

  const handleAfterDeleteProject = (data) => {
    const newProjects = projectsData.filter((project) => project._id !== data._id);
    setProjectsData(newProjects);
  };

  const handleAfterChangeFolderStar = (isSaved) => {
    setFolderData({ ...folderData, isSaved: isSaved });
  };

  const handleAfterRenameProject = (data) => {
    const newData = projectsData.map((project) => {
      if (project._id === data._id) {
        return { ...data };
      }
      return { ...project };
    });
    setProjectsData(newData);
  };

  const allowCreate = [PERMISSION.EDITOR, PERMISSION.OWNER].includes(permission);

  if (isLoading) {
    return <Loading active transparent />;
  }

  if (!permission) {
    return <></>;
  }
  if (permission === PERMISSION.NO_PERMISSION) {
    return <NeedAccess />;
  }
  if (permission === CONSTANT.NOT_FOUND) {
    return <NoData>{t("FOLDER_NOT_FOUND")}</NoData>;
  }
  const setFolderNameSearch = (e) => {
    setSearchValue(e.target.value);
  };
  const clearFolderNameSearch = () => {
    setSearchValue("");
  };

  const onCreateProjectExam = () => {
    navigate(LINK.CREATE_EXAM, { state: { folderId: id } });
  }
  return (
    <>
      <div className="folder-page">
        <div className="folder-page__content">
          <div className="content__filter">
            <div className="filter__actions">
              {!!projectsData?.length && (
                <SearchInput
                  size={"large"}
                  placeholder={t("SEARCH")}
                  className={"filter__actions__search"}
                  onChange={setFolderNameSearch}
                  onClear={clearFolderNameSearch}
                  value={searchValue}
                />
              )}
            </div>
            <div className={"filter__action-right"}>
              <div>
                {allowCreate && (
                  <Dropdown
                    menu={{
                      items: [
                        { key: "1", label: t("CREATE_PROJECT_LESSON"), onClick: () => toggleModalCreateNew(CONSTANT.PROJECT_LESSON) },
                        { key: "2", label: t("CREATE_PROJECT_EXAM"), onClick: onCreateProjectExam },
                        { key: "3", label: t("CREATE_PROJECT_GRADING"), onClick: () => toggleModalCreateNew(CONSTANT.PROJECT_GRADING_ASSIGNMENT) }
                      ],
                      className: "create-new-menu",
                    }}
                    trigger={["click"]}
                    placement="bottomRight"
                  >
                    <AntButton
                      type={BUTTON.DEEP_NAVY}
                      size="large"
                      className="actions__add-button"
                    >
                      {t("CREATE_PROJECT")}
                      <img src={ADD_ICON_PRIMARY} />
                    </AntButton>
                  </Dropdown>

                )}
              </div>
              <div>
                <AntButton
                  type={BUTTON.DEEP_NAVY}
                  className="filter__layout-button"
                  onClick={handleChangeLayoutType}
                  size="large"
                >
                  <img
                    src={layoutType === LAYOUT_TYPE.TABLE ? GRID_LAYOUT_ICON : TABLE_LAYOUT_ICON}
                    alt=""
                    className="filter__layout-button__img"
                  />
                </AntButton>
              </div>
            </div>
          </div>
          {!!projectsData.length && !dataFolderFilter.length && <NoData searchNoData={true}>{t("NO_DATA")}</NoData>}
          {projectsData && !projectsData?.length && (
            <div className="no-data-folder">
              <NoData>
                <span>
                  {i18n.language === LANGUAGE.EN
                    ? <>{t("USE_THE")} <b>"{t("CREATE_PROJECT")}"</b> {t("BUTTON")}</>
                    : <>{t("USE_THE")} {t("BUTTON")} <b>"{t("CREATE_PROJECT")}"</b></>}
                </span>
              </NoData>
            </div>
          )}
          {!!projectsData.length && !!dataFolderFilter.length && (
            <>
              {layoutType === LAYOUT_TYPE.TABLE ? (
                <FolderListTable
                  projectsData={dataFolderFilter}
                  handleAfterMove={handleAfterDeleteProject}
                  handleAfterDelete={handleAfterDeleteProject}
                  handleAfterCopy={handleAfterCopyProject}
                  handleAfterRename={handleAfterRenameProject}
                  handleAfterChangeStar={handleAfterChangeProjectStar}
                />
              ) : (
                <FolderListGrid
                  projectsData={dataFolderFilter}
                  handleAfterMove={handleAfterDeleteProject}
                  handleAfterDelete={handleAfterDeleteProject}
                  handleAfterCopy={handleAfterCopyProject}
                  handleAfterRename={handleAfterRenameProject}
                  handleAfterChangeStar={handleAfterChangeProjectStar}
                />
              )}
            </>
          )}
        </div>

        <FolderInfo
          folderData={folderData}
          setFolderData={setFolderData}
          getProjectsData={getProjectsData}
          changeStar={handleAfterChangeFolderStar}
          setBreadcrumb={props.setBreadcrumb}
        />
      </div>
      <ModalCreateNew
        {...modalCreateState}
        onCancel={() => toggleModalCreateNew(null)}
        onSubmit={onCreateProject} />
    </>
  );
}

function mapStateToProps(store) {
  const { theme } = store.app;
  const { user } = store.auth;
  return { theme, user };
}

const mapDispatchToProps = {
  ...app.actions,
  ...folderRedux.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(Folder);
