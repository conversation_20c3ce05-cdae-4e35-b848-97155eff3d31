import React, { useEffect, useState } from "react";
import { Col, Form, Input, Row, Select } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import AntButton from "@component/AntButton";
import CancelIcon from "@component/SvgIcons/CancelIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";
import CustomModal from "@component/CustomModal";

import { cloneObj, coverLangArrayToObject, coverLanguageObjectToArray } from "@common/functionCommons";

import { BUTTON, INPUT_TYPE, LANG_OPTIONS, RULES } from "@constant";

import "./ModalOptionDetail.scss";


const ModalOptionDetail = ({ ...props }) => {
  const { t } = useTranslation();
  const { isShowModal, optionData, knowledgeData } = props;
  const [formOption] = Form.useForm();
  const [isSelectType, setSelectType] = useState(false);
  const [nameLanguage, setNameLanguage] = useState([]);
  
  useEffect(() => {
    if (optionData) {
      if (["select", "select_multiple"].includes(optionData.type)) {
        setSelectType(true);
      }
      let repareLocalization = coverLanguageObjectToArray(optionData?.localization);
      delete repareLocalization?.lang;
      formOption.setFieldsValue({ ...optionData, localization: repareLocalization });
    } else setSelectType(false);
  }, [optionData]);
  
  const onFinish = async (values) => {
    const { selectOptions, type, localization, rule, ...rest } = values;
    const data = {
      ...rest,
      type,
      rule: !!rule ? rule : null,
      ...(optionData && { _id: optionData._id }),
      selectOptions: ["select", "select_multiple"].includes(type) ? selectOptions : [],
      localization: coverLangArrayToObject(localization),
    };
    await props.handleOk(data);
    formOption.resetFields();
  };
  
  const handleCancel = () => {
    formOption.resetFields();
    props.handleCancel();
  };
  
  const onChangeType = (value) => setSelectType(["select", "select_multiple"].includes(value));
  
  const getAvailabeOptions = (index, options) => {
    const optionsSelected = options.filter((lang, optionIndex) => optionIndex !== index);
    return LANG_OPTIONS.filter((option) => !optionsSelected.some((selected) => selected.value === option.value));
  };
  
  const onRemove = (index, name, remove, setData) => {
    remove(index, name);
    setData(prevData => prevData.filter((_, i) => i !== index));
  };
  
  const onSelect = (data, index, setData) => {
    const { value } = data;
    setData((pre) => {
      const newData = cloneObj(pre);
      newData[index] = value;
      return newData;
    });
  };
  
  
  const renderFormItem = (restField, name, label, rules, placeholder, component) => (
    <Form.Item
      {...restField}
      name={name}
      label={label}
      rules={rules}
    >
      {component}
    </Form.Item>
  );
  
  const renderSelect = (index, name, restField, setNameLanguage) => (
    renderFormItem(
      restField,
      [name, "lang"],
      index === 0 ? "Language" : "",
      [{ required: true, message: "Missing language of name!" }],
      "Select language",
      <Select
        options={getAvailabeOptions(index, nameLanguage)}
        placeholder="Select language"
        onSelect={(_, option) => onSelect(option, index, setNameLanguage)}
      />,
    )
  );
  
  const renderInput = (index, name, restField) => (
    renderFormItem(
      restField,
      [name, "name"],
      index === 0 ? "Name" : "",
      [{ required: true, message: "Missing name!" }],
      "Enter name",
      <Input placeholder="Enter name"/>,
    )
  );
  
  
  return (
    <CustomModal
      isShowModal={isShowModal}
      closeIcon
      handleCancel={handleCancel}
      className="option-modal"
      form="option-form"
      footerAlign="center"
      width={800}
      okText={optionData ? t("UPDATE") : t("CREATE")}
      title={optionData ? "Option detail" : "Create Option"}
    
    >
      <div className="option-modal__content">
        <div className="option-modal__body">
          <Form id="option-form" onFinish={onFinish} layout="vertical" size={"large"} form={formOption}>
            <Row gutter={24}>
              <Col xs={24} lg={12}>
                <Form.Item label="Name" name="name">
                  <Input placeholder={"Enter name"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item label="Code" name="code" rules={[{ required: true, message: "Code can't be blank!" }]}>
                  <Input placeholder={"Enter code"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item label="Placeholder" name="placeholder">
                  <Input placeholder={"Enter placeholder"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item label="Default value" name="defaultValue">
                  <Input placeholder={"Enter default value"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={24}>
                <Form.Item label="Knowledge" name="knowledgeIds">
                  <Select
                    options={knowledgeData}
                    mode="multiple"
                    placeholder={"Select knowledge data"}
                    allowClear
                    fieldNames={{ label: "name", value: "_id" }}
                    optionFilterProp="name"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item label="Type" name="type" rules={[{ required: true, message: "Type can't be blank!" }]}>
                  <Select options={INPUT_TYPE} onChange={onChangeType} placeholder={"Select type"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item label="Rule" name="rule">
                  <Select options={RULES} allowClear placeholder={"Select rules"}/>
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item label="Instruction" name="instruction">
                  <Input.TextArea
                    autoSize={{
                      minRows: 1,
                    }}
                    placeholder={"Input instruction"}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item required className="localization">
              <Form.List name="localization">
                {(fields, { add, remove }) => {
                  if (fields.length === 0) add();
                  return (
                    <div className={"instruction-localization-container"}>
                      {fields.map(({ key, name, ...restField }, index) => (
                        <div key={index}>
                          <div className={"instruction-localization-item"}>
                            
                            <Row gutter={24} key={key} className="items-baseline">
                              <Col xs={24} lg={6}>
                                {renderSelect(index, name, restField, setNameLanguage)}
                              </Col>
                              <Col xs={24} lg={18}>
                                {renderInput(index, name, restField)}
                              </Col>
                            </Row>
                            
                            <div className={"select-options-item__btnRemove"}>
                              <AntButton
                                type={BUTTON.WHITE}
                                shape={"circle"}
                                className={clsx("btn-cancel-add-options-detail", { "first-actions": !index })}
                                icon={<CancelIcon/>}
                                size={"small"}
                                onClick={() => onRemove(index, name, remove, setNameLanguage)}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                      
                      {fields.length < 2 && (
                        <div className={"add-options-actions"}>
                          <AntButton
                            shape={"circle"}
                            size={"large"}
                            type={BUTTON.WHITE_BLUE}
                            onClick={() => add()}
                            icon={<PlusIcon/>}
                          ></AntButton>
                        </div>
                      )}
                    </div>
                  );
                }}
              </Form.List>
            </Form.Item>
            {isSelectType && (
              <Form.List name="selectOptions" initialValue={[{ label: null }]}>
                {(fields, { add, remove }) => (
                  <>
                    <div className="select-options-item">
                      <label>Select options:</label>
                    </div>
                    {fields.map(({ key, name, ...restField }) => (
                      <div className={"select-options-item"} key={key}>
                        <Row gutter={24}>
                          <Col xs={24} lg={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "label"]}
                              rules={[
                                {
                                  required: true,
                                  message: "Label can't be blank!",
                                },
                              ]}
                            >
                              <Input placeholder="Label"/>
                            </Form.Item>
                          </Col>
                          <Col xs={24} lg={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "value"]}
                              rules={[
                                {
                                  required: true,
                                  message: "Value can't be blank!",
                                },
                              ]}
                            >
                              <Input placeholder="Value"/>
                            </Form.Item>
                          </Col>
                        </Row>
                        <div className={"select-options-item__btnRemove"}>
                          <AntButton
                            type={BUTTON.WHITE}
                            shape={"circle"}
                            className={"btn-cancel-add-options"}
                            icon={<CancelIcon/>}
                            size={"small"}
                            onClick={() => remove(name)}
                          />
                        </div>
                      </div>
                    ))}
                    
                    <div className={"add-options-actions"}>
                      <AntButton
                        shape={"circle"}
                        size={"large"}
                        type={BUTTON.WHITE_BLUE}
                        onClick={() => add()}
                        icon={<PlusIcon/>}
                      ></AntButton>
                    </div>
                  </>
                )}
              </Form.List>
            )}
          </Form>
        </div>
      </div>
    </CustomModal>
  );
};

export default ModalOptionDetail;
