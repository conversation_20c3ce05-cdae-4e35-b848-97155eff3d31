import AntModal from "@component/AntModal";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Form, Input, Switch } from "antd";

import RULE from "@rule";

import "./DocumentOptionDetail.scss";

function DocumentOptionDetail({ isOpen, handleCancel, optionSelected, ...props }) {
  const { t } = useTranslation();

  const [formOptionDetail] = Form.useForm();

  const [isImageField, setImageField] = useState(false);

  useEffect(() => {
    if (isOpen) {
      formOptionDetail.resetFields();
      if (optionSelected) {
        setImageField(optionSelected.isImage);
        formOptionDetail.setFieldsValue(optionSelected);
      }
    } else {
      setImageField(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (isImageField) {
      formOptionDetail.setFieldsValue({ fieldName: "avatarId" });
    }
  }, [isImageField]);

  function onFinish(values) {
    props.handleSaveDocumentOption({ ...values, isImage: isImageField });
  }

  return <>
    <AntModal
      title={t(optionSelected ? "UPDATE_DOCUMENT_OPTION" : "ADD_DOCUMENT_OPTION")}
      onCancel={handleCancel}
      open={isOpen}
      formId="option-form"
      okText={t(optionSelected ? "UPDATE" : "ADD")}
      width={600}
    >
      <Form
        id="option-form"
        layout="vertical"
        form={formOptionDetail}
        onFinish={onFinish}
        className="document-option-detail-form"
      >
        <Form.Item layout="horizontal" label={t("IMAGE")} className="switch-field">
          <Switch checked={isImageField} onChange={setImageField} size="default" />
          <span className="switch-label">{isImageField ? t("YES") : t("NO")}</span>
        </Form.Item>
        <Form.Item label={t("FIELD_NAME")} name="fieldName" rules={[RULE.REQUIRED]}>
          <Input disabled={isImageField} placeholder={t("ENTER_FIELD_NAME")} size="large" />
        </Form.Item>
        <Form.Item label={t("FIELD_LABEL")} name="fieldLabel" rules={[RULE.REQUIRED]}>
          <Input placeholder={t("ENTER_FIELD_LABEL")} size="large" />
        </Form.Item>
      </Form>
    </AntModal>
  </>;
}

export default DocumentOptionDetail;