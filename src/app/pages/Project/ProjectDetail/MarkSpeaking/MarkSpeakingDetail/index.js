import React, { useContext, useEffect, useMemo, useState } from "react";
import { Form, Input, Popover, Switch } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import { AntForm } from "@component/AntForm";
import DynamicForm from "@component/DynamicForm";
import Loading from "@component/Loading";
import AudioRecorder from "@component/AudioRecorder";
import SpeakingAudio from "@app/pages/Project/ProjectDetail/MarkSpeaking/MarkSpeakingDetail/SpeakingAudio";
import SpeakingScoreChart from "@component/SpeakingScoreChart";

import { cloneObj } from "@common/functionCommons";
import { convertPascalCaseToCamelCase } from "@common/dataConverter";

import RULE from "@rule";
import { RECOGNIZE_STATUS } from "@constant";

import "./MarkSpeakingDetail.scss";


const ERROR_INIT = {
  mispronunciation: {
    title: "Mispronunciations",
    count: 0,
    backgroundColor: "rgb(255, 204, 0)",
    color: "#000",
    show: true,
  },
  unexpectedBreak: {
    title: "Unexpected break",
    count: 0,
    backgroundColor: "rgb(240, 201, 203)",
    color: "#000",
    show: true,
  },
  missingBreak: {
    title: "Missing break",
    count: 0,
    backgroundColor: "rgb(212, 212, 212)",
    color: "#000",
    show: true,
  },
  monotone: { title: "Monotone", count: 0, backgroundColor: "rgb(134, 46, 181)", color: "#FFF", show: true },
};
export const MarkSpeakingContext = React.createContext();

function MarkSpeakingDetail({ inputData, resultData, instructionOptions, ...props }) {
  
  const { t } = useTranslation();
  
  
  const [formSpeakingRecorder] = Form.useForm();
  const [results, setResults] = useState([]);
  const [textRecognizing, setTextRecognizing] = useState(null);
  const [recognizeStatus, setRecognizeStatus] = useState(RECOGNIZE_STATUS.NOT_STARTED);
  const [openAiResult, setOpenAiResult] = useState({
    html: null,
    isWaiting: false,
  });
  
  const [wordAnalyzed, setWordAnalyzed] = useState([]);
  const [errors, setErrors] = useState(cloneObj(ERROR_INIT));
  const [audioFileId, setAudioFileId] = useState(null);
  const [audioUrlDownload, setAudioUrlDownload] = useState(null);
  
  const [isMarkingSpeaking, setMarkingSpeaking] = useState(false);
  
  useEffect(() => {
    formSpeakingRecorder.setFieldsValue(inputData);
  }, [inputData]);
  
  
  useEffect(() => {
    if (resultData?.state === "done") {
      setResults(convertPascalCaseToCamelCase(resultData.output?.results || []));
      setOpenAiResult({ html: resultData.output?.html, isWaiting: false });
      setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
      setAudioUrlDownload(resultData.output?.audioUrl);
      setAudioFileId(resultData.output?.fileId);
    }
    
  }, [resultData]);
  
  function calculateAverageScores(data) {
    const scoreTotals = {
      pronunciationAssessment: {},
      contentAssessment: {},
    };
    
    data.forEach(item => {
      item.nBest.forEach(nBestItem => {
        Object.entries(nBestItem).forEach(([assessmentType, assessment]) => {
          if (scoreTotals[assessmentType]) {
            Object.entries(assessment).forEach(([key, value]) => {
              if (key.toLowerCase().includes("score")) {
                if (!scoreTotals[assessmentType][key]) {
                  scoreTotals[assessmentType][key] = { total: 0, count: 0 };
                }
                scoreTotals[assessmentType][key].total += value;
                scoreTotals[assessmentType][key].count += 1;
              }
            });
          }
        });
      });
    });
    
    const averages = {};
    Object.entries(scoreTotals).forEach(([assessmentType, scores]) => {
      averages[assessmentType] = {};
      Object.entries(scores).forEach(([scoreType, { total, count }]) => {
        averages[assessmentType][scoreType] = Math.round(total / count);
      });
    });
    delete averages?.pronunciationAssessment?.completenessScore;
    delete averages?.pronunciationAssessment?.pronScore;
    
    averages.pronunciationOverall = calAvg(averages?.pronunciationAssessment);
    averages.contentOverall = calAvg(averages?.contentAssessment);
    
    return averages;
  }
  
  
  function calAvg(scores) {
    const values = Object.values(scores);
    const total = values.reduce((sum, score) => sum + score, 0);
    return Math.ceil(total / values.length);
  }
  
  const scoreAvg = useMemo(() => calculateAverageScores(results), [results]);
  
  
  const convertToMonotoneGroupedArray = (arr) => {
    const result = [];
    let group = [];
    
    arr.forEach((item) => {
      if (item.errors?.monotone) {
        group.push(item);
      } else {
        if (group.length > 0) {
          result.push({ monotone: true, groups: group });
          group = [];
        }
        result.push(item);
      }
    });
    
    if (group.length > 0) {
      result.push({ monotone: true, groups: group });
    }
    
    return result;
  };
  
  useEffect(() => {
    const errorTemp = cloneObj(ERROR_INIT);
    
    const words = results
      .flatMap(item => item.nBest.flatMap(nBestItem => nBestItem.words))
      .filter(item => !!item?.word)
      .map(word => {
        const { isUnexpectedBreak, isMissingBreak, isMonotone, isMispronunciation } = findErrors(word);
        if (isUnexpectedBreak) errorTemp.unexpectedBreak.count += 1;
        if (isMissingBreak) errorTemp.missingBreak.count += 1;
        if (isMonotone) errorTemp.monotone.count += 1;
        if (isMispronunciation) errorTemp.mispronunciation.count += 1;
        
        word.errors = {
          unexpectedBreak: isUnexpectedBreak,
          missingBreak: isMissingBreak,
          monotone: isMonotone,
          mispronunciation: isMispronunciation,
        };
        
        return word;
      });
    
    const analyzedList = convertToMonotoneGroupedArray(words);
    
    setWordAnalyzed(analyzedList);
    setErrors(errorTemp);
    
  }, [results]);
  
  function findErrors(word) {
    const breakErrors = word?.pronunciationAssessment?.feedback?.prosody?.break?.errorTypes;
    const intonationErrors = word?.pronunciationAssessment?.feedback?.prosody?.intonation?.errorTypes;
    const isUnexpectedBreak = breakErrors?.includes("UnexpectedBreak");
    const isMissingBreak = breakErrors?.includes("MissingBreak");
    const isMonotone = intonationErrors?.includes("Monotone");
    const isMispronunciation = word.pronunciationAssessment?.accuracyScore < 60;
    
    return {
      isUnexpectedBreak,
      isMissingBreak,
      isMonotone,
      isMispronunciation,
    };
  }
  
  function handleChangeShowError(errorKey, checked) {
    setErrors(prevState => {
      const newState = cloneObj(prevState);
      newState[errorKey].show = checked;
      return newState;
    });
  }
  
  function renderRecognize() {
    
    function renderOneWord(word, key) {
      const { unexpectedBreak, missingBreak, mispronunciation, monotone } = word.errors;
      
      return <React.Fragment key={word?.word + key}>
        
        {unexpectedBreak && errors.unexpectedBreak?.show && <span className="transcript__unexpected-break">
          [&nbsp;&nbsp;]
        </span>}
        
        {missingBreak && errors.missingBreak?.show && <span className="transcript__missing-break">
          [&nbsp;&nbsp;]
        </span>}
        
        <Popover
          arrow={false}
          getPopupContainer={() => document.getElementById("js-transcript-content")}
          title={<span>
            {`${word.word}: ${word.pronunciationAssessment?.accuracyScore}`}
          </span>}
          content={<div className="word-phonemes">
            {word.phonemes.map((phoneme, index) => {
              return <div key={index} className="word-phonemes__phoneme">
                <span>{phoneme.phoneme}</span>
                <span>{phoneme.pronunciationAssessment?.accuracyScore}</span>
              </div>;
            })}
          </div>}
          trigger="hover"
          mouseEnterDelay={0}
          mouseLeaveDelay={0}
          destroyTooltipOnHide={true}
        >
          <span
            className={clsx("transcript__word", { "transcript__word-monotone": monotone && errors.monotone?.show })}>
            <span className={clsx({
              "transcript__word-mispronunciation": mispronunciation && errors.mispronunciation?.show,
            })}>
              {`${word?.word}${word?.punctuation || ""}`}
            </span>
          </span>
        </Popover>
      </React.Fragment>;
    }
    
    if (recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING || recognizeStatus === RECOGNIZE_STATUS.COMPLETE) {
      return <>
        {wordAnalyzed?.map((word, index) => {
          if (word.monotone) {
            return word.groups.map((wordItem, wordItemIndex) => {
              return renderOneWord(wordItem, index + wordItemIndex);
            });
            //todo: handle monotone
            return <span key={index} className="transcript__monotone">
              {/*<span className="transcript__monotone-bg" />*/}
              {word.groups.map((wordItem, wordItemIndex) => {
                return renderOneWord(wordItem, index + wordItemIndex);
              })}
            </span>;
          } else {
            return renderOneWord(word, index);
          }
        })}
        {textRecognizing?.split(" ")?.map((text, index) => {
          return <span key={index} className="transcript__word">
          <span>{text}</span>
        </span>;
        })}
      </>;
    }
  }
  
  return <MarkSpeakingContext.Provider value={{
    projectId: props.projectId,
    instructionId: props.instructionId,
    isAllowEditing: props.isAllowEditing,
    
    formSpeakingRecorder,
    results, setResults,
    textRecognizing, setTextRecognizing,
    recognizeStatus, setRecognizeStatus,
    setOpenAiResult,
    setErrors,
    audioFileId, setAudioFileId,
    audioUrlDownload, setAudioUrlDownload,
    
    isMarkingSpeaking, setMarkingSpeaking,
  }}>
    <div className="mark-speaking-container">
      
      <div className="speaking-recorder-container">
        
        <AntForm
          layout="vertical"
          form={formSpeakingRecorder}
          className="speaking-recorder__form"
          name="form-submit-exam"
          disabled={!props.isAllowEditing}
        >
          {/*<AntForm.Item label={t("TOPIC")} name="topic" rules={[RULE.REQUIRED]}>
           <Input.TextArea autoSize={{ minRows: 1, maxRows: 5 }} />
           </AntForm.Item>*/}
          
          <DynamicForm
            formFields={instructionOptions}
          />
        </AntForm>
        
        <div className="speaking-recorder__description">
          {t("PREPARE_AND_START_RECORDING")}
        </div>
        
        <AudioRecorder />
      </div>
      
      
      <div className="speaking-assessment-result">
        {t("ASSESSMENT_RESULT")}
      </div>
      <div className="speaking-response">
        <div className="speaking-response__left">
          <SpeakingAudio
          />
          
          <div className="speaking-response__transcript">
            <div id="js-transcript-content" className="speaking-response__transcript-content">
              {renderRecognize()}
            </div>
          </div>
        </div>
        <div className="speaking-response__error">
          <div className="speaking-response__error-title">
            {t("ERROR")}
          </div>
          <div className="speaking-response__error-content">
            
            {Object.entries(errors).map(([key, error]) => {
              return <div key={key} className="speaking-response__error-item">
                <div
                  className="speaking-error__count"
                  style={{ backgroundColor: error.backgroundColor, color: error.color }}>
                  {error.count}
                </div>
                <div className="speaking-error__title">
                  {error.title}
                </div>
                <div className="speaking-error__switch">
                  <Switch
                    checked={error.show}
                    onChange={(checked) => handleChangeShowError(key, checked)}
                    disabled={isMarkingSpeaking}
                  />
                  <label className="speaking-error__switch-text">
                    {error.show ? "On" : "Off"}
                  </label>
                </div>
              </div>;
            })}
          
          
          </div>
        </div>
      </div>
      
      {(!!scoreAvg.pronunciationOverall || !!scoreAvg.contentOverall) && <div className="speaking-score">
        
        {!!scoreAvg.pronunciationOverall && <SpeakingScoreChart
          overallLabel="Pronunciation score"
          overallScore={scoreAvg.pronunciationOverall}
          scoreBreakdown={scoreAvg.pronunciationAssessment}
        />}
        
        {recognizeStatus === RECOGNIZE_STATUS.COMPLETE && <SpeakingScoreChart
          overallLabel="Content score"
          overallScore={scoreAvg.contentOverall}
          scoreBreakdown={scoreAvg.contentAssessment}
        />}
      </div>}
      
      {recognizeStatus === RECOGNIZE_STATUS.COMPLETE &&
        <Loading active={openAiResult.isWaiting} className="open-ai-result">
          {!openAiResult.isWaiting && <div dangerouslySetInnerHTML={{ __html: openAiResult.html }} />}
        </Loading>}
    
    </div>
  </MarkSpeakingContext.Provider>;
}

const useMarkSpeaking = () => useContext(MarkSpeakingContext);
export { MarkSpeakingDetail, useMarkSpeaking };