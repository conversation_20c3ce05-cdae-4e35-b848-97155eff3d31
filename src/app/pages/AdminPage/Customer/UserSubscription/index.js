import React, { useEffect, useMemo, useState } from "react";
import { Col, Form, Row, Select, Card, Input } from "antd";
import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { SearchOutlined } from "@ant-design/icons";

import { formatDateTime, handleSearchParams, paginationConfig } from "@common/functionCommons";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import TableAdmin from "@src/app/component/TableAdmin";

import { getAllUserSubscription } from "@services/UserSubscription";
import { getAllUser } from "@services/User";

import { BUTTON, PAGINATION_INIT } from "@constant";

import "./UserSubscription.scss";
import { LINK } from "@link";

UserSubscription.propTypes = {
  setIsLoading: PropTypes.func
};

function UserSubscription(props) {
  const { t, i18n } = useTranslation();
  const { setIsLoading } = props;
  const [formSearchUser] = Form.useForm();
  const [dataUserPackage, setDataUserPackage] = useState(PAGINATION_INIT);
  const [dataUser, setDataUser] = useState([]);
  const location = useLocation();
  const navigate = useNavigate();
  const { query, paging } = handleSearchParams(location.search);

  useEffect(() => {
    getAPIAllUser();
    getUserPackage(paging, query);
  }, [location.search]);


  const pagination = paginationConfig(dataUserPackage.paging, dataUserPackage.query, i18n.language);

  const getAPIAllUser = async () => {
    setIsLoading(true);
    const apiResponse = await getAllUser();
    if (apiResponse) {
      setDataUser(apiResponse);
    }
    setIsLoading(false);
  };

  const getUserPackage = async (paging = dataUserPackage.paging, query = dataUserPackage.query) => {
    setIsLoading(true);
    formSearchUser.setFieldsValue({
      email: query?.email,
      name: query?.email,
    });
    const apiResponse = await getAllUserSubscription();
    if (apiResponse) {
      // Giả lập dữ liệu phân trang - trong thực tế cần API hỗ trợ phân trang
      const filteredData = !query?.email ? apiResponse : apiResponse.filter(record => record.email === query?.email);
      setDataUserPackage({
        rows: filteredData,
        paging: paging,
        query: query
      });
    }
    setIsLoading(false);
  };

  const onChangeSelect = (user) => {
    formSearchUser.setFieldsValue({
      email: user,
      name: user,
    });
  };

  const submitFormFilter = (formData) => {
    const newQuery = {};
    if (formData?.email) {
      newQuery.email = formData.email;
    }
    navigate(`?${new URLSearchParams(newQuery).toString()}`);
  };


  const clearFormFilter = () => {
    navigate("");
    formSearchUser.resetFields();
  };

  const handleChangeTable = (pagination, filters, sorter) => {
    const { current, pageSize } = pagination;
    const params = new URLSearchParams(location.search);
    params.set('page', current);
    params.set('pageSize', pageSize);
    navigate(`?${params.toString()}`);
  };

  const orderColumn = (paging) => ({
    title: t("ORDER"),
    key: "order",
    width: 80,
    align: "center",
    render: (_, __, index) => (
      <span>{(paging.page - 1) * paging.pageSize + index + 1}</span>
    ),
  });

  const columns = [
    orderColumn(dataUserPackage.paging),
    {
      title: t("FULL_NAME"),
      dataIndex: "fullName",
      key: "fullName",
      width: 250,
      render: (text) => <span className="user-name-value">{text}</span>,
    },
    {
      title: t("EMAIL"),
      dataIndex: "email",
      key: "email",
      width: 300,
    },
    {
      title: t("PACKAGE"),
      dataIndex: ["subscription", "packageId", "name"],
      key: "packageName",
      align: "center",
      width: 200,
      render: (text) => <span className="package-name-value">{text}</span>,
    },
    {
      title: t("START_DATE_NOT_COLON"),
      dataIndex: ["subscription", "startDate"],
      key: "startDate",
      align: "center",
      render: formatDateTime,
      width: 200,
    },
    {
      title: t("END_DATE_NOT_COLON"),
      dataIndex: ["subscription", "endDate"],
      key: "endDate",
      align: "center",
      render: formatDateTime,
      width: 200,
    },
    {
      title: t("ACTION"),
      key: "paymentHistory",
      render: (_, record) => (
        <div className="user-subscription-actions">
          <Link to={LINK.ADMIN_PAYMENT_HISTORY_ID.format(record?._id)} state={record}>
            {t("PAYMENT_HISTORY")}
          </Link>
        </div>
      ),
      align: 'center',
      width: 200
    }
  ];

  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());

  return (
    <div className="user-subscription-container">
      <Card className="user-subscription-search-card">
        <AntForm form={formSearchUser} size={"large"} className="form-filter" onFinish={submitFormFilter}>
          <Row gutter={16} align="middle" justify="space-between">
            <Col xs={24} md={12} lg={16}>
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <AntForm.Item name={"name"} className="search-form-item">
                    <Select
                      showSearch
                      options={dataUser.map((res) => ({ label: res?.fullName, value: res?.email }))}
                      placeholder={t("SELECT_BY_FULL_NAME")}
                      onChange={onChangeSelect}
                      allowClear
                      filterOption={filterOption}
                    />
                  </AntForm.Item>
                </Col>
                <Col xs={24} md={12}>
                  <AntForm.Item name={"email"} className="search-form-item">
                    <Select
                      showSearch
                      options={dataUser.map((res) => ({ label: res?.email, value: res?.email }))}
                      placeholder={t("SELECT_BY_EMAIL")}
                      onChange={onChangeSelect}
                      allowClear
                      filterOption={filterOption}
                    />
                  </AntForm.Item>
                </Col>
              </Row>
            </Col>
            <Col xs={24} md={12} lg={8} className="search-buttons-col">
              <div className="search-buttons">
                <AntButton size={"large"} type={BUTTON.GHOST_WHITE} onClick={clearFormFilter}>
                  {t("CLEAR")}
                </AntButton>
                <AntButton size={"large"} htmlType={"submit"} type={BUTTON.DEEP_NAVY}>
                  {t("SEARCH")}
                </AntButton>
              </div>
            </Col>
          </Row>
        </AntForm>
      </Card>

      <Card className="user-subscription-table-card">
        <TableAdmin
          columns={columns}
          dataSource={dataUserPackage.rows}
          scroll={{ x: 1000 }}
          rowKey="_id"
          pagination={pagination}
          onChange={handleChangeTable}
          className="user-subscription-table"
          rowClassName={() => "user-subscription-table-row"}
          locale={{ emptyText: t("NO_USER_SUBSCRIPTION_FOUND") }}
        />
      </Card>
    </div>
  );
}

export default UserSubscription;