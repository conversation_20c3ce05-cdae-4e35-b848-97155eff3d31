import React from "react";
import { Empty, Button } from "antd";
import { InboxOutlined, FilterOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";

const EmptyState = ({ type = "noData", onAction, actionText }) => {
  const { t } = useTranslation();

  const configs = {
    noData: {
      icon: <InboxOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />,
      title: t("NO_STATISTICS_DATA"),
      description: t("NO_STATISTICS_DATA_DESCRIPTION"),
      actionText: actionText || t("REFRESH_DATA")
    },
    noFilters: {
      icon: <FilterOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />,
      title: t("NO_FILTER_RESULTS"),
      description: t("TRY_DIFFERENT_FILTERS"),
      actionText: actionText || t("CLEAR_FILTERS")
    },
    noStatistics: {
      icon: <InboxOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />,
      title: t("NO_STATISTICS_AVAILABLE"),
      description: t("SELECT_CAMPAIGN_AND_DATE_RANGE"),
      actionText: actionText || t("REFRESH_DATA")
    }
  };

  const config = configs[type] || configs.noData;

  return (
    <div className="empty-statistics">
      <Empty
        image={config.icon}
        description={
          <div>
            <h3>{config.title}</h3>
            <p>{config.description}</p>
          </div>
        }
      >
        {onAction && (
          <Button type="primary" onClick={onAction}>
            {config.actionText}
          </Button>
        )}
      </Empty>
    </div>
  );
};

export default EmptyState;
