.custom-language-selector {
  position: relative;
  display: inline-flex; // Hoặc block tùy theo layout
  align-items: center;

  &.disabled {
    .custom-language-selector__trigger,
    .custom-language-selector__button {
      cursor: not-allowed;
      opacity: 1;
    }
  }

  &__trigger {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    gap: 16px;
    // border: 1px solid #dbdbdb;
    box-shadow: 0px 4px 32px rgba(0, 0, 0, 0.08);
    padding: 12px 16px;
    border-radius: 16px;
  }

  &__label {
    font-size: 14px;
    color: #09196b; // Màu chữ cho label "Output Language"
    font-weight: 500;
    line-height: 24px;
  }

  &__button {
    background-color: #36a6ff; // Màu nền giống AntButton type DEEP_BLUE
    color: white;
    border: none;
    padding: 2px 8px;
    border-radius: 8px; // Bo góc giống AntButton
    gap: 4px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: 12px;
    line-height: 16px;
    font-weight: 500;
    transition: background-color 0.3s ease;

    &:hover:not(:disabled) {
      background-color: darken(#009ef7, 10%); // Làm tối màu khi hover
    }

    &:disabled {
      background-color: #d9d9d9; // Màu khi disabled
      color: #fff;
      cursor: not-allowed;
    }

    span {
      margin-right: 0px;
    }
  }

  &__chevron {
    transition: transform 0.2s ease-in-out;
    width: 16px;
    height: 16px;
    path {
      fill: currentColor; // Chevron sẽ có màu giống text của button
    }
    &.open {
      transform: rotate(180deg);
    }
  }

  &__dropdown-menu {
    position: absolute;
    top: calc(100% + 5px); // Hiển thị ngay dưới trigger, có khoảng cách nhỏ
    right: 0; // Căn phải
    background-color: white;
    border: 1px solid #e8e8e8; // Border giống Dropdown của Antd
    border-radius: 12px; // Bo góc giống Dropdown của Antd
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05); // Shadow giống Antd
    list-style: none;
    padding: 0; // Padding giống Antd
    margin: 0;
    z-index: 1050;
    min-width: 160px; // Độ rộng tối thiểu
    overflow: hidden;
  }

  &__item {
    display: flex;
    align-items: center;
    padding: 12px 16px; // Padding giống Antd menu item
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #09196B;
    gap: 16px;

    &:hover {
      background-color: #e7e5ff; // Màu hover giống Antd
    }

    &.selected {
      background-color: #09196B; // Màu selected giống Antd
      font-weight: 600; // Chữ đậm hơn khi selected
      color: #fff;
    }
  }

  &__item-flag {
    width: 24px;
    height: 24px;
    border-radius: 2px;
  }

  &__item-lang {
    // Style cho text ngôn ngữ nếu cần
  }
}
