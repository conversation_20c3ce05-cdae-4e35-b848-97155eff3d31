@mixin generate-nested-first-items($depth, $prefix: '*') {
  @for $i from 1 through $depth {
    > *:first-child {
      margin-top: 0;

      @if $i < $depth {
        @include generate-nested-first-items($depth - 1);
      }
    }
  }
}

@mixin generate-nested-last-items($depth, $prefix: '*') {
  @for $i from 1 through $depth {
    > *:last-child {
      margin-bottom: 0;

      @if $i < $depth {
        @include generate-nested-last-items($depth - 1);
      }
    }
  }
}

.html-output__value {
  @include generate-nested-first-items(6);
  @include generate-nested-last-items(6);

  padding: 24px;
  background: var(--primary-colours-blue-navy-light-1);
  border-radius: 8px;
  width: 100%;

  * {
    text-wrap: wrap;
  }
}