import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { useSpeaking } from "@app/pages/Student/Speaking";
import AntButton from "@component/AntButton";
import Speaking<PERSON><PERSON> from "@app/pages/Student/Speaking/SpeakingChart";

import { BUTTON, CONSTANT } from "@constant";
import { cleanFileName, downloadUsingBrowser, getFileExtension, randomKey } from "@common/functionCommons";
import useWindowDimensions from "@common/windowDimensions";

import "./SpeakingFeedback.scss";
import { extractKeys } from "@common/dataConverter";
import { EVALUATION_LIST, EVALUATION_OBJECT } from "@app/pages/Student/Speaking/SpeakingFeedback/evaluationCommon";
import ArrowDown from "@component/SvgIcons/ArrowDown";
import { createSpeakingReport } from "@services/Report";
import { API } from "@api";
import FeedbackPopopver from "@src/app/component/FeedbackPopover";
import { actions, paramsCreators, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";
import { useDispatch } from "react-redux";

function SpeakingFeedback() {
  const { t, i18n } = useTranslation();
  const { width, height } = useWindowDimensions();
  
  const jsSpeakingFeedback = useRef(null);
  const jsSpeakingFeedbackHeader = useRef(null);
  const jsSpeakingFeedbackBody = useRef(null);
  
  const { projectData, responseData, feedbackData } = useSpeaking();
  
  const [evaluationSelected, setEvaluationSelected] = useState(CONSTANT.ALL);
  
  const dispatch = useDispatch();
  
  const evaluationData = useMemo(() => {
    
    if (Array.isArray(feedbackData?.evaluations)) {
      return feedbackData.evaluations.map((evaluation, index) => {
        return {
          ...evaluation,
          key: randomKey(),
          color: EVALUATION_LIST[index].color,
          icon: EVALUATION_LIST[index].icon,
        };
      });
    }
    return Object.entries(EVALUATION_OBJECT).map(([key, value]) => {
      return {
        key,
        color: value.color,
        icon: value.icon,
        sectionName: t(value.lang),
        feedback: feedbackData[key],
      };
    });
  }, [feedbackData?.evaluations, i18n.language]);
  
  /*useEffect(() => {
   jsSpeakingFeedback.current ||= document.getElementById("js-speaking-feedback");
   jsSpeakingFeedbackHeader.current ||= document.getElementById("js-speaking-feedback__header");
   jsSpeakingFeedbackBody.current ||= document.getElementById("js-speaking-feedback__body");
   
   const headerHeight = jsSpeakingFeedbackHeader.current.offsetHeight;
   
   jsSpeakingFeedbackBody.current.style.height = height - headerHeight - 210 + "px";
   }, [width, height, feedbackData]);*/
  async function handleDownloadFeedback() {
    
    const apiResponse = await createSpeakingReport({ responseId: responseData._id });
    if (apiResponse) {
      const displayName = cleanFileName(projectData.projectName || "Untitled speech") + "." + getFileExtension(apiResponse.fileName);
      downloadUsingBrowser(API.DOWNLOAD_REPORT_FILE.format(apiResponse.fileName, displayName));
    }
    dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_DOWNLOAD, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name)));
  }
  
  
  return <div id="js-speaking-feedback" className="speaking-feedback">
    <div id="js-speaking-feedback__header" className="speaking-feedback__header">
      <AntButton
        type={BUTTON.LIGHT_NAVY}
        active={evaluationSelected === CONSTANT.ALL}
        onClick={() => {
          setEvaluationSelected(CONSTANT.ALL);
          dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_TAB, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name, CONSTANT.ALL)));
        }}
      >
        {t("ALL")}
      </AntButton>
      {evaluationData.map((evaluation) => {
        return <AntButton
          key={evaluation.key}
          type={BUTTON.LIGHT_NAVY}
          active={evaluationSelected === evaluation.key}
          onClick={() => {
            setEvaluationSelected(evaluation.key);
            dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_TAB, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name, evaluation?.sectionName)));
          }}
        >
          {evaluation.sectionName}
        </AntButton>;
      })}
    </div>
    <div id="js-speaking-feedback__body" className="speaking-feedback__body">
      <div className="speaking-feedback__body-content">
        <div className="speaking-feedback__score">
          <div className="speaking-feedback__band-score">
            <div className="speaking-feedback-band-score__score">
              {`${feedbackData?.score || 0}/9`}
            </div>
            <div className="speaking-feedback-band-score__label">
              {t("IELTS_BAND_SCORE")}
            </div>
          </div>
          
          <SpeakingChart/>
        </div>
        <div className="speaking-feedback__evaluations">
          {evaluationData
            .filter(evalItem => {
              if (evaluationSelected === CONSTANT.ALL) return true;
              return evalItem.key === evaluationSelected;
            }).map(evaluation => {
              return <div key={evaluation.sectionName} className="speaking-feedback-evaluation">
                <div className="speaking-feedback-evaluation__name" style={{ color: evaluation.color }}>
                  <div className="speaking-feedback-evaluation__icon">
                    <img src={evaluation.icon} alt=""/>
                  </div>
                  {evaluation.sectionName}
                </div>
                <div
                  className="speaking-feedback-evaluation__feedback"
                  dangerouslySetInnerHTML={{ __html: evaluation.feedback }}
                />
              </div>;
            })}
        </div>
        <div className="speaking-feedback__action">
          <AntButton
            size="mini"
            icon={<ArrowDown/>}
            type={BUTTON.WHITE_COBALT}
            iconLocation={CONSTANT.RIGHT}
            onClick={handleDownloadFeedback}
          >
            {t("DOWNLOAD")}
          </AntButton>
          <FeedbackPopopver toolId={responseData?.inputId?.toolId} projectId={projectData?._id}/>
        </div>
      </div>
    </div>
  </div>;
}

export default SpeakingFeedback;