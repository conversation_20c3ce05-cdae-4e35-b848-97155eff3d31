import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { connect } from "react-redux";

import AntButton from "@component/AntButton";
import IncentivePage from "./IncentivePage";
import PriceList from "./PriceList";

import { getAllFeature, getAllPackage } from "@services/Package";

import { BUTTON, CONSTANT } from "@constant";

import "./Package.scss";

function PackagePage({ user }) {
  const [activeTabs, setActiveTabs] = useState(CONSTANT.YEAR);
  const { t, i18n } = useTranslation();
  const [dataPackages, setDataPackages] = useState([]);
  const [features, setFeatures] = useState([]);
  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    const query = { customerTarget: user?.type || "teacher" };
    const dataResponse = await getAllPackage(query);
    if (dataResponse) {
      dataResponse.sort((a, b) => a.order - b.order);
      setDataPackages(dataResponse);
    }

    const features = await getAllFeature();
    if (features) setFeatures(features);
  };

  return (
    <div className="package-container">
      <div className="package-header">
        <span className="package-header__title">{t("UPGRADE_FOR_MORE_TOOL")}</span>
        <div className="package-header__info">
          <div>
            <span>
              {t("AGREE_CLICKEE_TEARM")}{" "}
              <Link
                to={
                  i18n.language === "vi"
                    ? "https://clickee.ai/dieu-khoan-su-dung/"
                    : "https://clickee.ai/en/term-of-service/"
                }
                target="_blank"
                className="package-header__terms"
              >
                {t("TEARM_OF_SERVICE_CLICKEE")}.
              </Link>
            </span>
          </div>
          <div>
            <span>
              {t("NOTE")}:{" "}
              <Link
                to={
                  i18n.language === "vi"
                    ? "https://clickee.ai/privacy-policy/"
                    : "https://clickee.ai/en/privacy-policy-2/"
                }
                target="_blank"
                className="package-header__terms"
              >
                {t("CLICKEE_PRIVACE_POLICY")}{" "}
              </Link>
              {t("DESCIRIBLE_HANDLE_SERVICE")}.
            </span>
          </div>
        </div>
        <div className="package-header__timePackage">
          <AntButton
            type={activeTabs === CONSTANT.MONTH ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
            onClick={() => setActiveTabs(CONSTANT.MONTH)}
          >
            {t("MONTHY")}
          </AntButton>
          <AntButton
            type={activeTabs === CONSTANT.YEAR ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
            onClick={() => setActiveTabs(CONSTANT.YEAR)}
          >
            <span>{t("ANNUAL")}</span>
            <span className="package-header__typo-year">{t("SAVE_MONEY")} 20%</span>
          </AntButton>
          <AntButton
            type={activeTabs === CONSTANT.INCENTIVE ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
            onClick={() => setActiveTabs(CONSTANT.INCENTIVE)}
          >
            <span>{t("INCENTIVE_PACKAGE")}</span>
            <span className="package-header__typo-package">4 {t("PACKS")}</span>
          </AntButton>
        </div>
      </div>
      {activeTabs === CONSTANT.INCENTIVE ? (
        <div className="grid-incentive-content">
          <IncentivePage />
        </div>
      ) : (
        <div className="package-content">
          <PriceList unitPrice={activeTabs} dataPackages={dataPackages} features={features} />
        </div>
      )}
    </div>
  );
}

const mapStateToProps = (state) => ({
  user: state.auth.user,
});
const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(PackagePage);
