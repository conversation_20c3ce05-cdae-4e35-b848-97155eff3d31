import { API } from "@api";
import axios from "axios";

import {
  createBase,
  deleteBase,
  getAllBase,
  updateBase,
  getDetailBase,
  getAllPaginationBase,
  getAllManager,
  joinParams,
  getAllPaginationBaseList
} from "@services/Base";
import { genPopulateParam, genQueryParam } from "@src/common/functionCommons";
import { convertSnakeCaseToCamelCase } from "@src/common/dataConverter";

export const getAllWithoutPaginationSpeakingExercise = async (
  query,
  paging = {},
  populateOpts = [],
  loading = true
) => {
  const finalPaging = { ...paging, pageSize: 1000 };
  return getAllPaginationBaseList(
    API.SPEAKING_EXERCISES_ALLPUBLISHED,
    finalPaging,
    query,
    [],
    populateOpts,
    loading
  );
};

export const createSession = async (data) => {
  return createBase(API.SPEAKING_SESSIONS, data, [], true, true, {});
}

export async function getSessionDetail(id, populateOpts = [], loading = true) {
  const config = { loading };
  const populateParams = genPopulateParam(populateOpts);
  return axios.get(API.SESSIONS_DETAIL.format(id) + "?" + populateParams, config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch(err => {
      console.log("err", err);
      return err.response?.data;
    });
}

export async function submitCompleteSession(sessionId, data) {
  return axios.put(API.SUBMIT_COMPLETE.format(sessionId), data)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return err.response?.data;
    });
}

export async function getResultsByStudent(paging, query, requestConfig = {}) {
  const currentPage = paging?.page || 1;
  const pageSize = paging?.pageSize || paging?.limit || 0;

  const arrParams = [
    `page=${currentPage}`,
    `pageSize=${pageSize}`,
    genQueryParam(query),
  ];

  return axios.get(`/api/spksessions/completed?${joinParams(arrParams)}`, requestConfig)
    .then(response => {
      if (response.status === 200 && Array.isArray(response?.data?.rows)) {
        if (pageSize) {
          return convertSnakeCaseToCamelCase(response.data);
        } else if (Array.isArray(response.data.rows)) {
          return convertSnakeCaseToCamelCase(response.data.rows);
        } else {
          return [];
        }
      }
      return null;
    })
    .catch((err) => {
      return null;
    });
}
export const getAllSessions = (query = {}, paging = { page: 1, limit: 12 }, populateOpts = [], loading = true) => {
  return getAllPaginationBaseList(API.SESSION_COMPLETED, paging, query, populateOpts, loading);
};

export function deleteSession(id, toastError = true, params) {
  return deleteBase(API.SPEAKING_SESSIONS_ID, id, false, toastError, params);
}

export const allPublishedSessions = async () => {
  return axios.get(API.SPEAKING_EXERCISES_ALLPUBLISHED)
    .then(response => {
      if (response.status === 200) return response?.data?.rows;
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return err.response?.data;
    });
}

export function changeQuestion(id, data) {
  return updateBase(API.SESSIONS_UPDATE.format(id), data, [], false, true, {})
}

export const shareSpeakingSession = async (sessionId, permissionsArray) => {
  // Tạo đối tượng body đúng với cấu trúc yêu cầu
  const requestBody = {
    sessionId: sessionId,
    data: permissionsArray
  };

  try {
    const response = await axios.post(API.SHARE_SESSION, requestBody);

    // Kiểm tra nếu request thành công (status codes 2xx)
    if (response.status >= 200 && response.status < 300) {
      return response.data?.rows || response.data; // Hoặc chỉ response.data tùy thuộc vào API
    } else {
      // Xử lý các trường hợp thành công nhưng không phải 2xx nếu có
      console.log("Request successful but with status: ", response.status);
      return null; // Hoặc trả về response.data nếu API vẫn trả về dữ liệu có ích
    }
  } catch (err) {
    console.error("Error sharing session:", err);
    // Trả về dữ liệu lỗi từ server nếu có, giúp phía client xử lý tốt hơn
    if (err.response) {
      return err.response.data;
    }
    // Trả về lỗi chung nếu không có response từ server (ví dụ: lỗi mạng)
    return { error: true, message: err.message || "An unknown error occurred" };
  }
};

export function getSessionsPermission(id, loading = true, toastError = false) {
  const config = { loading, toastError };
  return axios.get(API.SESSIONS_PERMISSION.format(id) + "?", config)
              .then(response => {
                if (response.status === 200) return { code: 200, permission: response?.data };
                return null;
              })
              .catch(err => {
                console.log("err", err);
                return { code: err.response?.status, permission: null };
              });
}

