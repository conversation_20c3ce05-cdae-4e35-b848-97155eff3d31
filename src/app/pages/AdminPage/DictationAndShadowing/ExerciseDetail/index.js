import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button, Collapse, Form, Input, InputNumber, Modal, Select } from "antd";

import { toast } from "@component/ToastProvider";

import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";

import RULE from "@rule";

import {
  createExercise,
  getExerciseDetail,
  textToAudio,
  updateExercise,
  uploadExerciseAudio,
} from "@services/DictationAndShadowing";

import "./ExerciseDetail.scss";

import SegmentDisplay from "../SegmentDisplay";
import AudioTranscriptEditor from "../AudioTranscriptEditor";
import { DIFFICULTY_OPTIONS, EXERCISE_STATUS_OPTIONS, EXERCISE_TYPE_OPTIONS } from "@constant";
import { LINK } from "@link";
import VoiceAndSpeedSelector from "@app/pages/AdminPage/DictationAndShadowing/VoiceAndSpeedSelector";
import UploadImagePreview from "@component/UploadImagePreview";
import { uploadFile } from "@services/File";

function ExerciseDetail() {
  const { t } = useTranslation();
  const exerciseId = useParams().id;

  const [formExercise] = Form.useForm();
  const navigate = useNavigate();
  const [isLoadingInfo, setLoadingInfo] = useState(false);
  const [isLoadingUpload, setLoadingUpload] = useState(false);

  const [exerciseData, setExerciseData] = useState({});
  const [audioId, setAudioId] = useState(null);

  const [isVoiceModalVisible, setIsVoiceModalVisible] = useState(false); // State quản lý hiển thị Modal
  const [selectedVoiceForCreation, setSelectedVoiceForCreation] = useState("alloy"); // State cho giọng đọc đã chọn
  const [selectedSpeedForCreation, setSelectedSpeedForCreation] = useState(1); // State cho tốc độ đã chọn (mặc định 1x)

  const [avatarId, setAvatarId] = useState(null);
  const [isLoadingAvatar, setLoadingAvatar] = useState(false);


  useEffect(() => {
    if (exerciseId) {
      getDocumentTemplateData();
    }
  }, [exerciseId]);

  async function getDocumentTemplateData() {
    const apiResponse = await getExerciseDetail(exerciseId);
    if (apiResponse) {
      setExerciseData(apiResponse);
      setAudioId(apiResponse.audioId);
      setAvatarId(apiResponse.avatarId);
      formExercise.setFieldsValue(apiResponse);
    }
  }

  function validateRequestData(formData) {
    const warnings = [
      { condition: !formData.name, message: "NAME_IS_REQUIRED" },
      { condition: !formData.tag, message: "TAG_IS_REQUIRED" },
      { condition: !exerciseData.audioId, message: "AUDIO_IS_REQUIRED" },
      { condition: !exerciseData.transcript, message: "TRANSCRIPT_IS_REQUIRED" },
    ];

    for (const { condition, message } of warnings) {
      if (condition) {
        toast.warning({ description: t(message) });
        return false;
      }
    }

    return true;
  }

  async function handleSave() {
    try {
      // const formData = formExercise.getFieldsValue();
      // validateRequestData(formData);
      // setLoadingInfo(true);

      const formData = await formExercise.validateFields();
      if (!validateRequestData(formData)) return;

      setLoadingInfo(true);

      const apiRequest = {
        ...formData,
        difficulty: formData.difficulty || exerciseData.difficulty,
        status: formData.status || exerciseData.status,
        type: formData.type || exerciseData.type,
        audioId,
        avatarId,
        transcript: exerciseData.transcript,
        segments: exerciseData.segments,
        _id: exerciseId || undefined,
      };

      const apiResponse = exerciseId
        ? await updateExercise(apiRequest)
        : await createExercise(apiRequest);
     
      if (apiResponse) {
        !exerciseId && navigate(LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT_ID.format(apiResponse._id));
        const successMessage = exerciseId ? "UPDATE_SUCCESS" : "CREATE_SUCCESS";
        toast.success(successMessage, { replace: true });
      }
    } catch (error) {
      toast.warning({ description: t('TOAST_ERROR_SAVE_DATA') });
      console.error("Error during save operation:", error);
    } finally {
      setLoadingInfo(false);
    }
  }


  async function onDropAudio({ file }) {
    setLoadingUpload(true);

    if (file.status === "uploading") return;

    setLoadingUpload(false);

    if (file.status === "done") {
      const { audioId, text: transcript, segments, duration: audioDuration } = file.response;
      setExerciseData(prevData => ({
        ...prevData,
        audioId,
        transcript,
        segments,
        audioDuration,
      }));
      setAudioId(audioId);
      return toast.success("UPLOAD_SUCCESS", { replace: true });
    }

    if (file.status === "error") {
      return toast.error(file.response.message, { replace: true });
    }
  }

  const handleCreateNewAudio = () => {
    setIsVoiceModalVisible(true); // Mở Modal khi click "Tạo mới Audio"
  };

  const handleVoiceModalCancel = () => {
    setIsVoiceModalVisible(false); // Đóng Modal khi click "Cancel" trong Modal
  };
  const handleVoiceModalOk = async () => {
    setIsVoiceModalVisible(false);
    setLoadingInfo(true);

    try {
      const { text: transcript, audioId, duration: audioDuration, segments } = await textToAudio({
        text: exerciseData.transcript,
        voice: selectedVoiceForCreation,
        speed: selectedSpeedForCreation,
      });

      setExerciseData(prevData => ({
        ...prevData,
        audioDuration,
        audioId,
        transcript,
        segments,
      }));
      setAudioId(audioId);
    } catch (error) {
      console.error("Error in handleVoiceModalOk:", error);
    } finally {
      setLoadingInfo(false);
    }
  };


  async function updateSegmentsFromTranscript() {
    const { segments: currentSegments, transcript } = exerciseData;
    const sentences = transcript.split(/[.\n]/).map(sentence => sentence.trim()).filter(Boolean);

    const newSegments = currentSegments.map((segment, index) => ({
      ...segment,
      text: sentences[index] || segment.text,
    }));

    setExerciseData({
      ...exerciseData,
      segments: newSegments,
    });

    toast.success("Segments đã được cập nhật từ transcript!", { replace: true });
  }

  const handleTranscriptChange = (e) => {
    setExerciseData({
      ...exerciseData,
      transcript: e.target.value,
    });
  };
  const handleSelectWord = (word, index) => {
    const newSegments = [...exerciseData.segments];
    newSegments[index].hiddenWord = word;
    setExerciseData({
      ...exerciseData,
      segments: newSegments,
    });
  };

  const handleVoiceChangeFromSelector = (voiceName) => {
    setSelectedVoiceForCreation(voiceName);
  };

  const handleSpeedChangeFromSelector = (speed) => {
    setSelectedSpeedForCreation(speed);
  };

  const onChange = (key) => {
    console.log(key);
  };

  async function handleUploadImage(file) {
    setLoadingAvatar(true);
    const apiResponse = await uploadFile(file, { folder: "image" });
    if (apiResponse) {
      setAvatarId(apiResponse._id);
      formExercise.setFieldsValue({ avatarId: apiResponse._id });
    }
    setLoadingAvatar(false);
  }

  async function handleClearImage() {
    setAvatarId(null);
    formExercise.resetFields(["avatarId"]);
  }

  const handleSegmentTextUpdate = (segmentId, newText, index) => {
    const updatedSegments = [...exerciseData.segments];
    // Sử dụng index để cập nhật segment thay vì dựa vào _id
    updatedSegments[index] = { ...updatedSegments[index], text: newText };

    const newTranscriptText = updatedSegments.map(seg => seg.text).join(" ");
    setExerciseData({
      ...exerciseData,
      segments: updatedSegments,
      transcript: newTranscriptText,
    });
  };
  // console.log("exerciseData", exerciseData);
  return <>
    <div className="document-template-detail-container">
      <div className="document-template-detail__header">
        {t("EXERCISE_INFO")}
      </div>


      <Loading active={isLoadingInfo || isLoadingUpload} className="document-template-detail__section">
        <AntForm
          form={formExercise}
          layout="vertical"
          requiredMark={true}
          className="form-document-template-detail"
        >
          <AntForm.Item label={t("NAME")} name="name" rules={[RULE.REQUIRED]}>
            <Input placeholder={t("ENTER_NAME")}/>
          </AntForm.Item>

          <AntForm.Item label={t("TAG")} name="tag" rules={[RULE.REQUIRED]}>
            <Input placeholder={t("ENTER_TAG")}/>
          </AntForm.Item>

          <AntForm.Item label={t("TIME_LIMIT")} name="timeLimit">
            <InputNumber placeholder={t("ENTER_TIME_LIMIT")}/>
          </AntForm.Item>

          <AntForm.Item label={t("DIFFICULTY")} name="difficulty" rules={[RULE.REQUIRED]}>
            <Select
              options={DIFFICULTY_OPTIONS}
              placeholder={t("SELECT_DIFFICULTY")}
            />
          </AntForm.Item>

          <AntForm.Item label={t("TYPE")} name="type" rules={[RULE.REQUIRED]}>
            <Select
              placeholder={t("SELECT_TYPE")}
              options={EXERCISE_TYPE_OPTIONS}
            />
          </AntForm.Item>

          <AntForm.Item label={t("STATUS")} name="status" rules={[RULE.REQUIRED]}>
            <Select
              placeholder={t("SELECT_STATUS")}
              options={EXERCISE_STATUS_OPTIONS}
            />
          </AntForm.Item>

          <AntForm.Item label={t("IMAGE")}>
            <UploadImagePreview
              loading={isLoadingAvatar}
              onDrop={handleUploadImage}
              onClear={handleClearImage}
              imageId={avatarId}
            />
          </AntForm.Item>

          <AntForm.Item hidden name="avatarId">
            <Input readOnly/>
          </AntForm.Item>

        </AntForm>

        <AudioTranscriptEditor
          audioId={audioId}
          exerciseData={exerciseData}
          setExerciseData={setExerciseData}
          onDropAudio={onDropAudio}
          handleCreateNewAudio={handleCreateNewAudio}
          updateSegmentsFromTranscript={updateSegmentsFromTranscript}
          handleTranscriptChange={handleTranscriptChange}
        />

        {exerciseData.segments &&
          <Collapse
            items={[
              {
                key: "1",
                label: "Segments display",
                children: exerciseData.segments.map((segment, index) => {
                  return (
                    <SegmentDisplay
                      key={segment._id}
                      segment={segment}
                      index={index}
                      onSegmentTextChange={handleSegmentTextUpdate}
                      handleSelectWord={handleSelectWord}
                    />
                  );
                }),
              },
            ]}
            onChange={onChange}
          />}

        <div className="button-wrapper">
          <Button type="primary" onClick={handleSave}>
            {t("SAVE_DATA")}
          </Button>
        </div>
      </Loading>

      <Modal
        width={1200}
        title="Chọn giọng đọc và tốc độ"
        open={isVoiceModalVisible}
        onOk={handleVoiceModalOk}
        onCancel={handleVoiceModalCancel}
      >
        <VoiceAndSpeedSelector
          onVoiceChange={handleVoiceChangeFromSelector}
          onSpeedChange={handleSpeedChangeFromSelector}
        />
      </Modal>

    </div>

  </>;
}

export default ExerciseDetail;