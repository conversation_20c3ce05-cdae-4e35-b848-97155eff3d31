import { useEffect, useRef, useState } from "react";
import {
  Grid,
  WindowScroller,
  AutoSizer,
} from "react-virtualized";

import { useWindowSize } from "../useWindowSize";
import { useWorkspace } from "..";
import GridItem from "./GridItem";
import "./GridLayout.scss";
import Loading from "@src/app/component/Loading";

const GRID_HEIGHT = 117.5;
const GRID_MIN_WIDTH = 300;

function GridLayout() {
  const { dataSource } = useWorkspace();

  const containerRef = useRef(null);
  const gridRef = useRef(null);
  const windowSize = useWindowSize();
  const [isRendering, setIsRendering] = useState(true);

  const scrollElement = document.getElementsByClassName("workspace-content")[0];
  const isScrolling = scrollElement?.scrollHeight > scrollElement?.clientHeight;
  const containerWidth = containerRef.current?.clientWidth + (isScrolling ? 16 : 24);

  useEffect(() => {
    gridRef.current?.recomputeGridSize();
  }, [windowSize, dataSource]);

  function calculateColumnCount(width) {
    return Math.floor(width / GRID_MIN_WIDTH);
  }

  return <div ref={containerRef} className="grid-container" >
    {isRendering && <Loading active transparent />}
    <WindowScroller scrollElement={scrollElement}>
      {({ height, isScrolling, onChildScroll, scrollTop }) => {
        return (
          <AutoSizer disableHeight>
            {() => {
              const columnCount = calculateColumnCount(containerWidth);
              const rowCount = Math.ceil(dataSource?.length / columnCount);
              const itemWidth = containerWidth / columnCount;
              return (
                <Grid
                  ref={gridRef}
                  autoHeight
                  columnCount={columnCount}
                  columnWidth={itemWidth}
                  width={containerWidth || 0}
                  height={height || 0}
                  rowCount={rowCount}
                  rowHeight={GRID_HEIGHT}
                  isScrolling={isScrolling}
                  scrollTop={scrollTop}
                  onScroll={onChildScroll}
                  overscanRowCount={5}
                  onSectionRendered={() => setIsRendering(false)}
                  cellRenderer={(props) => {
                    const index = props.rowIndex * columnCount + props.columnIndex;
                    // console.log('index', index);

                    if (index > dataSource.length - 1) {
                      return <></>;
                    }
                    const dataItem = dataSource[index];
                    return <GridItem
                      dataItem={dataItem}
                      key={props.key}
                      style={props.style}
                    // isRendering={isRendering} 
                    />
                  }}
                />
              );
            }}
          </AutoSizer>
        )
      }}
    </WindowScroller>
  </div>
}

export default GridLayout;