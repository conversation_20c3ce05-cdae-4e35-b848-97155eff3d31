import React, { useEffect, useState } from "react";
import { Form, Input } from "antd";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import { useProject } from "@app/pages/Project";

import AntButton from "@component/AntButton";
import AntModal from "@component/AntModal";
import { AntForm } from "@src/app/component/AntForm";

import { BUTTON } from "@constant";
import RULE from "@rule";

import Edit24 from "@component/SvgIcons/Edit/Edit24";

import "./TitleInput.scss";


function TitleInput({ value, onSubmit = () => null, showSubmit = true, content }) {
  const { t } = useTranslation();
  
  const { isExam } = useProject();
  
  const [titleForm] = Form.useForm();
  
  const [isLoading, setLoading] = useState(false);
  const [isEditingTitle, setEditingTitle] = useState(false);
  
  useEffect(() => {
    if (isEditingTitle) {
      titleForm.setFieldsValue({ title: value });
    }
  }, [isEditingTitle]);
  
  function handleClickEdit() {
    setEditingTitle(true);
  }
  
  function onFinish({ title }) {
    setLoading(true);
    onSubmit(title.trim())
      .then(() => {
        setEditingTitle(false);
        setLoading(false);
      });
  }
  
  return <>
    <div className="title-input">
      <div className={clsx("title-input__value", { "title-input__hide-action": !showSubmit })}>
          <span className="title-input__content-index">
            {`${content.contentIndex}. `}
          </span>
        {/*{isPreviewProject && `${content.contentIndex}. `}*/}
        {value}
      </div>
      {showSubmit && <div className="title-input__action">
        <AntButton
          size="small"
          type={isExam ? BUTTON.GHOST_WHITE : BUTTON.WHITE}
          icon={<Edit24 />}
          onClick={handleClickEdit}
        />
      </div>}
    </div>
    
    <AntModal
      open={isEditingTitle}
      onCancel={() => setEditingTitle(false)}
      formId={`form-title-${content?._id}`}
      closeIcon={null}
      submitting={isLoading}
    >
      <AntForm
        id={`form-title-${content?._id}`}
        form={titleForm}
        onFinish={onFinish}
        layout="vertical"
        requiredMark={false}
      >
        <AntForm.Item
          label={t("TITLE")}
          name="title"
          rules={[RULE.REQUIRED]}
        >
          <Input.TextArea autoSize={{ maxRows: 10 }} />
        </AntForm.Item>
      </AntForm>
    </AntModal>
  </>;
}


TitleInput.propTypes = {
  onSubmit: PropTypes.func,
  showSubmit: PropTypes.bool,
};

export default TitleInput;