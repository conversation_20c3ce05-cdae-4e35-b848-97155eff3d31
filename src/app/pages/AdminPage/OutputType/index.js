import React, { useEffect, useMemo, useState } from "react";
import { Card, Col, Form, Input, Row, Select, Tag, Tooltip } from "antd";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { EditOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";

import { createOutputType, deleteOutputType, getAllOutputTypes, updateOutputType } from "@services/OutputType";
import { handlePagingData, parseJsonToText, parseTextToJson } from "@common/dataConverter";
import { handleReplaceUrlSearch, handleSearchParams, orderColumn, paginationConfig } from "@common/functionCommons";

import { toast } from "@component/ToastProvider";
import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import { confirm } from "@component/ConfirmProvider";
import OutputTypeModal from "./OutputTypeModal";
import TableAdmin from "@component/TableAdmin";
import Loading from "@component/Loading";

import { BUTTON, PAGINATION_INIT, RESPONSE_FORMAT } from "@constant";

import { LINK } from "@link";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import "./OutputType.scss";

const OutputType = () => {
  const [outputTypeData, setOutputTypeData] = useState(PAGINATION_INIT);
  const [rowSelected, setRowSelected] = useState(null);
  const [modalState, setModalState] = useState({
    visible: false,
    outputType: null,
  });
  const [isLoading, setIsLoading] = useState(false);

  const { i18n, t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [formFilter] = Form.useForm();


  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getOutputTypeData(paging, query);
  }, [location.search]);

  async function getOutputTypeData(paging = outputTypeData.paging, query = outputTypeData.query, isUpdate = false) {
    setIsLoading(true);
    const dataResponse = await getAllOutputTypes(paging, query);

    if (dataResponse) {
      setOutputTypeData(handlePagingData(dataResponse, query));
      const selectedRow = isUpdate ? dataResponse.rows.find((x) => x._id === rowSelected._id) : dataResponse.rows[0];
      setRowSelected(selectedRow);
    }
    setIsLoading(false);
  }

  const handleDelete = (outputTypId, outputTypeName) => {
    confirm.delete({
      title: t("DELETE_OUTPUT_TYPE"),
      content: t("DELETE_OUTPUT_TYPE_CONFIRM", { name: outputTypeName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async (e) => {
        setIsLoading(true);
        const apiResponse = await deleteOutputType(outputTypId, true);
        if (apiResponse) {
          toast.success(t("DELETE_OUTPUT_TYPE_SUCCESS"));
          let paging = { ...outputTypeData.paging };
          const { total, pageSize, page } = paging;
          if (page > 1 && ((total - 1) === pageSize * (page - 1))) {
            paging.page -= 1;
          }
          await getOutputTypeData(paging);
        } else {
          toast.error(t("DELETE_OUTPUT_TYPE_ERROR"));
          setIsLoading(false);
        }
      },
    });
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, outputTypeData.paging.pageSize, values);
  };

  const handleSelectRow = (record) => {
    setRowSelected(record);
    document.getElementsByClassName("output-type-content")[0].scrollTop = 0;
  };

  async function onSave(values) {
    setIsLoading(true);
    const { schemaInstruction } = values;
    const toJson = parseTextToJson(schemaInstruction);
    const isUpdate = Boolean(modalState.outputType);
    const dataRequest = { ...values, schemaInstruction: toJson, ...(isUpdate && { _id: modalState.outputType._id }) };
    const dataResponse = isUpdate ? await updateOutputType(dataRequest, true) : await createOutputType(dataRequest, true);

    if (dataResponse) {
      toast.success(t(isUpdate ? "UPDATE_OUTPUT_TYPE_SUCCESS" : "CREATE_OUTPUT_TYPE_SUCCESS"));
      await getOutputTypeData(outputTypeData.paging, outputTypeData.query, isUpdate);
    } else {
      toast.error(t(isUpdate ? "UPDATE_OUTPUT_TYPE_ERROR" : "CREATE_OUTPUT_TYPE_ERROR"));
      setIsLoading(false);
    }

    onToggleModal();
  }

  const onToggleModal = (outputType) => {
    setModalState(pre => ({ visible: !pre.visible, outputType: outputType }));
  };

  const columns = [
    orderColumn(outputTypeData.paging),
    {
      title: t("NAME"),
      dataIndex: "name",
      width: 250,
      colSpan: 1,
      render: (value) => <span className="output-type-name-value">{value}</span>,
    },
    {
      title: t("CODE"),
      dataIndex: "code",
      width: 250,
      colSpan: 1,
      render: (value) => <span className="line-clamp-1">{value}</span>,
    },
    {
      title: t("RESPONSE_FORMAT"),
      dataIndex: "responseFormat",
      width: 150,
      render: (value) => {
        let color;

        switch (value) {
          case "json":
            color = "blue";
            break;
          case "text":
            color = "green";
            break;
          case "html":
            color = "purple";
            break;
          case "json_object":
            color = "geekblue";
            break;
          case "markdown":
            color = "cyan";
            break;
          default:
            color = "default";
        }

        const format = RESPONSE_FORMAT.find((output) => output.value === value);
        const label = format?.label || value;

        return (
          <Tag color={color}>
            {label}
          </Tag>
        );
      },
    },
    {
      title: <div className="output-type-content-title">
        {t("SCHEMA")}
        {rowSelected && <div className={"output-type-content-action"}>
          <Tooltip title={t("EDIT_OUTPUT_TYPE")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-edit-output-type"}
              icon={<EditOutlined/>}
              onClick={() => onToggleModal(rowSelected)}
            />
          </Tooltip>
          <Tooltip title={t("DELETE_OUTPUT_TYPE")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-output-type"}
              icon={<DeleteIcon/>}
              onClick={() => handleDelete(rowSelected?._id, rowSelected?.name)}
            />
          </Tooltip>
        </div>}
      </div>,
      key: "schemaInstruction",
      render: () => (
        <div
          className={`output-type-content rows-height-${outputTypeData?.rows?.length}`}
          onClick={(e) => e.stopPropagation()}
        >
          <Input.TextArea
            value={parseJsonToText(rowSelected.schemaInstruction)}
            autoSize={{ minRows: 1 }}
            disabled
          />
        </div>
      ),

      onCell: (_, index) => ({
        rowSpan: !index ? outputTypeData?.rows?.length : 0,
        colSpan: 10,
      }),
    },
  ];

  const pagination = useMemo(() => paginationConfig(outputTypeData.paging, outputTypeData.query, i18n.language), [navigate, outputTypeData.paging, outputTypeData.query, i18n.language]);

  const clearFilter = () => {
    formFilter.resetFields();
    onSubmitFilter({});
  };
  return (
    <Loading active={isLoading} transparent>
      <div className="output-type">
        <Card className="output-type-info-card">
          <div className="output-type-info-header">
            <div>
              <h1 className="output-type-title">{t("OUTPUT_TYPE_MANAGEMENT")}</h1>
              <p className="output-type-description">{t("OUTPUT_TYPE_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <AntButton
              type={BUTTON.DEEP_NAVY}
              size="large"
              icon={<PlusOutlined/>}
              className={"btn-create-output-type"}
              onClick={() => onToggleModal()}
            >
              {t("CREATE_OUTPUT_TYPE")}
            </AntButton>
          </div>
        </Card>

        <Card className="output-type-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={12}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_OUTPUT_TYPE_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={12}>
                    <AntForm.Item name="responseFormat" className="search-form-item">
                      <Select
                        options={RESPONSE_FORMAT}
                        allowClear
                        placeholder={t("SEARCH_RESPONSE_FORMAT_PLACEHOLDER")}
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={clearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="output-type-table-card">
          <TableAdmin
            columns={columns}
            bordered
            dataSource={outputTypeData.rows}
            pagination={pagination}
            className={"output-type-table"}
            onRow={(row) => ({
              onClick: () => handleSelectRow(row),
            })}
            rowClassName={(row) => (rowSelected?._id === row._id ? "row-selected" : "")}
            locale={{ emptyText: t("NO_OUTPUT_TYPES_FOUND") }}
          />
        </Card>

        <OutputTypeModal {...modalState} onCancel={onToggleModal} onFinish={onSave}/>
      </div>
    </Loading>
  );
};

export default OutputType;
