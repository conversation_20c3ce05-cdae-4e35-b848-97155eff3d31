.watch-video-container {
  height: 100vh;
  width: 100vw;
  position: relative;

  .watch-video__ratio-container {
    background-color: red;
    width: 100%;
    padding-top: 56.25%;
    position: relative;

    .watch-video__ratio-item {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
    }
  }

  .watch-video__player {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -60%);
    width: 700px;

    @media screen and (max-width: 768px) {
      width: calc(100% - 40px);
      top: 250px;
    }
  }
}

