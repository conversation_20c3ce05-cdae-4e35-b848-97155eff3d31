.package-container {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  font-family: "Segoe UI";
  gap: 24px;

  .package-header {
    display: flex;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 24px;

    .package-header__title {
      font-weight: 700;
      font-size: 24px;
      line-height: 30px;
      text-align: center;
    }

    .package-header__terms {
      color: var(--typo-colours-support-blue);
    }

    .package-header__info {
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      text-align: center;
    }

    .package-header__timePackage {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      flex-direction: row;
      border-radius: 4px 0px 0px 4px;
      box-shadow: var(--shadow-level-2);

      :first-child {
        border-radius: 4px 0px 0px 4px;
      }

      :last-child {
        border-radius: 0px 4px 4px 0px;
      }

      .ant-btn-deep-blue {
        border: 0 !important;
      }

      > button {
        color: var(--typo-colours-primary-black);
        display: flex;
        width: 200px;
        height: 56px;
        padding: 8px 32px;
        flex-direction: column;
        align-items: center;
        gap: 3px;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
        border: 1px solid var(--background-light-background-grey) !important;

      }

      .ant-btn-white {
        .package-header__typo-year {
          color: var(--typo-colours-support-green);
        }

        .package-header__typo-package {
          color: var(--typo-colours-support-purple)
        }
      }

    }

    .package-header__typo-year {
      font-weight: 400;
      font-size: 13px;
      line-height: 16.25px;
    }

    .package-header__typo-package {
      font-weight: 400;
      font-size: 13px;
      line-height: 16.25px;
    }

    .package-content {
      // width: 100%;
    }

  }
  .grid-incentive-content{
    width: 1000px;
    @media screen and (max-width: 1300px){
      width: 80%;
    }
    @media screen and (max-width: 700px){
      width: 100%;
    }
  }
}