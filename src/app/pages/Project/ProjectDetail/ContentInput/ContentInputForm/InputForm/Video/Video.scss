.form-video-input {
  .ant-form-item {
    margin-bottom: 0;

    .ant-form-item-explain-error {
      margin-bottom: 0 !important;
    }
  }
}

.youtube-url.ant-form-item .ant-form-item-label {
  overflow: visible;

  label {
    display: block;

    &:after {
      content: none;
    }

    .youtube-url-label {
      display: flex;
      justify-content: space-between;
      width: 100%;


      .select-from-resource-button {
        margin: -2px 0;
      }
    }
  }
}

.youtube-frame-preview {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;


  .youtube-frame-inner {
    position: relative;
    aspect-ratio: 16/9;
    min-width: 496px;
    display: flex;
    border-radius: 8px;
    overflow: hidden;

    @media screen and (max-width: 1535.98px) {
      min-width: 400px;
    }

    @media screen and (max-width: 1366px) {
    }


    .youtube-embed {
      z-index: 2;
      width: 496px;
      height: 279px;
    }

    .youtube-embed-thumbnail {
      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

}

.offline-video-preview-container {
  display: flex;
  justify-content: center;

  .offline-video-preview {
    border-radius: 8px;
    overflow: hidden;
  }
}