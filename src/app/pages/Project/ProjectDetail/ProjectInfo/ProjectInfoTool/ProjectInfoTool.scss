.project-info__tool {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .project-tool__search {
    .ant-form-item:last-child {
      margin-bottom: 0;
    }
  }

  .tool-category__container {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .tool-category__item {
      margin: 0 -24px;
      user-select: none;

      .ant-collapse-item {
        display: flex;
        flex-direction: column;
        gap: 8px;

        &.ant-collapse-item-active .ant-collapse-header {
          background: var(--primary-colours-blue-navy);
          color: var(--white);

          .tool-category__image svg path {
            stroke: var(--white);
          }
        }

        .ant-collapse-header {
          padding: 8px 24px;
          border-radius: 0;
          align-items: center;
        }

        .ant-collapse-content .ant-collapse-content-box {
          padding: 0;
          display: flex;
          flex-direction: column;
          gap: 8px;

          > .tool-category__tool {
            padding: 8px 24px;
            display: flex;
            flex-direction: row;
            gap: 8px;

            &:hover {
              background: var(--primary-colours-blue-navy-light-1);
            }

            &:not(:has(.tool-category__tool-fav-icon > *:active)):not(.tool-category__tool-disabled) {
              cursor: pointer;

              &:active {
                background: var(--primary-colours-blue-navy);
                color: var(--white);
              }
            }


            .tool-category__tool-fav {
              width: 16px;
              display: flex;
              align-items: center;

              .tool-category__tool-fav-icon {
                display: contents;
                cursor: pointer;
              }
            }

            .tool-category__tool-name {
              flex: 1;
            }

          }
        }
      }

      &.tool-category__filterer .ant-collapse-header {
        display: none;
      }

      &:hover {
        .ant-collapse-header {
          background: var(--primary-colours-blue-navy-light-1);
        }
      }

      .tool-category__name {
        display: flex;
        flex-direction: row;
        gap: 8px;

        .tool-category__image {
          display: flex;
          align-items: center;

          > * {
            height: 16px;
            width: 16px;
          }
        }
      }

      .tool-category__container {
      }
    }

    .tool-category__no-data {
      width: 120px;
      margin: auto;
    }
  }
}

.tool-tooltip {
  box-shadow: var(--shadow-level-2);

  .ant-tooltip-arrow {
    left: 36px;
    bottom: -6px;

    &::before {
      display: none;
    }

    &::after {
      background-color: var(--background-light-background-2);
    }
  }

  .ant-tooltip-inner {
    padding: 16px;
    background-color: var(--background-light-background-2);
    margin-left: 24px;
    margin-bottom: -6px;
    border-radius: 8px;

    .tool-tooltip__title {
      color: var(--primary-colours-blue-navy);
      cursor: pointer;
    }
  }
}