import React from "react";
import { useTranslation } from "react-i18next";
import MetricCard from "./shared/MetricCard";

const OverviewMetrics = ({ statisticsData, isLoading = false }) => {
  const { t } = useTranslation();

  if (!statisticsData?.overview) {
    return null;
  }

  const { overview } = statisticsData;

  // Define metrics with enhanced data
  const metrics = [
    {
      title: "TOTAL_SENT",
      value: overview.totalSent,
      icon: "MailOutlined",
      color: "blue",
      trend: null, // Can be enhanced later with previous period data
      description: "TOTAL_EMAILS_SENT_DESC"
    },
    {
      title: "TOTAL_OPENED",
      value: overview.totalOpened,
      icon: "EyeOutlined",
      color: "green",
      trend: null,
      description: "TOTAL_EMAILS_OPENED_DESC"
    },
    {
      title: "OPEN_RATE",
      value: overview.openRate,
      icon: "PercentageOutlined",
      color: "orange",
      suffix: "%",
      precision: 1,
      trend: null,
      description: "EMAIL_OPEN_RATE_DESC"
    },
    {
      title: "CLICK_RATE",
      value: overview.clickRate,
      icon: "PercentageOutlined",
      color: "purple",
      suffix: "%",
      precision: 1,
      trend: null,
      description: "EMAIL_CLICK_RATE_DESC"
    }
  ];

  return (
    <div className="overview-metrics-grid">
      {metrics.map((metric, index) => (
        <MetricCard
          key={metric.title}
          title={metric.title}
          value={metric.value}
          icon={metric.icon}
          color={metric.color}
          trend={metric.trend}
          suffix={metric.suffix}
          precision={metric.precision}
          description={metric.description}
          loading={isLoading}
        />
      ))}
    </div>
  );
};

export default OverviewMetrics;
