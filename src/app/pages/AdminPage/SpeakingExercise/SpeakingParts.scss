/* Shared styles for all Speaking Parts */

/* Hint Modal Styles */
.hint-modal {
  .ant-modal-content {
    border-radius: 12px;
  }

  .ant-modal-header {
    border-radius: 12px 12px 0 0;
  }

  .hint-modal-footer {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    gap: 12px;
  }

  .modal-button {
    min-width: 120px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    border-radius: 4px;

    &.ant-btn-primary {
      background-color: #1890ff;
      border-color: #1890ff;
      color: #fff;

      &:hover {
        background-color: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &.recreate-button {
      background-color: #1890ff;
      border-color: #1890ff;
      color: #fff;

      &:hover {
        background-color: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &.close-button {
      border-color: #09196b;
      color: #09196b;
    }
  }
}

.hint-preview {
  margin-top: 16px;

  h3 {
    font-size: 16px;
    margin-bottom: 12px;
    color: #333;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .question-section {
    margin-bottom: 24px;
  }

  .question-content {
    background-color: #f9f9f9;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 16px;
    font-size: 15px;
    line-height: 1.6;
    color: #333;
    white-space: pre-wrap;
  }

  .hint-section {
    margin-top: 24px;

    h3 {
      color: #3a18ce;
      display: flex;
      align-items: center;

      /* Icon được thêm trực tiếp trong component React */

      .recreate-hint-button {
        margin-left: auto;
        font-size: 14px;
        padding: 0 12px;
        height: 28px;
        display: flex;
        align-items: center;
        gap: 6px;
        background-color: #3a18ce;
        border-color: #3a18ce;
        color: white;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(58, 24, 206, 0.2);

        .anticon {
          font-size: 12px;
        }

        &:hover {
          background-color: #4a28de;
          border-color: #4a28de;
          color: white;
          transform: translateY(-1px);
          box-shadow: 0 3px 6px rgba(58, 24, 206, 0.3);
        }

        &:active {
          background-color: #3010b0;
          border-color: #3010b0;
        }
      }
    }

    .hint-edit-container {
      margin-top: 16px;

      .hint-edit-textarea {
        border: 1px solid #d6d1ff;
        border-radius: 12px;
        padding: 16px;
        font-size: 15px;
        line-height: 1.8;
        background-color: #f0f2ff;
        transition: all 0.3s;

        &:hover, &:focus {
          border-color: #7b61ff;
          box-shadow: 0 0 0 2px rgba(58, 24, 206, 0.1);
          background-color: #fff;
        }
      }
    }
  }

  .hint-content {
    background-color: #f0f2ff;
    border: 1px solid #d6d1ff;
    border-radius: 12px;
    padding: 20px;
    font-size: 15px;
    line-height: 1.8;
    color: #333;
    white-space: pre-wrap;
    box-shadow: 0 2px 8px rgba(58, 24, 206, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, #7b61ff, #3a18ce);
    }
  }
}

/* Style for Tooltip */
.ant-tooltip {
  .ant-tooltip-inner {
    font-size: 13px;
    padding: 6px 10px;
    min-height: auto;
  }
}

/* Style for Popconfirm */
.ant-popconfirm {
  .ant-popconfirm-message {
    .ant-popconfirm-title {
      font-weight: 600;
    }

    .ant-popconfirm-description {
      margin-top: 8px;
      color: #666;
    }
  }

  .ant-popconfirm-buttons {
    .ant-btn-primary.ant-btn-dangerous {
      background-color: #ff4d4f;
      border-color: #ff4d4f;

      &:hover {
        background-color: #ff7875;
        border-color: #ff7875;
      }
    }
  }
}
.part-description {
  background-color: #f5f7fa;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
  margin-bottom: 20px;
  font-size: 15px;
  line-height: 1.6;
  color: #555;
}
.speaking-questions-table {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;

  .ant-table {
    border-radius: 8px;
    overflow: hidden;

    .ant-table-container {
      border-radius: 8px;
      overflow: hidden;
    }
  }

  .ant-table-thead > tr > th {
    background-color: #f5f7fa;
    color: #333;
    font-weight: 600;
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    font-size: 15px;
  }

  .ant-table-tbody > tr > td {
    padding: 12px 16px;
    vertical-align: middle;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f9f9f9;
  }

  .question-number {
    font-weight: 600;
    color: #1890ff;
    font-size: 16px;
  }

  .question-textarea {
    width: 100%;
    resize: none;
    border: 1px solid transparent;
    background-color: transparent;
    padding: 8px;
    font-size: 15px;
    line-height: 1.6;
    min-height: 40px;
    border-radius: 4px;

    &:hover, &:focus {
      border-color: #d9d9d9;
      background-color: #fff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }

  .question-audio {
    width: 100%;
    height: 40px;
    border-radius: 4px;
    //background-color: #f9f9f9;
  }

  .ant-space {
    justify-content: center;

    .ant-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      padding: 0;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }

      // Button for creating audio (default state)
      &.audio-btn-default {
        background-color: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;

        &:hover {
          background-color: #bae7ff;
          border-color: #1890ff;
        }

        &:disabled {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;

          &:hover {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);
            transform: none;
            box-shadow: none;
          }
        }
      }

      // Button for recreating audio (primary state)
      &[type="primary"] {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;

        &:hover {
          background-color: #40a9ff;
          border-color: #40a9ff;
        }

        &:disabled {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;

          &:hover {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);
            transform: none;
            box-shadow: none;
          }
        }
      }

      &.ant-btn-dangerous {
        background-color: #fff;
        border-color: #ff4d4f;
        color: #ff4d4f;

        &:hover {
          background-color: #ff4d4f;
          color: #fff;
        }
      }

      // Button for AI hint generation
      &.ai-hint-btn {
        background-color: #f0f2ff;
        border-color: #7b61ff;
        color: #3a18ce;
        position: relative;

        .ai-gen-star-icon {
          width: 16px;
          height: 16px;
        }

        .hint-badge {
          position: absolute;
          top: -3px;
          right: -3px;
          width: 8px;
          height: 8px;
          background-color: #3a18ce;
          border-radius: 50%;
          display: block;
        }

        &:hover {
          background-color: #e6e9ff;
          border-color: #3a18ce;
          box-shadow: 0 2px 8px rgba(58, 24, 206, 0.2);
        }

        &:disabled {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;

          &:hover {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);
            transform: none;
            box-shadow: none;
          }

          .hint-badge {
            background-color: rgba(0, 0, 0, 0.1);
          }
        }

        // Style for show hint button
        &.show-hint {
         // màu xanh
          background-color: #f0f2ff;
          border-color: #7b61ff;
          color: #3a18ce;

          .hint-badge {
            background-color: #3a18ce;
          }

          &:disabled {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);

            .hint-badge {
              background-color: rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }
  }
}

.ant-table-footer{
  padding: 0 !important;
  .add-question-container {
    display: flex;
    margin-top: 24px;
    background-color: #f9f9f9;
    padding: 16px;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
    position: relative;
    transition: all 0.3s;
    padding-top: 24px;

    .add-question-label {
      position: absolute;
      top: -10px;
      left: 16px;
      background-color: #fff;
      padding: 0 8px;
      font-size: 12px;
      color: #888;
      font-weight: 500;
      border-radius: 4px;
    }

    &:hover {
      border-color: #40a9ff;
      background-color: #f0f7ff;
    }

    .ant-input {
      flex: 1;
      margin-right: 16px;
      border-radius: 4px;
      padding: 8px 12px;
      min-height: 60px;
      font-size: 15px;
      line-height: 1.6;

      &:hover, &:focus {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }

    .add-question-buttons {
      display: flex;
      gap: 12px;

      .add-multiple-questions-button {
        color: #1890ff;
        border-color: #1890ff;

        .anticon {
          font-size: 16px;
        }

        &:hover {
          color: #1890ff;
          border-color: #1890ff;
          background-color: #f0f7ff;
          transform: translateY(-1px);

          .anticon {
            font-size: 16px;
          }
        }

        &:focus {
          color: #1890ff;
          border-color: #1890ff;
          background-color: #f6ffed;
        }

        &:disabled {
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.25);
          background-color: #f5f5f5;

          .anticon {
            color: rgba(0, 0, 0, 0.25);
          }

          &:hover {
            transform: none;
            box-shadow: none;
          }
        }
      }
    }

    .ant-btn {
      border-radius: 4px;
      height: auto;
      padding: 8px 16px;
      display: flex;
      align-items: center;

      .anticon {
        margin-right: 6px;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

}
/* Batch Operations Styles */
.batch-operations-container {
  margin-top: 24px;
  //padding: 16px;
  //border-top: 1px dashed #e8e8e8;
  //background-color: #f9f9f9;
  border-radius: 0 0 8px 8px;
}

.batch-operations-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  font-size: 16px;
  text-align: right;
}

.batch-operations-buttons {
  display: flex;
  gap: 16px;
  justify-content: flex-end;

  .batch-operation-button {
    min-width: 180px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border-radius: 4px;
    font-size: 14px;

    &:disabled {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      color: rgba(0, 0, 0, 0.25);
      cursor: not-allowed;

      &:hover {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.25);
        transform: none;
        box-shadow: none;
      }
    }

    &.ai-batch-button {
      background-color: #3a18ce;
      border-color: #3a18ce;

      &:hover {
        background-color: #4a28de;
        border-color: #4a28de;
      }

      &:disabled {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.25);

        &:hover {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.25);
        }
      }

      .ai-gen-star-icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
  }
}

/* Specific styles for Part 2 */
.speaking-part2-container {
  .cue-card-section {
    margin-bottom: 16px;
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    background-color: #fafafa;
    position: relative;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .cue-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .cue-card-number {
        font-size: 16px;
        color: #1890ff;
        background-color: #e6f7ff;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .cue-card-actions {
        display: flex;
        gap: 8px;

        .ant-btn {
          border-radius: 4px;
          display: flex;
          align-items: center;


          &[type="primary"] {
            background-color: #1890ff;
            border-color: #1890ff;
            color: #fff;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
              background-color: #40a9ff;
              border-color: #40a9ff;
            }
          }

          &.audio-btn-default {
            background-color: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
              background-color: #bae7ff;
              border-color: #1890ff;
            }
          }

          // Button for AI hint generation in Part 2
          &.ai-hint-btn {
            background-color: #f0f2ff;
            border-color: #7b61ff;
            color: #3a18ce;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;

            .ai-gen-star-icon {
              width: 16px;
              height: 16px;
            }

            .hint-badge {
              position: absolute;
              top: -3px;
              right: -3px;
              width: 8px;
              height: 8px;
              background-color: #3a18ce;
              border-radius: 50%;
              display: block;
            }

            &:hover {
              background-color: #e6e9ff;
              border-color: #3a18ce;
              box-shadow: 0 2px 8px rgba(58, 24, 206, 0.2);
              transform: translateY(-1px);
            }

            // Style for show hint button in Part 2
            &.show-hint {
              background-color: #f0f2ff;
              border-color: #7b61ff;
              color: #3a18ce;
              &:hover {
                background-color: #e6e9ff;
                border-color: #3a18ce;
                box-shadow: 0 2px 8px rgba(58, 24, 206, 0.2);
                transform: translateY(-1px);
              }
            }
          }
        }
      }
    }

    .cue-card-textarea-container {
      position: relative;
      margin-bottom: 12px;

      .cue-card-label {
        position: absolute;
        top: -10px;
        left: 16px;
        background-color: #fff;
        padding: 0 8px;
        font-size: 12px;
        color: #1890ff;
        font-weight: 500;
        border: 1px solid #91d5ff;
        border-radius: 4px;
        z-index: 1;
      }

      .cue-card-textarea {
        margin-bottom: 0;
        border: 1px solid #d9d9d9;
        padding: 16px;
        background-color: #fff;
        border-radius: 8px;
        min-height: 120px;
        font-size: 15px;
        line-height: 1.6;
        position: relative;
        transition: all 0.3s;

        &:hover, &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
      }
    }

    .cue-card-audio {
      width: 100%;
      height: 40px;
      border-radius: 4px;
    }
  }

  .add-cue-card-button-container {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 0;
    margin-bottom: 0;
    flex: 1;

    .add-cue-card-button,
    .add-multiple-cue-cards-button {
      border-radius: 4px;
      height: 40px;
      padding: 0 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-style: dashed;
      transition: all 0.3s;
      color: #1890ff;
      border-color: #1890ff;
      .anticon {
        margin-right: 8px;
        font-size: 16px;
      }

      &:hover {
        color: #1890ff;
        border-color: #1890ff;
        background-color: #f0f7ff;
        transform: translateY(-1px);
      }
    }

    .add-multiple-cue-cards-button {
      color: #1890ff;
      border-color: #1890ff;

      .anticon {
        font-size: 16px;
      }

      &:hover {
        color: #1890ff;
        border-color: #1890ff;
        background-color: #f0f7ff;
        transform: translateY(-1px);

        .anticon {
          font-size: 16px;
        }
      }

      &:focus {
        color: #1890ff;
        border-color: #1890ff;
        background-color: #f6ffed;
      }

      &:disabled {
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.25);
        background-color: #f5f5f5;

        .anticon {
          color: rgba(0, 0, 0, 0.25);
        }

        &:hover {
          transform: none;
          box-shadow: none;
        }
      }
    }
  }
}

/* Bulk Add Modal Styles */
.bulk-add-modal-content {
  .bulk-add-description {
    margin-bottom: 16px;
    font-size: 15px;
    color: #555;
    line-height: 1.6;
  }

  .ant-input {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    padding: 12px;
    font-size: 15px;
    line-height: 1.6;
    transition: all 0.3s;

    &:hover, &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }

    &::placeholder {
      color: #bfbfbf;
      font-style: italic;
    }
  }

  .bulk-add-info {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #f6f8fa;
    border-radius: 6px;
    border-left: 3px solid #1890ff;

    .ant-typography {
      margin: 0;
      font-size: 13px;
      line-height: 1.5;
    }
  }
}
