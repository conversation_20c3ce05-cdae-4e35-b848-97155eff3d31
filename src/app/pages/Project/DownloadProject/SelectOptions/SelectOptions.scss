@import "src/app/styles/scroll";

.download-project__select-options {
  .download-project__template-list {
    flex-shrink: 0;

    .org-doc-template-list {
      .org-doc-template-item {
        width: 156px;
      }
    }
  }

  .select-options__form {

    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 1;
    overflow-y: auto;

    .ant-form-item {
      margin: 0;
    }


    .select-options__select-content {
      @extend .scrollbar;
      @extend .scrollbar-show;

      display: flex;
      flex-direction: column;
      gap: 24px;
      min-height: 108px;

      .ant-form-item-control-input {
        min-height: 20px;
      }

      .ant-checkbox-group {

        .ant-checkbox-wrapper {
          width: 100%;

          &:not(:last-child) {
            margin-bottom: 24px;
          }
        }
      }
    }

    .ck-editor__editable {
      height: 100px;
      min-height: 100px;
    }
  }
}