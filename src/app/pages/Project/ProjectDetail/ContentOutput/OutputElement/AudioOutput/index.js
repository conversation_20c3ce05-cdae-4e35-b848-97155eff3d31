import React, { useMemo } from "react";

import NoData from "@component/NoData";
import AudioPlayer from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm/InputForm/Audio/AudioPlayer";

import { API } from "@api";

import VoiceViolet from "@component/SvgIcons/Voice/VoiceViolet";
import Download from "@component/SvgIcons/Download";

import "./AudioOutput.scss";

function AudioOutput({ content, responseSelected, ...props }) {
  const outputData = responseSelected.output;
  
  const audioFileId = useMemo(() => outputData?.audio?.audioFileId, [outputData?.audio?.audioFileId]);
  
  return <>
    {audioFileId
      ? <div className="audio-output-container">
        <AudioPlayer id={content?._id} src={API.STREAM_MEDIA.format(audioFileId)} />
        
        <div className="audio-output__download">
          <a
            href={API.STREAM_ID.format(audioFileId)}
            className="ant-btn ant-btn-sm ant-btn-light-blue"
          >
            <span className="ant-btn-icon">
              <VoiceViolet />
            </span>
            <span className="media-name">
              {outputData?.audio.displayName}
            </span>
            <span className="ant-btn-icon">
              <Download />
            </span>
          </a>
        </div>
      </div>
      : <NoData />}
  </>;
}

export default AudioOutput;