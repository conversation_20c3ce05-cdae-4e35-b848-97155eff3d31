import { API } from "@api";
import { createBase, deleteBase, getAllBase, getBase, getDetailBase, joinParams, updateBase } from "@services/Base";
import axios from "axios";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import { genQueryParam } from "@common/functionCommons";

export function getAllDocumentHeader(query) {
  return getAllBase(API.DOCUMENT_HEADER, query);
}

export async function getDocDefaultHeader(docxTemplateId, organizationId) {
  let queryStr = `docxTemplateId=${docxTemplateId}`;
  if (organizationId) {
    queryStr += `&organizationId=${organizationId}`;
  }
  return axios.get(`${API.DOCUMENT_HEADER}/getDefaultHeader?${queryStr}`)
              .then(response => response?.data)
              .catch(() => null);
}

export async function getDocHeaderForProject(docxTemplateId, config = {}) {
  return axios.get(`${API.DOCUMENT_HEADER}/getHeaderUserTemplate?docxTemplateId=${docxTemplateId}`, config)
              .then(response => response?.data)
              .catch(() => null);
}

export async function getDocumentHeaderByDocTemplate(docxTemplateId) {
  const query = { docxTemplateId };
  const dataResponse = await axios.get(`${API.DOCUMENT_HEADER}/findAll?${genQueryParam(query)}`)
                                  .then(response => {
                                    if (response.status === 200) return response.data;
                                    return null;
                                  })
                                  .catch((err) => {
                                    return null;
                                  });
  if (Array.isArray(dataResponse) && dataResponse.length) {
    return dataResponse[0];
  }
  return null;
}


export function getDocumentHeaderDetail(id) {
  return getDetailBase(API.DOCUMENT_HEADER_ID, id, ["baseTemplateId", "templateId"]);
}

export function editDocumentHeader({ _id, ...data }) {
  return axios.put(API.DOCUMENT_HEADER_ID.format(_id), (data))
              .then(response => {
                if (response.status === 200) return response.data;
                return null;
              })
              .catch(() => null);
}

export function deleteDocumentHeader(id) {
  return deleteBase(API.DOCUMENT_HEADER_ID, id);
}

export function createDocumentHeader(data) {
  return createBase(API.DOCUMENT_HEADER, data);
}

export function createDefaultHeader(data) {
  return createBase(API.DOCUMENT_HEADER + "/makeDefaultHeader", data);
}

export function createDocumentHeaderOrg(data) {
  return createBase(API.DOCUMENT_HEADER + "/makeHeaderOrg", data);
}

export function createDocumentHeaderProject(data) {
  return createBase(API.DOCUMENT_HEADER + "/makeHeaderUser", data);
}