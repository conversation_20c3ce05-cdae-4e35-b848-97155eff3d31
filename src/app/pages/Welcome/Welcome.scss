@import "src/app/styles/main";


.welcome-container {
  margin: -32px -24px;
  padding: 0 24px;
  background: linear-gradient(180deg, #FEFCFF 0%, #C0E3FF 100%);
  min-height: calc(100% + 32px + 32px);
  display: flex;
  justify-content: center;
  align-items: center;

  .welcome-inner {
    display: flex;
    flex-direction: column;
    row-gap: 32px;
    padding: 32px 0;

    .welcome__left {
      display: flex;
      flex-direction: column;
      row-gap: 40px;

      .welcome-intro {
        grid-column: span 5 / span 5;
        display: flex;
        flex-direction: column;
        gap: 16px;

        .welcome-intro__heading {
          font-size: 50px;
          font-weight: 700;
          line-height: 66.5px;

          .welcome-intro__heading-highlight {
            color: var(--primary-colours-blue-navy);
          }
        }

        .welcome-intro__label {
          font-weight: 600;
        }
      }

      .welcome-template {
        grid-column: 1 / -1;

        .template-list {
          grid-template-columns: repeat(4, minmax(0, 1fr));
        }
      }
    }

    .welcome-suggest {

      .welcome-suggest__item {


        &-youtube .welcome-suggest__item-inner {
          background-color: #F82558;
        }

        &-image .welcome-suggest__item-inner {
          background-color: #2196F3;
        }

        &-document .welcome-suggest__item-inner {
          background-color: #F3B108;
        }

        &-audio .welcome-suggest__item-inner {
          background-color: #00B15F;
        }

        .welcome-suggest__item-inner {
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          padding: 16px;
          gap: 16px;
          user-select: none;

          .suggest__header {
            display: flex;
            gap: 16px;

            .suggest-header__image {
              width: 40px;
              height: 40px;
              object-fit: cover;
            }

            .suggest-header__title {
              font-weight: 600;
              font-size: 24px;
              align-content: center;
              color: var(--white);
            }
          }

          .suggest__content {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .suggest-content__tool {
              cursor: pointer;
              border-radius: 8px;
              padding: 16px;
              background-color: var(--white);
              display: flex;
              flex-direction: column;
              gap: 16px;

              .suggest-content__tool-name {
                display: flex;

                .suggest-tool-name__label {
                  @extend .line-clamp-2;

                  font-weight: 600;
                  width: calc(100% - 20px);
                }

                .suggest-tool-name__icon {
                  display: flex;
                  align-items: center;
                  margin-left: auto;

                  svg path {
                    stroke: var(--black);
                  }
                }
              }

              .suggest-content__tool-description {
                @extend .line-clamp-3;

                font-size: 14px;
              }
            }

            .suggest__title {
              color: var(--typo-colours-primary-black);
              font-weight: 600;
            }

            .suggest__description {
              color: var(--typo-colours-support-blue-dark);
              font-size: 14px;
              line-height: 17.5px; /* 125% */
            }
          }

        }
      }


      @media screen and (min-width: 1920px) {
        .welcome-suggest__item:first-child {
          margin-top: 80px;
        }
      }
      @media screen and (max-width: 1535.98px) {
      }
      @media screen and (max-width: 1366px) {
      }
      @media screen and (max-width: 800px) {
      }

    }
  }


  // ------------------ responsive ------------------
  @media screen and (min-width: 1920px) {
    .welcome-inner {
      column-gap: 24px;
      align-items: center;
      display: grid;
      grid-template-columns: repeat(12, minmax(0, 1fr));
      padding: 0;

      .welcome__left {
        grid-column: span 6 / span 6;
        grid-column-start: 2;

        display: grid;
        grid-template-columns: repeat(6, minmax(0, 1fr));
        column-gap: 24px;
      }

      .welcome-suggest {
        grid-column: span 4 / span 4;
        gap: 24px;
        display: flex;
        flex-flow: column wrap;
        align-content: space-between;
        max-height: 875px;
        overflow: hidden;

        .welcome-suggest__item {
          width: calc(50% - 12px);

          &:first-child {
            margin-top: 80px;
          }
        }

      }
    }
  }

  @media screen and (min-width: 1024px) and (max-width: 1919.98px) {
    .welcome-suggest {
      display: grid;
      gap: 24px;
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media screen and (min-width: 1536px) and (max-width: 1919.98px) {
    .welcome-inner {
      width: 1064px;
    }
  }

  @media screen and (min-width: 1024px) and (max-width: 1535.98px) {
    .welcome-inner {
      width: 929px;
    }
  }

  @media screen and (min-width: 768px) and (max-width: 1023.98px) {
    .welcome-inner {
      width: 657px;

      .welcome__left {
        .welcome-template {
          .template-list {
            grid-template-columns: repeat(3, minmax(0, 1fr));
          }
        }
      }

      .welcome-suggest {
        display: grid;
        gap: 24px;
        grid-template-columns: repeat(3, minmax(0, 1fr));
      }
    }
  }

  @media screen and (max-width: 767.98px) {
    .welcome-inner {
      width: calc(100% - 20px);

      .welcome__left {
        .welcome-template {
          .template-list {
            grid-template-columns: repeat(1, minmax(0, 1fr));
          }
        }
      }

      .welcome-suggest {
        display: grid;
        gap: 24px;
        grid-template-columns: repeat(1, minmax(0, 1fr));
      }
    }
  }
}

.suggest-tool-description--popover{
  max-width: 700px;
  padding: 0px 10px;
}

