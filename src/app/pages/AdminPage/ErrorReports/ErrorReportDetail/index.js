import React, { useEffect, useState } from "react";
import { Card, Col, Row, Tag, Image, Button, Space, Descriptions, Typography, Switch, Input } from "antd";
import { useTranslation } from "react-i18next";
import { CheckOutlined, CloseOutlined, PlayCircleOutlined } from "@ant-design/icons";

import AntModal from "@src/app/component/AntModal";
import Loading from "@src/app/component/Loading";
import AntButton from "@src/app/component/AntButton";

import { formatDate } from "@src/common/functionCommons";
import { BUTTON } from "@constant";
import { getErrorReportDetail, updateErrorReport } from "@services/ErrorReport";
import { toast } from "@src/app/component/ToastProvider";

import "./ErrorReportDetail.scss";

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

function ErrorReportDetail({ open, errorReport, onClose, onUpdate }) {
  const { t } = useTranslation();
  const [detailData, setDetailData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [adminNotes, setAdminNotes] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (open && errorReport?._id) {
      fetchErrorReportDetail();
    }
  }, [open, errorReport]);

  const fetchErrorReportDetail = async () => {
    setIsLoading(true);
    try {
      const response = await getErrorReportDetail(errorReport._id);
      if (response) {
        setDetailData(response);
        setAdminNotes(response.adminNotes || "");
      }
    } catch (error) {
      toast.error(t("FETCH_ERROR_REPORT_DETAIL_ERROR"));
    }
    setIsLoading(false);
  };

  const handleUpdateStatus = async (field, value) => {
    setIsUpdating(true);
    try {
      const updateData = {
        _id: detailData._id,
        [field]: value
      };

      const response = await updateErrorReport(updateData);
      if (response) {
        setDetailData({ ...detailData, [field]: value });
        onUpdate && onUpdate({ ...detailData, [field]: value });
        toast.success(t("ERROR_REPORT_UPDATED_SUCCESS"));
      }
    } catch (error) {
      toast.error(t("UPDATE_ERROR_REPORT_ERROR"));
    }
    setIsUpdating(false);
  };

  const handleSaveNotes = async () => {
    setIsUpdating(true);
    try {
      const response = await updateErrorReport({
        _id: detailData._id,
        adminNotes
      });

      if (response) {
        setDetailData({ ...detailData, adminNotes });
        toast.success(t("ADMIN_NOTES_SAVED_SUCCESS"));
      }
    } catch (error) {
      toast.error(t("SAVE_ADMIN_NOTES_ERROR"));
    }
    setIsUpdating(false);
  };

  const getImpactLevelColor = (level) => {
    switch (level) {
      case "high": return "red";
      case "medium": return "orange";
      case "low": return "green";
      default: return "default";
    }
  };

  const renderMediaContent = () => {
    if (!detailData) return null;

    const { imageIds = [], videoId } = detailData;

    return (
      <div className="media-content">
        {imageIds.length > 0 && (
          <div className="images-section">
            <Title level={5}>{t("ATTACHED_IMAGES")} ({imageIds.length})</Title>
            <div className="images-gallery">
              <Image.PreviewGroup>
                {imageIds.map((image, index) => (
                  <Image
                    key={image._id || index}
                    width={120}
                    height={120}
                    src={image.url || image.path}
                    alt={`Error image ${index + 1}`}
                    className="error-image"
                    style={{ objectFit: 'cover', borderRadius: '8px' }}
                  />
                ))}
              </Image.PreviewGroup>
            </div>
          </div>
        )}

        {videoId && (
          <div className="video-section">
            <Title level={5}>{t("ATTACHED_VIDEO")}</Title>
            <div className="video-player">
              <video
                controls
                width="100%"
                height="300"
                src={videoId.url || videoId.path}
                className="error-video"
              >
                {t("VIDEO_NOT_SUPPORTED")}
              </video>
            </div>
          </div>
        )}

        {imageIds.length === 0 && !videoId && (
          <div className="no-media">
            <Text type="secondary">{t("NO_MEDIA_ATTACHED")}</Text>
          </div>
        )}
      </div>
    );
  };

  if (!detailData) {
    return (
      <AntModal
        open={open}
        onCancel={onClose}
        title={t("ERROR_REPORT_DETAILS")}
        width={1000}
        footer={null}
      >
        <Loading active={isLoading} transparent>
          <div style={{ height: 200 }} />
        </Loading>
      </AntModal>
    );
  }

  return (
    <AntModal
      open={open}
      onCancel={onClose}
      title={t("ERROR_REPORT_DETAILS")}
      width={1200}
      footer={null}
      className="error-report-detail-modal"
    >
      <Loading active={isLoading || isUpdating} transparent>
        <div className="error-report-detail">
          {/* Header với thông tin cơ bản */}
          <Card className="basic-info-card" size="small">
            <Row gutter={24}>
              <Col span={12}>
                <Descriptions column={1} size="small">
                  <Descriptions.Item label={t("ERROR_ID")}>
                    <Text code>{detailData._id}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label={t("USER")}>
                    <div>
                      <div><strong>{detailData.userId?.email}</strong></div>
                      <div><Text type="secondary">{detailData.userId?.fullName}</Text></div>
                    </div>
                  </Descriptions.Item>
                  <Descriptions.Item label={t("IMPACT_LEVEL")}>
                    <Tag color={getImpactLevelColor(detailData.impactLevel)}>
                      {t(`IMPACT_${detailData.impactLevel?.toUpperCase()}`)}
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>
              </Col>
              <Col span={12}>
                <Descriptions column={1} size="small">
                  <Descriptions.Item label={t("CREATED_AT")}>
                    {formatDate(detailData.createdAt)}
                  </Descriptions.Item>
                  <Descriptions.Item label={t("UPDATED_AT")}>
                    {formatDate(detailData.updatedAt)}
                  </Descriptions.Item>
                  <Descriptions.Item label={t("ERROR_URL")}>
                    <a href={detailData.errorUrl} target="_blank" rel="noopener noreferrer">
                      {detailData.errorUrl}
                    </a>
                  </Descriptions.Item>
                </Descriptions>
              </Col>
            </Row>
          </Card>

          {/* Mô tả lỗi */}
          <Card title={t("ERROR_DESCRIPTION")} size="small" className="description-card">
            <Paragraph>{detailData.description}</Paragraph>
          </Card>

          {/* Media content */}
          <Card title={t("MEDIA_CONTENT")} size="small" className="media-card">
            {renderMediaContent()}
          </Card>

          {/* Admin actions */}
          <Card title={t("ADMIN_ACTIONS")} size="small" className="admin-actions-card">
            <Row gutter={24}>
              <Col span={12}>
                <div className="action-item">
                  <Text strong>{t("NOTIFICATION_STATUS")}: </Text>
                  <Switch
                    checked={detailData.notified}
                    onChange={(checked) => handleUpdateStatus('notified', checked)}
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                  />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {detailData.notified ? t("NOTIFIED") : t("NOT_NOTIFIED")}
                  </Text>
                </div>
              </Col>
            </Row>

            <div className="admin-notes-section" style={{ marginTop: 16 }}>
              <Text strong>{t("ADMIN_NOTES")}:</Text>
              <TextArea
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                placeholder={t("ADD_ADMIN_NOTES_PLACEHOLDER")}
                rows={4}
                style={{ marginTop: 8 }}
              />
              <div style={{ marginTop: 8, textAlign: 'right' }}>
                <AntButton
                  type={BUTTON.DEEP_NAVY}
                  onClick={handleSaveNotes}
                  disabled={adminNotes === (detailData.adminNotes || "")}
                >
                  {t("SAVE_NOTES")}
                </AntButton>
              </div>
            </div>
          </Card>
        </div>
      </Loading>
    </AntModal>
  );
}

export default ErrorReportDetail;
