import React from "react";
import { Dropdown } from "antd";

import AntButton from "@component/AntButton";
import OutlineList from "@app/pages/Project/ProjectDetail/Outline/OutlineList";

import Category24 from "@component/SvgIcons/Category/Category24";

import "./OutlineMobile.scss";


function OutlineMobile({ ...props }) {
  
  const [isOpenInfoDropdown, setOpenInfoDropdown] = React.useState(false);
  
  
  function onOpenChange(open, { source }) {
    if (source === "trigger") setOpenInfoDropdown(open);
  }
  
  return <div className="project__outline-mobile">
    
    <Dropdown
      trigger="click"
      onOpenChange={onOpenChange}
      open={isOpenInfoDropdown}
      overlayClassName="project__outline-mobile-dropdown"
      menu={{ items: [{ key: 1, label: <OutlineList /> }] }}
    >
      <AntButton
        size="large"
        className="toggle-outline-mobile"
        icon={<Category24 />}
      />
    </Dropdown>
  
  
  </div>;
}

export default OutlineMobile;