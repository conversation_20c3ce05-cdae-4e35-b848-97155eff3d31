import React from "react";
import { useTranslation } from "react-i18next";

import "./AbcdAnswer.scss";

function AbcdAnswer({ correctAnswers }) {
  const { t } = useTranslation();
  
  return <div className="abcd-output__answer-list">
    {correctAnswers?.map(correctAnswer => {
      return <div key={correctAnswer.questionId} className="abcd-output__answer-item">
        <div className="abcd-output__answer-index">
          <span className="js-index-question-margin">{correctAnswer.questionId}</span>
        </div>
        <div className="abcd-output__answer-text">{correctAnswer.correctAnswer}</div>
      </div>;
    })}
  </div>
}

export default AbcdAnswer;