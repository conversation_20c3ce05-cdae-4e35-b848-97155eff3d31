.customer-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .customer-info-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }

    .customer-info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .customer-title {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 8px;
        color: var(--typo-colours-primary-black);
      }

      .customer-description {
        font-size: 14px;
        color: var(--typo-colours-secondary-grey);
        margin-bottom: 0;
      }
    }
  }

  .search-buttons {
    display: flex;
    flex-direction: row;
    gap: 18px;
    justify-content: flex-end;
  }

  .customer-table-card {
    margin-top: 16px;
  }
}