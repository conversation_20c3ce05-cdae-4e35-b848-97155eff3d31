import { Table } from "antd";
import { useTranslation } from 'react-i18next';
import clsx from "clsx";

import { CONSTANT } from "@constant";

import { renderMoney } from "@src/common/functionCommons";

import DOCUMENT_ICON from '@src/asset/icon/document/document.svg';

const TransactionList = ({ ...props }) => {
  const { t } = useTranslation();
  const { transactionData, formatDate, transactionActive, onChangeTransaction, renderTransactionStatus } = props;

  const columns = [
    {
      title: t("REGISTRATION_PACKAGE"),
      key: "Registration package",
      render: (text, record) => {
        const packageName = record.subscriptionId?.packageId?.name;
        const unitPrice = record.subscriptionId?.unitPrice;
        const packageTerm = unitPrice?.toUpperCase() === CONSTANT.MONTH ? t('MONTHLY') : t('YEARLY');
        return <div className="package-info">
          {packageName}
          <span className="package-info__unit-price">{packageTerm}</span>
        </div>
      },
    },
    {
      title: t('TOTAL_AMOUNT'),
      key: "TOTAL_AMOUNT",
      dataIndex: "cost",
      render: (value) => {
        return renderMoney(value);
      },
    },
    {
      title: t('TRANSACTION_DATE'),
      key: "TRANSACTION_DATE",
      render: (text, record) => {
        return formatDate(record?.createdAt);
      },
    },
    {
      title: t('TRANSACTION_STATUS'),
      key: "TRANSACTION_STATUS",
      render: (value, record) => {
        return renderTransactionStatus(record?.state, record?.responseCode);
      },
    },
  ];

  return <div className={clsx("transaction-list", !transactionData?.length && "width-full")}>
    <div className="transaction-list__title">
      <div className="title__icon">
        <img src={DOCUMENT_ICON} alt="" />
      </div>
      {t("HISTORY_TRANSACTION_LIST")}
    </div>
    <Table
      columns={columns}
      dataSource={transactionData}
      pagination={false}
      rowClassName={record => record?._id === transactionActive?._id ? "transaction-selected" : ""}
      onRow={record => {
        return {
          onClick: () => {
            onChangeTransaction(record);
          },
        };
      }}
    />
  </div>
};

export default TransactionList;