.homepage-section {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .homepage-section__header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .homepage-section__header-left {
      display: flex;
      gap: 16px;
      height: fit-content;
      align-items: center;

      .homepage-section__header-icon {
        display: flex;
        height: 25px;
        width: 24px;
        align-items: center;

        img {
          height: 24px;
          width: 24px;
        }
      }

      .homepage-section__title {
        .homepage-section__title-text {
          display: inline-block;
          color: var(--typo-colours-primary-black);
          font-size: 20px;
          font-weight: 600;
        }

        .homepage-section__info {
          display: inline-block;
          color: var(--typo-colours-support-blue-light);
          font-size: 14px;
        }
      }
    }

    .homepage-section__header-right {
      height: 40px;
      position: relative;
    }
  }
}