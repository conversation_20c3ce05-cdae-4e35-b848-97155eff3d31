import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Card, Col, Form, Input, Row, Tooltip } from "antd";
import { EditOutlined, PlusOutlined, SearchOutlined, InfoCircleOutlined } from "@ant-design/icons";

import { BUTTON, INPUT_TYPE, PAGINATION_INIT } from "@constant";

import GPTDetailModal from "./GPTDetailModal";

import { createGptModel, deleteGptModel, getGptModel, updateGptModel } from "@services/GPTModelPrice";
import { getAllAPIKeys } from "@services/APIKey";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import AntButton from "@component/AntButton";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import "./ModelGPT.scss";

const GPTModel = ({ ...props }) => {
  const { t } = useTranslation();
  const [gptModelData, setGPTModelData] = useState([]);
  const [apiKeyData, setAPIKeyData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [formSearch] = Form.useForm();
  const [modalState, setModalState] = useState({
    isShowModal: false,
    gptModel: null,
  });

  useEffect(() => {
    Promise.all([
        getGptModelData(),
        getAPIKeyData(),
      ],
    );
  }, []);

  async function getGptModelData() {
    setLoading(true);
    const apiResponse = await getGptModel();
    if (apiResponse) {
      setGPTModelData(apiResponse);
    }
    setLoading(false);
  }

  async function getAPIKeyData() {
    const apiResponse = await getAllAPIKeys();
    if (apiResponse) {
      setAPIKeyData(apiResponse);
    }
  }

  const handleEdit = (modelSelected) => {
    setModalState({
      isShowModal: true,
      gptModel: modelSelected,
    });
  };

  const openModalCreate = () => {
    setModalState({ isShowModal: true, gptModel: null });
  };

  const handleDelete = (gptModelId, modelName) => {
    confirm.delete({
      title: t("DELETE_GPT_MODEL"),
      content: t("CONFIRM_DELETE_GPT_MODEL", { model: modelName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async (e) => {
        setLoading(true);
        const apiResponse = await deleteGptModel(gptModelId, true);
        if (apiResponse) {
          toast.success(t("DELETE_GPT_MODEL_SUCCESS"));
          await getGptModelData();
        } else {
          toast.error(t("DELETE_GPT_MODEL_ERROR"));
          setLoading(false);
        }
      },
    });
  };

  const handleCloseModal = () => {
    setModalState({
      isShowModal: false,
      option: null,
    });
  };

  const handleSubmitOption = async (values) => {
    setLoading(true);
    const dataRequest = values;
    let dataResponse;
    if (values._id) {
      dataResponse = await updateGptModel(dataRequest, true);
    } else {
      dataResponse = await createGptModel(dataRequest, true);
    }
    if (dataResponse) {
      if (values._id) {
        toast.success(t("UPDATE_GPT_MODEL_SUCCESS"));
      } else {
        toast.success(t("CREATE_GPT_MODEL_SUCCESS"));
      }
      await getGptModelData();
      handleCloseModal();
    } else {
      if (values._id) {
        toast.error(t("UPDATE_GPT_MODEL_ERROR"));
      } else {
        toast.error(t("CREATE_GPT_MODEL_ERROR"));
      }
      setLoading(false);
    }
  };

  const submitFormFilter = (values) => {
    const filteredData = gptModelData.filter(model => {
      if (!values.modelName) return true;
      return model.gptModel.toLowerCase().includes(values.modelName.toLowerCase());
    });
    setGPTModelData(filteredData);
  };

  const onClearFilter = () => {
    formSearch.resetFields();
    getGptModelData();
  };

  function renderAPIKeys(value) {
    if (!value) return null;
    return value.map((item, index) => (
      <span key={index}>
            {item?.apiKey} {index < value.length - 1 && ", "}
        </span>
    ));
  }

  const columns = [
    {
      title: t("ORDER"),
      align: "center",
      width: 80,
      render: (value, row, index) => index + 1,
    },
    {
      title: t("MODEL_NAME"),
      dataIndex: "gptModel",
      width: "15%",
      ellipsis: true,
      render: (text) => <span className="model-name-value">{text}</span>,
    },
    {
      title: t("API_KEYS"),
      dataIndex: "apiKeyIds",
      render: (value) => renderAPIKeys(value),
      width: "25%",
      ellipsis: true,
    },
    {
      title: t("TOKEN_UNIT"),
      dataIndex: "tokenUnit",
      width: "10%",
      align: "center",
      ellipsis: true,
    },
    {
      title: t("UNIT"),
      dataIndex: "unit",
      width: "10%",
      align: "center",
      ellipsis: true,
    },
    {
      title: t("INPUT_PRICE"),
      dataIndex: "priceInput",
      width: "10%",
      align: "center",
      sorter: (a, b) => a.priceInput - b.priceInput,
      ellipsis: true,
    },
    {
      title: t("OUTPUT_PRICE"),
      dataIndex: "priceOutput",
      width: "10%",
      align: "center",
      sorter: (a, b) => a.priceInput - b.priceInput,
      ellipsis: true,
    },
    {
      title: t("MAX_TOKENS"),
      dataIndex: "maxTokens",
      width: "10%",
      align: "center",
      ellipsis: true,
    },
    {
      title: t("ACTION"),
      key: "action",
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className={"gpt-model-actions"}>
          <Tooltip title={t("EDIT_GPT_MODEL")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-edit-gpt-model"}
              icon={<EditOutlined/>}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>

          <Tooltip title={t("DELETE_GPT_MODEL")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-gpt-model"}
              icon={<DeleteIcon/>}
              onClick={() => handleDelete(record._id, record.gptModel)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <Loading active={loading} transparent>
      <div className="gpt-model-container">
        <Card className="gpt-model-info-card">
          <div className="gpt-model-info-header">
            <div>
              <h1 className="gpt-model-title">{t("GPT_MODEL_MANAGEMENT")}</h1>
              <p className="gpt-model-description">{t("GPT_MODEL_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <AntButton
              type={BUTTON.DEEP_NAVY}
              size={"large"}
              icon={<PlusOutlined/>}
              onClick={openModalCreate}
              className={"btn-create-gpt-model"}
            >
              {t("CREATE_GPT_MODEL")}
            </AntButton>
          </div>
        </Card>

        <Card className="gpt-model-search-card">
          <AntForm form={formSearch} layout="horizontal" size={"large"} className="form-filter" onFinish={submitFormFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={12} lg={16}>
                <AntForm.Item name="modelName" className="search-form-item" style={{ marginBottom: 0 }}>
                  <Input
                    placeholder={t("SEARCH_MODEL_NAME_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={12} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton size={"large"} type={BUTTON.GHOST_WHITE} onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton size={"large"} htmlType={"submit"} type={BUTTON.DEEP_NAVY}>
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="gpt-model-table-card">
          <TableAdmin
            columns={columns}
            dataSource={gptModelData}
            pagination={true}
            className={"gpt-model-table"}
            scroll={{ x: "max-content" }}
            rowClassName={() => "gpt-model-table-row"}
            locale={{ emptyText: t("NO_GPT_MODEL_FOUND") }}
          />
        </Card>

        <GPTDetailModal
          isShowModal={modalState.isShowModal}
          gptModel={modalState.gptModel}
          apiKeyData={apiKeyData}
          handleCancel={handleCloseModal}
          handleOk={handleSubmitOption}
        />
      </div>
    </Loading>
  );
};

export default GPTModel;
