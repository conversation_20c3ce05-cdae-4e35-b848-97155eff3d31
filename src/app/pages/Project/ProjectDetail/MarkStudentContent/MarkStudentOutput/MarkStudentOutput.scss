@import "src/app/styles/scroll";

.mark-student-output {
  display: flex;
  flex-direction: row;
  gap: 24px;

  .mark-student-output__title {
    font-weight: 600;
    text-align: center;
    font-size: 1.5rem;
  }

  .mark-student-output__assignment {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 55%;

    .ck-content {
      @extend .scrollbar;
      @extend .scrollbar-show;

      max-height: calc(100vh - 298px);

      p {
        line-height: 1.6875rem;
      }
    }

    span[style="color:white;"] {
      border-radius: 4px;
      padding: 4px 6px;
      background-color: var(--typo-colours-support-blue);
    }

    .mark-student-output__save-btn {
      margin: 0 auto;
    }
  }

  .mark-student-output__evaluation {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 45%;

    .evaluation__content {
      display: flex;
      flex-direction: column;
      gap: 24px;
      max-height: calc(100vh - 182px);
      padding: 16px;
      line-height: 1.6875rem;
      border: 1px solid var(--lighttheme-content-background-stroke);
      border-radius: 4px;

      @extend .scrollbar;
      @extend .scrollbar-show;

      ul {
        margin-top: 4px;
        margin-bottom: 0;

        li {
          margin-bottom: 4px;
        }
      }

      .evaluation__content__item {
        display: flex;
        flex-direction: column;
        gap: 16px;
        justify-content: center;

        .content__item__title {
          margin: auto;
          padding: 15px;
          border-radius: 12px;
          font-weight: 600;
          color: white;
          background-color: var(--typo-colours-support-blue);
        }
      }

      .evaluation-suggest {
        .suggest-item {
          padding: 24px;
          border: #d6d9dd solid 1px;
          border-radius: 8px;
          cursor: pointer;

          .suggest-item__text {
            color: var(--typo-colours-support-blue);
          }

          &:hover {
            &:not(.suggest-item-active) {
              background-color: var(--background-light-background-1);
            }
          }

          &.suggest-item-active {
            display: flex;
            flex-direction: column;
            cursor: default;

            .suggest-item__suggestion {
              background: var(--typo-colours-support-purple);
              border-radius: .25rem;
              color: #fff;
              padding: .25rem .5rem;
              transition-duration: .2s;
              cursor: pointer;
            }
          }
        }
      }

      .evaluation-essay-assessment {
        .essay-assessment__item-title {
          font-weight: 600;
        }
      }

      .evaluation-vocabulary {
        .vocabulary-item {
          line-height: 1.6875rem;

          .vocabulary-item__word {
            font-weight: 600;
          }
        }
      }

      .evaluation-criteria-assessment {
        .criteria-assessment__item-title {
          font-weight: 600;

          &::after {
            content: ' ';
          }
        }
      }

      .evaluation-improved-essay {
        p {
          white-space: pre-wrap;
        }
      }
    }
  }
}