import React, { useEffect, useState } from "react";

import Loading from "@component/Loading";
import PreviewPdf from "./PreviewPdf";

import { getFileById } from "@services/File";

import SubmissionFile from "@src/asset/icon/submission-file.svg";
import ERROR_ICON from "@src/asset/icon/error/error-triangle.svg";

import "./PreviewPdfEssay.scss";

function PreviewPdfEssay(props) {

  const { fileId, pageRange, isResult } = props;
  const { startPage, endPage } = pageRange;

  const [pdfData, setPdfData] = useState(null);

  const [isLoading, setLoading] = useState(false);
  const [isError, setError] = useState(false);

  useEffect(() => {
    if (fileId) {
      getPreviewPdf();
    }
  }, [fileId]);

  async function getPreviewPdf() {
    setPdfData(null);
    setLoading(true);
    setError(false);

    const apiResponse = await getFileById(fileId);
    if (apiResponse) {
      setPdfData(apiResponse);
    } else {
      setError(true);
    }
    setLoading(false);
  }

  function renderPreview() {
    if (isLoading) return null;

    if (pdfData) {
      return <PreviewPdf
        file={pdfData}
        startPage={startPage}
        endPage={endPage}
        isResult={isResult}
      />;
    }

    if (isError) {
      return <div className="preview-pdf-essay-error">
        <img src={ERROR_ICON} alt="" />
      </div>;
    }
  }

  if (!fileId) return null;

  return <Loading active={isLoading} className="preview-pdf-essay">
    {renderPreview()}
  </Loading>;
}

export default PreviewPdfEssay;