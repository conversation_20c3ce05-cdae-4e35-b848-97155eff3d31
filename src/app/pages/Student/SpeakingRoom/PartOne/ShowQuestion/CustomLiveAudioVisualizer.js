import React, { useRef, useEffect } from 'react';

const CustomLiveAudioVisualizer = ({
  audioData,
  width = 240,
  height = 80,
  barWidth = 8,
  gap = 8,
  barColor = "#1890ff",
  backgroundColor = 'transparent',
  smoothingTimeConstant = 0.6,
  amplificationFactor = 2.0, // Thê<PERSON> hệ số khuếch đại để tăng độ nhạy
}) => {
  const canvasRef = useRef(null);
  const audioDataRef = useRef(audioData);
  const animationFrameIdRef = useRef(null);
  const displayedBarHeightsRef = useRef([]);

  useEffect(() => {
    audioDataRef.current = audioData;
  }, [audioData]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');

    const lerp = (start, end, amt) => (1 - amt) * start + amt * end;

    const drawRoundedBar = (x, y, w, h, r, color) => {
      ctx.beginPath();
      ctx.moveTo(x + r, y);
      ctx.lineTo(x + w - r, y);
      ctx.quadraticCurveTo(x + w, y, x + w, y + r);
      ctx.lineTo(x + w, y + h - r);
      ctx.quadraticCurveTo(x + w, y + h, x + w - r, y + h);
      ctx.lineTo(x + r, y + h);
      ctx.quadraticCurveTo(x, y + h, x, y + h - r);
      ctx.lineTo(x, y + r);
      ctx.quadraticCurveTo(x, y, x + r, y);
      ctx.closePath();
      ctx.fillStyle = color;
      ctx.fill();
    };

    const draw = () => {
      ctx.clearRect(0, 0, width, height);

      if (backgroundColor !== 'transparent') {
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, width, height);
      }

      const currentData = audioDataRef.current;
      const silenceThreshold = 0.005;
      let isSilent = true;

      if (currentData && currentData.length > 0) {
        for (let i = 0; i < currentData.length; i++) {
          if (Math.abs(currentData[i]) > silenceThreshold) {
            isSilent = false;
            break;
          }
        }
      }

      const numBars = Math.floor((width - gap) / (barWidth + gap));
      if (numBars <= 0) {
        animationFrameIdRef.current = requestAnimationFrame(draw);
        return;
      }

      if (displayedBarHeightsRef.current.length !== numBars) {
        displayedBarHeightsRef.current = new Array(numBars).fill(0);
      }

      const dataPointsPerBar =
        isSilent || !currentData ? 0 : Math.floor(currentData.length / numBars);

      const centerX = width / 2;
      const halfNumBars = Math.floor(numBars / 2);

      for (let i = 0; i < numBars; i++) {
        let targetHeight;

        if (isSilent || !currentData || dataPointsPerBar === 0) {
          targetHeight = barWidth;
        } else {
          let maxVal = 0;
          const start = i * dataPointsPerBar;
          const end = Math.min(start + dataPointsPerBar, currentData.length);

          for (let j = start; j < end; j++) {
            const val = Math.abs(currentData[j]);
            if (val > maxVal) maxVal = val;
          }

          // Áp dụng hệ số khuếch đại và cải thiện độ nhạy
          targetHeight = Math.pow(maxVal, 0.7) * height * amplificationFactor;
          if (targetHeight > 0 && targetHeight < 2) targetHeight = barWidth;
          targetHeight = Math.min(targetHeight, height);
        }

        displayedBarHeightsRef.current[i] = lerp(
          displayedBarHeightsRef.current[i] || 0,
          targetHeight,
          smoothingTimeConstant
        );

        const h = displayedBarHeightsRef.current[i];
        if (h >= 0.5) {
          const direction = i < halfNumBars ? -1 : 1;
          const offsetIndex = i < halfNumBars
            ? halfNumBars - 1 - i
            : i - halfNumBars;
          const x = centerX + direction * offsetIndex * (barWidth + gap) - barWidth / 2;
          const y = (height - h) / 2;
          const radius = barWidth / 2 // Math.min(2, barWidth / 2, h / 2);

          drawRoundedBar(x, y, barWidth, h, radius, barColor);
        }
      }

      animationFrameIdRef.current = requestAnimationFrame(draw);
    };

    draw();

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
    };
  }, [width, height, barWidth, gap, barColor, backgroundColor, smoothingTimeConstant]);

  return <canvas ref={canvasRef} width={width} height={height} />;
};

export default CustomLiveAudioVisualizer;
