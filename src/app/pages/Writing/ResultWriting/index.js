import React, { useEffect, useMemo, useState } from "react";
import { connect, useDispatch } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import Loading from "@src/app/component/Loading";
import StudentBreadcrumb from "@app/layout/StudentLayout/StudentBreadcrumb";
import ResultInput from "./ResultInput";
import ResultOutput from "./ResultOutput";
import GoBack from "./GoBack";
import ResultHeader from "./ResultHeader";

import { getProjectDetail } from "@src/app/services/Project";

import { CONSTANT, PERMISSION, TYPE_OF_TOOL } from "@constant";

import "./ResultWriting.scss";
import NeedAccess from "@src/app/component/NeedAccess";
import NoData from "@src/app/component/NoData";
import { checkAutoFeedback } from "@src/app/services/Feedback";
import FeedbackModal from "@src/app/component/FeedbackModal";
import { actions, paramsCreators } from "@src/ducks/tracking.duck";

const ResultWriting = ({ studentTools, ...props }) => {
  const location = useLocation();
  const projectId = useParams()?.id;
  const { writing: writingTools } = studentTools || {};
  const { t } = useTranslation();

  const [projectData, setProjectData] = useState({});
  const [isLoading, setLoading] = useState(false);
  const [essayText, setEssayText] = useState("");
  const [projectName, setProjectName] = useState('');
  const [isShowFeedback, setShowFeedback] = useState(false);
  const dispatch = useDispatch();

  const { permission } = projectData;
  const disabledEdit = permission === PERMISSION.VIEWER;

  useEffect(() => {
    if (location.state?.data) {
      setProjectName(location.state.data?.projectName);
      if (projectData?._id !== location.state.data?._id) {
        setProjectData(location.state.data);
      }
    } else {
      handleGetProjectData();
    }
  }, [projectId, location.state]);

  const { response, input, output } = useMemo(() => {
    const responseData = projectData?.content?.[0]?.responses.find(response => response.isActivate);
    if (!responseData) return {};

    const inputData = responseData?.inputId?.inputData;
    const outputData = responseData?.output;
    return ({ response: responseData, input: inputData, output: outputData });
  }, [projectData?.content?.[0]?.responses]);

  useEffect(() => {
    if (output) {
      setEssayText(output?.inputText?.replace(/\n/g, "<br>"));
    }
  }, [output]);
  
  useEffect(() => {
    projectData?.content?.[0] && dispatch(actions.trackCustomViewStudyHub(paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name)));
  }, [projectData]);
  
  const isShowTopicImage = useMemo(() => {
    const toolId = response?.toolId;
    const toolData = writingTools?.find(tool => tool._id === toolId);
    return toolData?.inputType === TYPE_OF_TOOL.MARK_TEST_TASK_1 || toolData?.inputType === TYPE_OF_TOOL.STUDENT_TASK_1;
  }, [writingTools, response]);

  const isShowGoBack = useMemo(() => {
    const { state, evaluation } = output || {};
    return state === 'invalid' || ['Cannot evaluate without content!', 'Không thể đánh giá bài khi không tìm thấy nội dung'].includes(evaluation);
  }, [output]);

  useEffect(() => {
    const { checkFeedback } = location.state?.data || {};
    if (checkFeedback && !isShowGoBack) {
      setTimeout(() => {
        checkShowFeedback();
      }, [10000]);
    }
  }, [location.state, isShowGoBack]);

  const checkShowFeedback = async () => {
    const response = await checkAutoFeedback();
    if (response?.showFeedback) setShowFeedback(true);
  }

  async function handleGetProjectData() {
    setLoading(true);
    const projectResponse = await getProjectDetail(projectId);
    if (projectResponse) {
      switch (projectResponse.code) {
        case 200:
          setProjectData(projectResponse.data);
          setProjectName(projectResponse.data.projectName);
          break;
        case 403:
          setProjectData({ permission: PERMISSION.NO_PERMISSION });
          break;
        case 404:
          setProjectData({ permission: CONSTANT.NOT_FOUND });
          break;
      }
    }
    setLoading(false);
  }

  if (permission === PERMISSION.NO_PERMISSION) {
    return <NeedAccess />;
  }

  if (permission === CONSTANT.NOT_FOUND) {
    return <NoData>{t("DATA_NOT_FOUND")}</NoData>;
  }

  return (
    <Loading className="result-writing-container" active={isLoading}>
      <StudentBreadcrumb />
      <div className="result-writing-inner">
        <ResultHeader
          projectData={projectData}
          projectName={projectName}
          setProjectName={setProjectName}
          disabledEdit={disabledEdit}
        />
        <div className="result-writing__content">
          <ResultInput
            inputData={input}
            category={output?.category}
            tag={output?.tag}
            essayText={essayText}
            isShowTopicImage={isShowTopicImage}
          />
          {isShowGoBack
            ? <GoBack
              projectData={{ ...projectData, contentId: projectData?.content?.[0]?._id }}
              disabledEdit={disabledEdit} />
            : <ResultOutput
              projectData={projectData}
              output={output}
              essayText={essayText}
              setEssayText={setEssayText}
            />
          }
        </div>
      </div>
      {isShowFeedback && <FeedbackModal toolId={response.toolId} projectId={projectId}/>}
    </Loading>
  );
};


const mapStateToProps = (state) => ({
  studentTools: state.tool.studentTools,
});

export default (connect(mapStateToProps, {})(ResultWriting));