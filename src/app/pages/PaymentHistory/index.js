import { useState, useEffect } from 'react';
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import dayjs from 'dayjs';

import SubcriptionInfo from '././SubcriptionInfo';
import TransactionDetail from './TransactionDetail';
import TransactionList from './TransactionList';

import { getAllTransactionByUser } from '@services/Transaction';

import { CONSTANT, LANGUAGE, TRANSACTION_STATUS } from "@constant";

import './PaymentHistory.scss';

const PaymentHistory = ({ user, ...props }) => {
  const { t } = useTranslation();
  const { subscription } = user;

  const [transactionData, setTransactionData] = useState([]);
  const [transactionActive, setTransactionActive] = useState();

  useEffect(() => {
    getTransactionData();
  }, []);

  const getTransactionData = async () => {
    const dataResponse = await getAllTransactionByUser();
    if (dataResponse) {
      setTransactionData(dataResponse.map(transaction => ({ ...transaction, key: transaction._id })));
      setTransactionActive(dataResponse[0]);
    }
  };

  const onChangeTransaction = (transaction) => {
    setTransactionActive(transaction);
  }

  const formatDate = (dateTime) => {
    const formatString = dayjs.locale() === LANGUAGE.EN ? "MMM-DD-YYYY" : "DD-MM-YYYY";
    return dayjs(dateTime).format(formatString);
  }

  const renderTransactionStatus = (status, responseCode) => {
    switch (status) {
      case TRANSACTION_STATUS.DONE: return <span className="transaction-status-done">{t("SUCCESSFUL_TRANSACTION")}</span>
      case TRANSACTION_STATUS.ERROR: if (responseCode === "24") {
        return <span className="transaction-status-cancel">{t("TRANSACTION_CANCELED")}</span>
      } else return <span className="transaction-status-error">{t("TRANSACTION_FAILED")}</span>
      default: return <span className="transaction-status-processing">{t("TRANSACTION_PROCESSING")}</span>
    }
  }

  return <div className="payment-history-container">
    <SubcriptionInfo subscription={subscription} formatDate={formatDate} />
    <div className="transaction-history">
      <TransactionList
        transactionData={transactionData}
        onChangeTransaction={onChangeTransaction}
        formatDate={formatDate}
        transactionActive={transactionActive}
        renderTransactionStatus={renderTransactionStatus}
      />
      {transactionActive && <TransactionDetail transactionActive={transactionActive} renderTransactionStatus={renderTransactionStatus} />}
    </div>
  </div>
}

const mapStateToProps = (state) => {
  const { user } = state.auth;
  return { user };
}
export default connect(mapStateToProps)(PaymentHistory);