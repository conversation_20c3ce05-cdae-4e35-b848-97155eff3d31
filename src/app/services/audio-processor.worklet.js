class AudioProcessor extends AudioWorkletProcessor {
  process(inputs, outputs, parameters) {
    const input = inputs[0];
    const output = outputs[0];

    if (input.length > 0) {
      const inputChannel = input[0];
      // <PERSON><PERSON><PERSON> dữ liệu PCM thô trở lại luồng chính
      this.port.postMessage(inputChannel);
    }

    return true;
  }
}

registerProcessor('audio-processor', AudioProcessor);