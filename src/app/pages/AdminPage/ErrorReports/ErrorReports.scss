.error-reports-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .error-reports-info-card {
    margin-bottom: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .error-reports-info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .error-reports-title {
        margin: 0;
        color: #1a1a1a;
        font-size: 28px;
        font-weight: 600;
      }

      .error-reports-description {
        margin: 8px 0 0 0;
        color: #666;
        font-size: 16px;
      }
    }
  }

  .error-reports-search-card {
    margin-bottom: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .form-filter {
      .search-form-item {
        margin-bottom: 0;
      }

      .search-buttons-col {
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;

        .search-buttons {
          display: flex;
          gap: 12px;
        }
      }
    }
  }

  .error-reports-table-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .error-reports-table {
      .error-id {
        font-family: 'Courier New', monospace;
        background-color: #f0f0f0;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
      }

      .error-description {
        line-height: 1.4;
        color: #333;
      }

      .error-url {
        color: #1890ff;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }

      .error-actions {
        display: flex;
        gap: 8px;
        justify-content: center;

        .ant-btn {
          border-radius: 6px;
          
          &.notified {
            background-color: #f6ffed;
            border-color: #b7eb8f;
            color: #52c41a;
          }

          &.not-notified {
            background-color: #fff2e8;
            border-color: #ffbb96;
            color: #fa8c16;
          }

          &.btn-delete {
            &:hover {
              background-color: #fff2f0;
              border-color: #ffccc7;
              color: #ff4d4f;
            }
          }
        }
      }

      .ant-table-tbody > tr:hover > td {
        background-color: #f8f9fa !important;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 16px;

    .error-reports-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .search-buttons-col {
      .search-buttons {
        width: 100%;
        
        .ant-btn {
          flex: 1;
        }
      }
    }
  }
}

// Global styles for error reports
.ant-tag {
  &.ant-tag-red {
    background-color: #fff2f0;
    border-color: #ffccc7;
    color: #cf1322;
  }

  &.ant-tag-orange {
    background-color: #fff7e6;
    border-color: #ffd591;
    color: #d46b08;
  }

  &.ant-tag-green {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #389e0d;
  }
}

// Loading overlay customization
.ant-spin-container {
  position: relative;
}

.ant-spin-spinning {
  .error-reports-table {
    opacity: 0.7;
    pointer-events: none;
  }
}
