const workerCode = () => {
  const TYPING_SLOW = 3;  // bigger is slower
  let timerInterval;
  
  self.onmessage = function ({ data: { status, index } }) {
    if (status === "STOP") {
      clearInterval(timerInterval);
    }
    if (status === "START") {
      timerInterval = setTimeout(() => {
        self.postMessage({ index: 0 });
      }, TYPING_SLOW);
    }
    if (status === "NEXT") {
      setTimeout(() => {
        self.postMessage({ index });
      }, TYPING_SLOW);
    }
  };
};

let code = workerCode.toString();
code = code.substring(code.indexOf("{") + 1, code.lastIndexOf("}"));

const blob = new Blob([code], { type: "application/javascript" });
const worker_script = URL.createObjectURL(blob);

module.exports = worker_script;
