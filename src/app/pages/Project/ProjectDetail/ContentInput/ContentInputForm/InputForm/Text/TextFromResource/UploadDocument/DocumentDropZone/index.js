import React, { useCallback, useMemo } from "react";
import { useDropzone } from "react-dropzone";
import { useTranslation } from "react-i18next";

import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";

import { checkFileType } from "@common/functionCommons";

import Upload40 from "@component/SvgIcons/Upload/Upload40";

import "./DocumentDropZone.scss";

const acceptImg = ["jpg", "png"];
const acceptDocument = ["pdf"];

function DocumentDropZone({ previewData, loadDocument, setError, ...props }) {
  const { t } = useTranslation();
  
  const handleDrop = useCallback(async (acceptedFiles) => {
    addFile(acceptedFiles[0]);
  }, []);
  
  const handlePaste = useCallback(async (event) => {
    const items = (event.clipboardData || event.originalEvent.clipboardData).items;
    addFile(items[0]);
  }, []);
  
  
  const accept = useMemo(() => {
    return [...acceptImg, ...acceptDocument]?.reduce(function (grouped, element) {
      grouped[`image/${element}`] = [`.${element}`];
      return grouped;
    }, {});
  }, []);
  
  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop: handleDrop,
    noClick: true,
    accept,
    multiple: false,
    //disabled,
  });
  
  
  const addFile = async (file) => {
    if (!file) return;
    
    const maxFileSize = 50 * 1024 * 1024;
    if (file.size >= maxFileSize) {
      return setError(t('FILE_EXCEEDS_MAX_FILE_SIZE'));
    }
    
    const fileType = checkFileType(file);
    if (fileType === CONSTANT.IMAGE) {
      handleLoadImage(file);
    } else if (fileType === CONSTANT.PDF) {
      handleLoadDocument(file);
    }
  };
  
  function handleLoadDocument(file) {
    const fileExtension = file?.name.split(".").pop().toLowerCase();
    if (fileExtension !== "pdf") {
      return setError(t('UPLOAD_FILE_IS_NOT_PDF'));
    }
    
    loadDocument({
      type: CONSTANT.DOCUMENT,
      file,
      blob: URL.createObjectURL(file),
    });
  }
  
  function handleLoadImage(file) {
    const fileExtension = file.name.split(".").pop().toLowerCase();
    if (!acceptImg.includes(fileExtension)) {
      return setError(t('UPLOAD_IMAGE_WRONG_FORMAT'));
    }
    
    loadDocument({
      type: CONSTANT.IMAGE,
      file,
      blob: URL.createObjectURL(file),
    });
  }
  
  const dropzoneHtml = <div className="document-drop-zone">
    
    <div {...getRootProps()} onPaste={handlePaste}>
      <input {...getInputProps()} />
      
      <div className="upload-image-content">
        <div className="upload-image-inner">
          <div className="">
            <Upload40 />
          </div>
          <div className="upload-image__title">
            {t('DROP_YOUR_FILE_HERE')}
          </div>
          <div className="upload-image__description">
            {t('WE_CAN_WORK_WITH')} JPG, PNG, PDF
          </div>
          <div className="upload-image__description">
            {t('DROPZONE_IMAGE_DESCRIPTION')}
          </div>
          
          <div className="upload-image__select-image">
            <AntButton
              size="large"
              type={BUTTON.DEEP_NAVY}
              onClick={open}
            >
              {t("SELECT_DOCUMENT")}
            </AntButton>
          </div>
        </div>
      </div>
    </div>
  </div>;
  
  if (!previewData?.type || previewData?.category !== CONSTANT.MY_COMPUTER) return dropzoneHtml;
  return null;
}

export default DocumentDropZone;