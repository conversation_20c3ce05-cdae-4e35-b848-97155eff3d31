.select-from-resource {
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media screen and (max-height: 720px) {
    gap: 16px
  }

  .resource-table-container {
    background-color: var(--background-light-background-1);
    padding: 24px 32px;
    display: flex;
    flex-direction: column;
    gap: 24px;

    .resource-search {
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .ant-table {

      .ant-table-selection-col,
      .ant-table-selection-column {
        background-color: red;
        display: none;
      }

      .ant-table-tbody tr td {
        cursor: pointer;
      }

      .audio-file-name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 200px;
        display: block;
      }

      .ant-table-container {
        max-height: calc(100vh - 490px);
        overflow-y: auto;

        @media screen and (max-height: 700px) {
          max-height: calc(100vh - 330px);
        }
      }
    }

    .select-resource__pagination {
      display: flex;
      justify-content: end;
    }
  }

  .resource-submit {
    display: flex;
    align-self: center;
    gap: 8px;
  }
}