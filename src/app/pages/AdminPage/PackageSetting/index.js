import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { Card, Col, Form, Input, Row, Tooltip } from "antd";
import { EditOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";

import AntButton from "@src/app/component/AntButton";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";

import { deletePackage, getAllPackage } from "@services/Package";
import { formatTimeDate, renderMoney } from "@src/common/functionCommons";

import { BUTTON } from "@constant";
import { LINK } from "@link";

import { toast } from "@src/app/component/ToastProvider";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import "./PackageSetting.scss";
import { confirm } from "@component/ConfirmProvider";


const PackageSetting = ({ ...props }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [formSearch] = Form.useForm();

  const [packageList, setPackageList] = useState([]);
  const [filteredPackageList, setFilteredPackageList] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    getPackagesData();
  }, []);

  useEffect(() => {
    if (searchQuery) {
      const lowercaseQuery = searchQuery.toLowerCase();
      const filtered = packageList.filter(pkg =>
        pkg.name.toLowerCase().includes(lowercaseQuery) ||
        (pkg.description && pkg.description.toLowerCase().includes(lowercaseQuery)) ||
        (pkg.type && pkg.type.toLowerCase().includes(lowercaseQuery))
      );
      setFilteredPackageList(filtered);
    } else {
      setFilteredPackageList(packageList);
    }
  }, [searchQuery, packageList]);

  const getPackagesData = async () => {
    setIsLoading(true);
    const dataResponse = await getAllPackage();
    if (dataResponse) {
      setPackageList(dataResponse);
      setFilteredPackageList(dataResponse);
    }
    setIsLoading(false);
  };

  const submitFormFilter = (values) => {
    setSearchQuery(values.searchTerm || "");
  };

  const onClearFilter = () => {
    formSearch.resetFields();
    setSearchQuery("");
  };

  const handleEdit = (id) => {
    navigate(LINK.PACKAGE_ID.format(id));
  };

  const handleDelete = async (id, packageName) => {
    confirm.delete({
      title: t("DELETE_PACKAGE"),
      content: t("DELETE_PACKAGE_CONFIRM", { name: packageName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async (e) => {
        setIsLoading(true);
        const response = await deletePackage(id);
        if (response) {
          toast.success(t("DELETE_PACKAGE_SUCCESS"));
          const updatedList = packageList.filter((item) => item._id !== id);
          setPackageList(updatedList);
          setFilteredPackageList(updatedList);
        } else {
          toast.error(t("DELETE_PACKAGE_ERROR"));
        }
        setIsLoading(false);
      },
    });
  };

  const translatePrice = (price) => {
    switch (price) {
      case "month":
        return t("MONTH");
        break;
      case "year":
        return t("YEAR");
        break;
      default:
        return price;
    }
  };

  const columns = [
    {
      title: t("PACKAGE_NAME"),
      dataIndex: "name",
      key: "name",
    },
    {
      title: t("DESCRIPTION"),
      dataIndex: "description",
      key: "description",
    },
    {
      title: t("PRICE"),
      dataIndex: "prices",
      key: "prices",
      render: (value) => {
        return (
          <div className={"price-cell"}>
            {value?.length
              ? value.map((price, index) => (
                  <div key={index}>{`${renderMoney(price?.unitAmount)} / ${translatePrice(price?.unitName)}`}</div>
                ))
              : null}
          </div>
        );
      },
    },
    {
      title: t("TYPE"),
      key: "type",
      dataIndex: "type",
      align: "center",
    },
    {
      title: t("CUSTOMER"),
      key: "customerTarget",
      dataIndex: "customerTarget",
      align: "center",
    },
    {
      title: t("ORDER"),
      key: "order",
      dataIndex: "order",
      align: "center",
    },
    {
      title: t("LAST_MODIFIED"),
      key: "updatedAt",
      dataIndex: "updatedAt",
      render: (value) => formatTimeDate(value),
    },
    {
      title: t("ACTION"),
      key: "action",
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className="package-setting-actions">
          <Tooltip title={t("EDIT_PACKAGE")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-edit-package"}
              icon={<EditOutlined />}
              onClick={() => handleEdit(record?._id)}
            />
          </Tooltip>
          <Tooltip title={t("DELETE_PACKAGE")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-package"}
              icon={<DeleteIcon />}
              onClick={() => handleDelete(record?._id, record?.name)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];
  return (
    <Loading active={isLoading} transparent>
      <div className="package-setting">
        <Card className="package-setting-info-card">
          <div className="package-setting-info-header">
            <div>
              <h1 className="package-setting-title">{t("PACKAGE_SETTING")}</h1>
              <p className="package-setting-description">{t("PACKAGE_SETTING_DESCRIPTION")}</p>
            </div>
            <AntButton
              type={BUTTON.DEEP_NAVY}
              size="large"
              className="btn-create-package"
              icon={<PlusOutlined />}
              onClick={() => navigate(LINK.PACKAGE_CREATE)}
            >
              {t("CREATE_PACKAGE")}
            </AntButton>
          </div>
        </Card>

        <Card className="package-setting-search-card">
          <AntForm form={formSearch} layout="horizontal" size={"large"} className="form-filter" onFinish={submitFormFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={12} lg={16}>
                <Form.Item name="searchTerm" className="search-form-item" style={{ marginBottom: 0 }}>
                  <Input
                    placeholder={t("SEARCH_PACKAGE_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton size={"large"} type={BUTTON.GHOST_WHITE} onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton size={"large"} htmlType={"submit"} type={BUTTON.DEEP_NAVY}>
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="package-setting-table-card">
          <TableAdmin
            pagination={true}
            dataSource={filteredPackageList.map((item, index) => ({ ...item, key: index }))}
            columns={columns}
            scroll={{ x: 1000 }}
            className={"package-setting-table"}
            rowClassName={() => "package-setting-table-row"}
            locale={{ emptyText: t("NO_PACKAGES_FOUND") }}
          />
        </Card>
      </div>
    </Loading>
  );
};

export default PackageSetting;
