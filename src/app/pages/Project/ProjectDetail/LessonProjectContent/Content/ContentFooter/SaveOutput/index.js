import { useTranslation } from "react-i18next";

import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";

import AntButton from "@component/AntButton";

import { BUTTON } from "@constant";

import "./SaveOutput.scss";

function SaveOutput(props) {
  const { t } = useTranslation();
  const outputForm = props?.outputForm || useContent()?.outputForm;
  const setEditing = props?.setEditing || useContent()?.setEditing;
  
  return <div className="save-output">
    
    <AntButton size="large" onClick={() => setEditing(false)}>
      {t("CANCEL")}
    </AntButton>
    
    <AntButton
      size="large"
      type={BUTTON.DEEP_NAVY}
      onClick={() => outputForm.submit()}
    >
      {t("SAVE")}
    </AntButton>
  </div>;
}

export default SaveOutput;