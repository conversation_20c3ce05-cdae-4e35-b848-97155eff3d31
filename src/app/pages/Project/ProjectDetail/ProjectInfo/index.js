import React, { useEffect, useMemo, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Affix, Dropdown } from "antd";
import clsx from "clsx";
import PropTypes from "prop-types";

import { connect } from "react-redux";

import { useProject } from "@app/pages/Project";
import Star from "@component/Star";
import AntButton from "@component/AntButton";
import Move from "@component/Move";
import Share from "@component/Share";
import UpdateProject from "./UpdateProject";
import ProjectInfoTool from "./ProjectInfoTool";
import ProjectInfoTemplate from "./ProjectInfoTemplate";
import WhoHasAccess from "@src/app/component/WhoHasAccess";
import { DownloadProject } from "@src/app/pages/Project/DownloadProject";

import { BUTTON, CONSTANT, PERMISSION, PROJECT_TYPE } from "@constant";
import { LINK } from "@link";

import { cleanFileName, cloneObj, downloadUsingBrowser, formatDate, getFileExtension } from "@common/functionCommons";
import { copyProject, deleteProject } from "@services/Project";
import { toStar, unStar } from "@services/MySaved";
import { getAllGeneralAccess } from "@services/GeneralAccess";
import { getAllUserAccess } from "@services/Share";


import MoreVertical from "@component/SvgIcons/MoreVertical";
import PlusIcon from "@component/SvgIcons/PlusIcon";
import EditIcon from "@component/SvgIcons/EditIcon";
import Download from "@component/SvgIcons/Download";

import CopyIcon from "@component/SvgIcons/Copy";
import MoveIcon from "@component/SvgIcons/Move";
import ShareIcon from "@component/SvgIcons/ShareIcon";
import TrashIcon from "@component/SvgIcons/Trash";

import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";

import PROJECT_LIGHT from "@src/asset/icon/project/project-light.svg";
import PROJECT_EXAM from "@src/asset/icon/project/project-exam.svg";
import FILE_YELLOW from "@src/asset/icon/file/file-yellow.svg";
import FOLDER_ICON from "@src/asset/icon/folder/folder-light.svg";
import DOWNLOAD_EXCEL_ICON from "@src/asset/icon/download-excel.svg";

import "./ProjectInfo.scss";

import * as app from "@src/ducks/app.duck";
import { createAcademicReport } from "@src/app/services/Report";
import { API } from "@api";

function ProjectInfo({ user, affixTargetId = "js-layout-content", ...props }) {
  
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  const { isExam, isMark, isMarkSpeaking } = useProject();
  const { projectId, projectData, setProjectData, permission, isAllowEditing, projectContentData } = useProject();
  const [generalAccessData, setGeneralAccessData] = useState({});
  const [userAccessData, setUserAccessData] = useState([]);
  const workspaceId = projectData?.workspaceId?._id || projectData?.workspaceId;
  const projectType = projectData?.type || PROJECT_TYPE.NORMAL;
  
  const [stateUpdateProject, setStateUpdateProject] = useState({
    isShowModal: false,
    updateType: undefined,
  });
  
  const [isShowMove, setShowMove] = useState(false);
  const [isShowShare, setShowShare] = useState(false);
  const [isShowDownload, setShowDownload] = useState(false);
  const [isDownloading, setDownloading] = useState(false);
  
  useEffect(() => {
    getAllGeneralAccessData();
    getAllUserAccessData();
  }, [projectId]);
  
  const getAllGeneralAccessData = async () => {
    const dataResponse = await getAllGeneralAccess({ projectId });
    if (dataResponse) {
      setGeneralAccessData(dataResponse[0]);
    }
  };
  
  const getAllUserAccessData = async () => {
    const dataResponse = await getAllUserAccess({ projectId });
    if (dataResponse) {
      setUserAccessData(dataResponse);
    }
  };
  
  
  async function handleSaveProject() {
    const serviceStar = projectData.isSaved ? unStar : toStar;
    const apiResponse = await serviceStar({ projectId });
    
    if (apiResponse) {
      setProjectData((prevState) => {
        const newState = cloneObj(prevState);
        newState.isSaved = !newState.isSaved;
        return newState;
      });
    }
  }
  
  const onCopyProject = async () => {
    const dataRequest = { projectId };
    const params = { workspaceId };
    const apiResponse = await copyProject(dataRequest, true, params);
    if (apiResponse) {
      navigate(LINK.PROJECT_DETAIL.format(apiResponse._id));
      toast.success("COPY_PROJECT_SUCCESS");
    }
  };
  
  const toggleMove = () => {
    setShowMove((prevState) => !prevState);
  };
  
  const toggleShare = () => {
    setShowShare((prevState) => !prevState);
  };
  
  async function handleShowModalRename() {
    setStateUpdateProject({ isShowModal: true, updateType: CONSTANT.NAME });
  }
  
  async function handleShowModalDescription() {
    setStateUpdateProject({ isShowModal: true, updateType: CONSTANT.DESCRIPTION });
  }
  
  async function closeUpdateProject() {
    setStateUpdateProject({ isShowModal: false, updateType: undefined });
  }
  
  async function onDeleteProject() {
    confirm.delete({
      content: t("CONFIRM_DELETE_PROJECT"),
      handleConfirm: async () => {
        const params = { workspaceId };
        const apiResponse = await deleteProject(projectId, true, params);
        if (apiResponse) {
          const isInitLocation = location.key === "default";
          toast.success("DELETE_PROJECT_SUCCESS");
          navigate(isInitLocation ? "/" : -1);
        }
      },
    });
  }
  
  function handleAfterShare(newPermission) {
    getAllGeneralAccessData();
    getAllUserAccessData();
    setProjectData((prevState) => ({ ...prevState, permission: newPermission }));
  }
  
  async function handleAfterMove(newProjectData) {
    props.setBreadcrumb({ project: newProjectData, folder: newProjectData?.folderId });
    setProjectData((prevState) => ({ ...prevState, folderId: newProjectData?.folderId }));
  }
  
  const onDownloadStudenReportCard = async () => {
    setDownloading(true);
    const dataRequest = { projectId };
    const apiResponse = await createAcademicReport(dataRequest);
    if (apiResponse?.fileName) {
      const displayName = cleanFileName(projectData.projectName) + "." + getFileExtension(apiResponse.fileName);
      const workspaceId = projectData?.workspaceId;
      const logParamsString = `&workspaceId=${workspaceId}&projectId=${projectId}`;
      downloadUsingBrowser(API.DOWNLOAD_REPORT_FILE.format(apiResponse.fileName, displayName) + logParamsString, displayName);
      setDownloading(false);
    } else {
      setDownloading(false);
    }
  };
  const isCreateByMe = projectData?.ownerId?._id === user?._id;
  const creator = isCreateByMe ? t("ME").toLowerCase() : projectData?.ownerId?.fullName;
  
  const isStartProject = useMemo(() => {
    return ((isMark && !projectContentData[0]?.responses?.length) || !projectContentData?.length);
  }, [projectContentData]);
  
  const isShowActivity = useMemo(() => {
    return !isMarkSpeaking && projectContentData.some(content => {
      return content?.responses?.some(response => response?.state === CONSTANT.DONE.toLowerCase());
    });
  }, [projectContentData, projectType]);
  
  return (
    <>
      <div className="project__info">
        <Affix offsetTop={32} target={() => document.getElementById(affixTargetId)}>
          <div className="project__info-content">
            <div className="project-info__header">
              <div className="project-info__icon">
                <img
                  src={isExam
                    ? PROJECT_EXAM
                    : isMark ? FILE_YELLOW : PROJECT_LIGHT}
                  alt="" />
              </div>
              <div className="project-info__name">
                <span className="project-info__name-label">{t("PROJECT")}:</span>
                <span className="project-info__name-text">{projectData.projectName}</span>
              </div>
              <div className="project-info__actions">
                <Dropdown
                  disabled={!isAllowEditing}
                  trigger={["click"]}
                  menu={{
                    selectedKeys: CONSTANT.DOCX,
                    items: [
                      { key: "COPY", label: t("COPY"), icon: <CopyIcon />, onClick: onCopyProject },
                      { key: "MOVE", label: t("MOVE"), icon: <MoveIcon />, onClick: toggleMove },
                      { key: "SHARE", label: t("SHARE"), icon: <ShareIcon />, onClick: toggleShare },
                      {
                        key: "RENAME",
                        label: t("RENAME"),
                        icon: <EditIcon />,
                        onClick: handleShowModalRename,
                      },
                      {
                        key: "DELETE",
                        label: t("DELETE"),
                        icon: <TrashIcon />,
                        onClick: onDeleteProject,
                      },
                    ],
                  }}
                >
                  <AntButton size="tiny" icon={<MoreVertical />} />
                </Dropdown>
                <Star visible active={projectData.isSaved} onClick={handleSaveProject} />
              </div>
            </div>
            
            <div
              className={clsx("project-info__description", {
                "project-info__description-allow-editing": isAllowEditing,
              })}
              onClick={handleShowModalDescription}
            >
              {isAllowEditing && (
                <div className="project-description__icon">{projectData.description ? <EditIcon /> : <PlusIcon />}</div>
              )}
              <div className="project-description__value" title={projectData.description}>
                {projectData.description || t("ADD_SHORT_DESCRIPTION")}
              </div>
            </div>
            
            <div className="project-info__divider">{t("PROJECT_DETAIL")}</div>
            
            <div className="project-info__detail">
              {projectData.folderId?._id && (
                <div className="project-detail-item">
                  <div className="project-detail-label">{t("LOCATION")}</div>
                  <Link
                    to={permission !== PERMISSION.VIEWER ? LINK.FOLDER_DETAIL.format(projectData.folderId?._id) : "#"}
                    className="project-detail-value project-detail-value__folder-name"
                  >
                    <img src={FOLDER_ICON} alt="" />
                    {projectData.folderId?.folderName}
                  </Link>
                </div>
              )}
              
              <div className="project-detail-item">
                <div className="project-detail-label">{t("MODIFIED")}</div>
                <div className="project-detail-value">
                  {`${formatDate(projectData.updatedAt)} ${t("BY").toLowerCase()} ${creator}`}
                </div>
              </div>
              
              <div className="project-detail-item">
                <div className="project-detail-label">{t("CREATED_AT")}</div>
                <div className="project-detail-value">
                  {`${formatDate(projectData.createdAt)} ${t("BY").toLowerCase()} ${creator}`}
                </div>
              </div>
              
              <WhoHasAccess
                permission={permission}
                generalAccess={generalAccessData}
                userAccess={userAccessData}
                handleAfterShare={handleAfterShare}
                shareData={{
                  queryAccess: {
                    projectId: projectData?._id,
                  },
                  name: projectData?.projectName,
                  owner: projectData?.ownerId,
                  workspaceId: workspaceId,
                }}
              />
            </div>
            
            {!isStartProject && <>
              {isShowActivity && <>
                <div className="project-info__divider">{t("PROJECT_ACTIVITY")}</div>
                <div className="project-info__activity">
                  <AntButton
                    block
                    size="small"
                    type={BUTTON.DEEP_NAVY}
                    icon={<Download />}
                    onClick={() => setShowDownload(true)}>
                    {t(isMark ? "DOWNLOAD_GRADING" : "DOWNLOAD_PROJECT")}
                  </AntButton>
                  {isMark && <AntButton
                    loading={isDownloading}
                    block
                    size="small"
                    type={BUTTON.GHOST_GREEN}
                    icon={<img src={DOWNLOAD_EXCEL_ICON} alt="" className="download-excel-icon" />}
                    onClick={onDownloadStudenReportCard}>
                    {t("SCOREBOARD")}
                  </AntButton>}
                  
                  {!isMark && <ProjectInfoTemplate />}
                </div>
              </>}
              
              <ProjectInfoTool />
            </>}
          </div>
        </Affix>
      </div>
      
      <UpdateProject
        open={stateUpdateProject.isShowModal}
        updateType={stateUpdateProject.updateType}
        onCancel={closeUpdateProject}
      />
      
      <Move
        isShowModal={isShowMove}
        handleCancel={toggleMove}
        projectId={projectId}
        handleAfterMove={handleAfterMove}
        workspaceId={workspaceId}
      />
      
      <Share
        isShowModal={isShowShare}
        handleCancel={toggleShare}
        queryAccess={{ projectId }}
        name={projectData?.projectName}
        owner={projectData?.ownerId}
        handleAfterShare={handleAfterShare}
        workspaceId={workspaceId}
      />
      
      <DownloadProject
        open={isShowDownload}
        onCancel={() => setShowDownload(false)}
        downloadType={CONSTANT.PROJECT}
      />
    </>
  );
}

ProjectInfo.propTypes = {
  affixTargetId: PropTypes.string,
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = { ...app.actions };

export default connect(mapStateToProps, mapDispatchToProps)(ProjectInfo);