import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { Pagination, Popover } from "antd";
import clsx from "clsx";

import Loading from "@component/Loading";
import SearchInput from "@component/SearchInput";
import AntButton from "@component/AntButton";
import NoData from "@component/NoData";

import { BUTTON, CONSTANT } from "@constant";
import { API } from "@api";

import { checkResourceExist } from "@services/Resource";
import { formatTimeDate } from "@common/functionCommons";

// import "./SelectImage.scss";
import { toast } from "@component/ToastProvider";

function ImageFromResource({ user, ...props }) {
  const { t } = useTranslation();
  const {
    onSelectResourceImage,
    categorySelected,
    myResourceData,
    orgResourceData,
    isLoading,
  } = props;
  
  const [imageSelected, setImageSelected] = useState(null);
  const [imageSearchValue, setImageSearchValue] = useState("");
  
  async function handleSelectImage() {
    const apiResponse = await checkResourceExist(imageSelected?._id);
    if (apiResponse) {
      onSelectResourceImage(imageSelected.imageId);
    } else {
      toast.error("FILE_NOT_FOUND");
    }
  }
  
  function onSearch(e) {
    setImageSearchValue(e.target.value);
    if (imageSelected) setImageSelected(null);
  }
  
  const resourceFilterer = useMemo(() => {
    const resourceData = categorySelected === CONSTANT.MY_RESOURCE
      ? myResourceData
      : categorySelected === CONSTANT.ORG_RESOURCE ? orgResourceData : [];
    
    if (imageSearchValue) {
      return resourceData?.filter(x => {
        return x?.name?.toLowerCase()?.includes(imageSearchValue?.toLowerCase()) && !!x?.imageId;
      });
    }
    
    return resourceData;
    
  }, [categorySelected, myResourceData, orgResourceData, imageSearchValue]);
  
  return <>
    <Loading active={isLoading} className="resource-table-container">
      <div className="resource-search">
        <SearchInput
          size={"large"}
          placeholder={t("SEARCH")}
          value={imageSearchValue}
          onChange={onSearch}
          onClear={() => setImageSearchValue(null)}
        />
      </div>
      
      {isLoading && <Loading active transparent minHeight={200} />}
      {!resourceFilterer?.length && !isLoading && <NoData />}
      
      {!!resourceFilterer?.length && <div className="image-from-resource__list">
        {resourceFilterer?.map(resource => {
          return <div
            key={resource._id}
            className={clsx("image-from-resource__item", { "image-from-resource__item-active": imageSelected?._id === resource._id })}
            onClick={() => setImageSelected(resource)}
          >
            <div className="image-from-resource__item-preview">
              <img
                className="project-preview-grid-item__preview-inner"
                src={API.STREAM_ID.format(resource?.imageId?.thumbnailFileId)}
                alt=""
              />
            </div>
            <div className="image-from-resource__item-title">
              <Popover
                className="line-clamp-1"
                placement="bottomLeft"
                content={resource?.name}
                trigger="hover"
              >
                {resource?.name}
              </Popover>
            </div>
            <div className="image-from-resource__item-update-at">
              {`${t("UPDATE_AT")}: ${formatTimeDate(resource?.updatedAt)}`}
            </div>
          </div>;
        })}
      </div>}
    
    </Loading>
    
    <div className="resource-submit">
      <AntButton
        size="large"
        onClick={() => props.setShowSelectImage(false)}
      >
        {t("CANCEL")}
      </AntButton>
      <AntButton
        size="large"
        type={BUTTON.DEEP_NAVY}
        disabled={!imageSelected}
        onClick={handleSelectImage}
      >
        {t("SELECT")}
      </AntButton>
    </div>
  </>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(ImageFromResource);