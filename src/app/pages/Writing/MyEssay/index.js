import React, { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import axios from "axios";

import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";
import Loading from "@component/Loading";
import StudentBreadcrumb from "@app/layout/StudentLayout/StudentBreadcrumb";
import Share from "@component/Share";
import MyAssign from "@app/pages/Student/MyAssign";

import { CONSTANT, STUDENT_TOOL_BASE_TYPE, TYPE_OF_TOOL } from "@constant";

import { handlePagingData } from "@common/dataConverter";
import { calPagingAfterDelete, cloneObj } from "@common/functionCommons";
import { deleteProject, getProjectByStudent, getProjectDetail } from "@services/Project";

import "./MyEssay.scss";

function MyEssay() {
  const { t } = useTranslation();

  const [isLoading, setLoading] = useState(false);
  const [essaysData, setEssaysData] = useState({
    rows: [],
    paging: {
      page: 1,
      pageSize: 5,
      total: 0,
      totalPages: 0,
    },
    query: { sortType: CONSTANT.LATEST, documentType: "" },
  });

  const [shareState, setShareState] = useState({
    isShowModal: false,
    projectData: null,
  });

  const cancelTokenSourceRef = useRef(null);

  function handleShowModal(isShowModal = false, projectData = null) {
    if (isShowModal) {
      setShareState({ isShowModal, projectData });
    } else {
      setShareState(prevState => Object.assign({}, prevState, { isShowModal }));
    }
  }

  async function getEssayData(
    paging = essaysData.paging,
    query = essaysData.query,
  ) {
    setLoading(true);
    const queryObj = {
      sort: query.sortType === CONSTANT.LATEST ? "-createdAt" : "createdAt",
      inputType: query.inputType,
      ...query.category ? { category: query.category } : {},
      ...query.documentType ? { documentType: query.documentType } : {},
      ...query.tag ? { tag: query.tag } : {},
    };

    if (cancelTokenSourceRef.current) {
      cancelTokenSourceRef.current.cancel();
    }

    cancelTokenSourceRef.current = axios.CancelToken.source();

    const config = {
      cancelToken: cancelTokenSourceRef.current?.token,
    };

    const apiResponse = await getProjectByStudent(paging, queryObj, config);
    if (apiResponse) {
      setEssaysData(handlePagingData(apiResponse, query));
    }
    cancelTokenSourceRef.current = null;
    setLoading(false);
  }

  function onChangePage(page) {
    getEssayData(Object.assign({}, essaysData.paging, { page }));
  }

  function onChangeSort(type) {
    getEssayData(essaysData.paging, { ...essaysData.query, sortType: type });
  }

  const handleQueryProperties = (query, dataChange) => {
    const { documentType, tags, categories } = dataChange || {};
    
    query.documentType = documentType?.length === 1 ? documentType[0] : undefined;
    query.tag = tags?.length ? tags.join(",") : undefined;
    
    let inputType = "";
    let categoryList = [];
    
    if (categories) {
      Object.entries(categories).forEach(([key, value]) => {
        if (value?.length) {
          categoryList.push(...value);
        }
        if (key === STUDENT_TOOL_BASE_TYPE.writing_1) {
          inputType += `${TYPE_OF_TOOL.MARK_TEST_TASK_1}, ${TYPE_OF_TOOL.STUDENT_TASK_1}, `;
        }
        if (key === STUDENT_TOOL_BASE_TYPE.writing_2) {
          inputType += `${TYPE_OF_TOOL.MARK_TEST_TASK_2}, ${TYPE_OF_TOOL.STUDENT_TASK_2}, `;
        }
      });
    }
    
    query.inputType = inputType || `${TYPE_OF_TOOL.MARK_TEST_TASK_1}, ${TYPE_OF_TOOL.MARK_TEST_TASK_2}, ${TYPE_OF_TOOL.STUDENT_TASK_1}, ${TYPE_OF_TOOL.STUDENT_TASK_2}`;
    query.category = categoryList.length ? categoryList.join(", ") : undefined;
    
    // Clean up undefined properties
    Object.keys(query).forEach(key => query[key] === undefined && delete query[key]);
  };
  
  function onChangeFilter(dataChange) {
    if (!dataChange) return;

    const query = cloneObj(essaysData.query);
    handleQueryProperties(query, dataChange);
    getEssayData(essaysData.paging, query);
  }

  function onDeleteAssign(projectId) {
    confirm.delete({
      content: t("CONFIRM_DELETE_PROJECT"),
      handleConfirm: async () => {
        const apiResponse = await deleteProject(projectId);
        if (apiResponse) {
          await getEssayData(calPagingAfterDelete(essaysData));
          toast.success("DELETE_PROJECT_SUCCESS");
        }
      },
    });
  }

  async function onShareAssign(projectId) {
    const apiResponse = await getProjectDetail(projectId);
    if (apiResponse.code === 200) {
      handleShowModal(true, apiResponse.data);
    }

  }

  return <>
    <Loading active={isLoading} className="my-essays-container">
      <StudentBreadcrumb />

      <MyAssign
        title={t("ALL_ESSAYS")}
        studentProjectType={CONSTANT.WRITING}
        total={essaysData.paging.total}
        currentPage={essaysData.paging.page}
        dataSource={essaysData.rows}
        sortType={essaysData.query.sortType}
        onChangePage={onChangePage}
        onChangeSort={onChangeSort}
        onDeleteAssign={onDeleteAssign}
        onShareAssign={onShareAssign}

        onChangeFilter={onChangeFilter}
      />
    </Loading>


    <Share
      isShowModal={shareState.isShowModal}
      handleCancel={handleShowModal}
      queryAccess={{ projectId: shareState.projectData?._id }}
      name={shareState.projectData?.projectName}
      owner={shareState.projectData?.ownerId}
      workspaceId={shareState.projectData?.workspaceId}
      disabledEdit
    />
  </>;
}

export default MyEssay;