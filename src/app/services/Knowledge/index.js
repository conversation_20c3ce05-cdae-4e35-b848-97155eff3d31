import { API } from "@api";
import { createBase, deleteBase, getAllBase, getAllPaginationBase, getDetailBase, updateBase } from "@services/Base";

export function createKnowledge(data, toastError = false) {
  return createBase(API.KNOWLEDGE, data, [], false, toastError);
}

export function getKnowledgeDetail(id) {
  return getDetailBase(API.KNOWLEDGE_ID, id);
}

export function updateKnowledge(data, toastError = false) {
  return updateBase(API.KNOWLEDGE_ID, data, [], false, toastError);
}

export function deleteKnowledge(id, toastError = false) {
  return deleteBase(API.KNOWLEDGE_ID, id, false, toastError);
}

export async function getPaginationKnowledge(paging, query, populateOpts = [], loading) {
  query.sort = query.sort ? query.sort : "-createdAt";
  return getAllPaginationBase(API.KNOWLEDGE, paging, query, ["name"]);
}

export function getAllKnowledge(query, loading) {
  return getAllBase(API.KNOWLEDGE, query, null, loading);
}