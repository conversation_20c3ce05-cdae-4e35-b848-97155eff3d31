import { useEffect } from "react";
import { Avatar, Table } from "antd";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { connect } from "react-redux";

import ProjectActionsDropdown from "@component/ProjectActionsDropdown";
import Star from "@component/Star";

import { LINK } from "@link";
import { THEME_TYPE } from "@constant";
import { formatDate } from "@common/functionCommons";
import { saved, unsaved } from "@src/app/services/MySaved";
import { getProjectIcon } from "@src/app/component/FolderProjectList/FolderProjectItem";

import USER_ICON from "@src/asset/icon/button/user.svg";

const FolderListTable = ({ user, ...props }) => {
  const { projectsData } = props;
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  useEffect(() => {
    const rows = document.getElementsByClassName("ant-table-row");
    for (let item of rows) {
      item.setAttribute("tabindex", "0");
    }
  }, []);
  
  const onClickStar = async (record) => {
    if (!record.isSaved) {
      const response = await saved(record?._id, null);
      if (response) {
        props.handleAfterChangeStar(record);
      }
    } else {
      const response = await unsaved(record?._id, null);
      if (response) {
        props.handleAfterChangeStar(record);
      }
    }
  };
  const navigateFolder = (project) => {
    navigate(LINK.PROJECT_DETAIL.format(project._id));
  };
  
  const columns = [
    {
      title: t("NAME"),
      dataIndex: "projectName",
      render: (text, record, index) => (
        <div className="project-name-cell" >
          <div onClick={e => e.stopPropagation()}>
            <Star
              active={record?.isSaved}
              onClick={() => onClickStar(record)}
            />
          </div>
          <img alt="" src={getProjectIcon(record)} />
          <span className="text-name-project">{text}</span>
        </div>
      ),
    },
    {
      title: "Owner",
      render: (value) => (
        <div className="owner-cell">
          <Avatar size={32} icon={<img alt="" src={USER_ICON} />} />
          {value?.ownerId?._id === user._id ? t("ME") : value?.ownerId.fullName}
        </div>
      ),
    },
    {
      title: "Last modified",
      dataIndex: "lastModified",
      className: "time-cell",
      render: formatDate,
    },
    {
      title: t("ACTION"),
      width: 80,
      render: (_, record, index) => (
        <div className="action-cell" onClick={e => e.stopPropagation()}>
          <ProjectActionsDropdown
            projectData={record}
            handleAfterCopy={props.handleAfterCopy}
            handleAfterRename={props.handleAfterRename}
            handleAfterDelete={props.handleAfterDelete}
            handleAfterMove={props.handleAfterMove}
          />
        </div>
      
      ),
    },
  ];
  
  return (
    <div className="folder-table">
      <Table
        bordered={false}
        columns={columns}
        dataSource={projectsData}
        
        scroll={{ x: "max-content" }}
        //scroll={{ x: 700 }}
        pagination={false}
        onRow={(record, rowIndex) => {
          return {
            onClick: () => navigateFolder(record),
          };
        }}
      />
    </div>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(FolderListTable);
