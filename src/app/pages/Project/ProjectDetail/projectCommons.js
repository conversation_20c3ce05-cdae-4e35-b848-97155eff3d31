import { CONSTANT, VISIBLE_TOOL } from "@constant";
import store from "@src/setup/redux/Store";
import { toast } from "@component/ToastProvider";
import { t } from "i18next";


function checkToolDisable(tool) {
  const { auth: { user } } = store.getState();
  
  if (tool.isDeleted || tool.visible === VISIBLE_TOOL.developing.value) return true;
  if (tool.visible === VISIBLE_TOOL.private.value && user?.organizationId?._id) {
    return !tool.organizationIds.includes(user.organizationId._id);
  }
  return false;
}

export function populateTool(toolId) {
  const { tool: { listAllTool } } = store.getState();
  
  const toolData = listAllTool?.find((tool) => tool._id === toolId);
  if (toolData) {
    toolData.isDisable = checkToolDisable(toolData);
  }
  return toolData;
}

export function getToolInfo(content) {
  return populateTool(content?.toolId?._id);
}

function handleCheckValid(formName, value, toolInfo) {
  if (formName.includes(CONSTANT.AUDIO) && !value.audioId) {
    return { type: CONSTANT.MISSING, field: CONSTANT.FILE };
  } else if (formName.includes(CONSTANT.VIDEO)) {
    if ((!value.url || !value.videoId) && !value.offlineVideoId) {
      return { type: CONSTANT.MISSING, field: CONSTANT.VIDEO };
    }
    if (value.isUploading) {
      return { type: CONSTANT.UPLOADING, field: CONSTANT.VIDEO };
    }
  } else if (formName.includes(CONSTANT.TOPIC) && !value.topic) {
    return { type: CONSTANT.MISSING, field: CONSTANT.TOPIC };
  } else if (formName.includes(CONSTANT.SAMPLE_EXAM) && !value.text) {
    return { type: CONSTANT.MISSING, field: toolInfo?.inputLabel || CONSTANT.SAMPLE_EXAM };
  } else if (formName.includes(CONSTANT.TEXT) && !value.text) {
    return { type: CONSTANT.MISSING, field: CONSTANT.TEXT };
  } else if (formName.includes(CONSTANT.IMAGE) && !value.imageId && !value.topicImageId) {
    return { type: CONSTANT.MISSING, field: formName.includes(CONSTANT.MARK_EXAM) ? CONSTANT.EXAM_QUESTION : CONSTANT.IMAGE };
  } else if (formName.includes(CONSTANT.INSTRUCTION) && !!toolInfo.instructionIds?.length && !value.instructionId) {
    return { type: CONSTANT.MISSING, field: "TYPE_OF_TOOL" };
  }
  
  return null;
}

export async function handleDataRequest(forms, toolInfo) {
  const dataRequest = {};
  
  let formValidateFailed = false;
  
  const formList = Object.keys(forms);
  const formError = {};
  for (let i = 0; i < formList.length; i++) {
    const formName = formList[i];
    const form = forms[formName];
    
    try {
      await form.validateFields();
    } catch (_) {
      formValidateFailed = true;
      break;
    }
    
    
    const formValue = form.getFieldsValue();
    const dataNotValid = handleCheckValid(formName, formValue, toolInfo);
    
    if (dataNotValid) {
      formError[dataNotValid.type] ||= [];
      formError[dataNotValid.type].push(dataNotValid.field);
    } else {
      Object.keys(formValue).forEach((key) => {
        dataRequest[key] = formValue[key];
      });
      
      delete dataRequest.isUploading;
      if (formName.includes(CONSTANT.VIDEO)) {
        if (dataRequest?.videoType === CONSTANT.OFFLINE_VIDEO) {
          delete dataRequest.url;
        } else {
          delete dataRequest.offlineVideoId;
        }
      }
    }
  }
  
  if (formValidateFailed) return null;
  
  if (Object.keys(formError).length) {
    Object.entries(formError).forEach(([key, value]) => {
      const valueTrans = value.map(item => t(item));
      let warningMessage = "";
      switch (key) {
        case CONSTANT.MISSING:
          warningMessage = t("_MUST_NOT_BE_MISSING").format(valueTrans.join(", "));
          break;
        case CONSTANT.UPLOADING:
          warningMessage = t("_IS_UPLOADING").format(valueTrans.join(", "));
          break;
        default:
          break;
      }
      toast.warning({ description: warningMessage });
    });
    return null;
  }
  
  return dataRequest;
}