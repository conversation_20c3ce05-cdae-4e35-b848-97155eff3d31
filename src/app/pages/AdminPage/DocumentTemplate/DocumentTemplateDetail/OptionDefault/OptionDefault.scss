.document-options {
  .ant-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .ant-form-item {
    margin-bottom: 0;

    .ant-form-item-label > label {
      font-weight: 500;
    }
  }

  .header-image-upload {
    .ant-form-item-control-input-content {
      display: flex;
      justify-content: center;

      @media screen and (max-width: 767.98px) {
        justify-content: flex-start;
      }
    }
  }

  .option-fields-container {
    border: 1px solid var(--background-light-background-grey);
    border-radius: 8px;
    padding: 20px;
    background-color: #FAFAFA;

    .option-fields-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      color: var(--typo-colours-primary-black);
    }

    .option-fields-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;

      @media screen and (max-width: 767.98px) {
        grid-template-columns: 1fr;
      }
    }
  }

  .flex.flex-row-reverse {
    margin-top: 24px;
  }

  .document-template-detail__no-data {
    padding: 24px;
    background-color: var(--background-light-background-grey);
    border-radius: 8px;
  }
}
