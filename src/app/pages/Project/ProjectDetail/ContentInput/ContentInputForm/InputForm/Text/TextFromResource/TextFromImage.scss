.text-from-image-modal {
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media screen and (max-height: 720px) {
    gap: 16px;
  }

  .text-from-image__upload-image {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .description {
      color: #858585;
      font-size: 16px;
      font-weight: 400;
      line-height: 1.25;
    }

    .image-input-hidden {
      display: none !important;
    }

    .preview-image-wrap {
      display: flex;
      flex-direction: column;
      width: 100%;
      align-items: center;

      .remove__image {
        display: flex;
        padding: 0;
        align-items: center;
        border-radius: 4px;
        background: #FFF;
        color: #FFA200;
        align-self: flex-start;
        height: auto;
        font-weight: 400;
        font-size: 16px;
        line-height: 1.25;
        margin-left: auto;
        margin-bottom: 8px;
        border: none;

        img {
          width: 16px;
          height: 16px;
        }
      }

      button {
        width: fit-content;
      }

      .preview-image {
        display: flex;
        width: 100%;
        aspect-ratio: 2;
        background: #F1F1F1;
        justify-content: center;
        margin-bottom: 16px;
        padding: 10px;
        align-items: center;

        img {
          max-width: 100%;
          max-height: calc(100vh - 300px);
          object-fit: contain;
        }
      }
    }
  }
}

.file-dropzone {
  display: flex;
  justify-content: center;
  background: var(--primary-colours-blue-light-2);
  width: 100%;

  &.dragenter {
    border: 1px dashed var(--primary-colours-blue);
    background: #FCFDFF;
  }

  &.dragenter * {
    opacity: 0.7;
  }

  .dropzone-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    padding: 80px 0;
    text-align: center;
    font-size: 16px;
    color: var(--primary-colours-blue);

    .dropzone__label {
      font-size: 24px;
      font-weight: 700;
    }

    span {
      max-width: 480px;
    }
  }
}