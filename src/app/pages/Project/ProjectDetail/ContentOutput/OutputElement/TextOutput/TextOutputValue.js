import React from "react";
import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";
import TypingEffect from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TypingEffect";


function TextOutputValue({ responseSelected }) {
  
  const textHtml = <div className="text-output__text">
    {responseSelected?.output?.text}
  </div>;
  
  if (responseSelected.typingEffect)
    return <TypingEffect html={textHtml} />;
  return textHtml;
}

export default TextOutputValue;