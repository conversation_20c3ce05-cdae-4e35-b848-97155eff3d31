.container123 {
  display: flex;
  flex-direction: row;
  gap: 16px;
  position: relative;
  align-items: flex-start;
  overflow-x: hidden;

}


.part-one {
  margin: 0;
  position: relative;
  align-items: center;
  display: flex;
  flex-direction: column;
  padding: 32px 32px;
  border-radius: 24px;
  background: radial-gradient(18.71% 33.59% at 50% 8.03%, #f4f3ff 0.01%, #ffffff 100%);
  box-shadow: 0 4px 20px 0 #0000001a;
  flex: 1;
  // min-width: 1128px;
  width: 100%;
  justify-content: center;


  .part-one__container {
    max-width: 544px;
    width: 100%;
    gap: 16px;

    .part-one__container__header {
      flex: 1;
      // max-width: 940px;
      display: flex;
      align-items: center;
      flex-direction: column;
      gap: 8px;
      text-align: center;
      justify-content: center;

      .part-one__container__title {
        font-size: 22px;
        font-weight: 600;
        line-height: 30px;
        color: #09196B;
      }

      .part-one__container__content {
        flex: 1;
        max-width: 308px;
        display: flex;
        align-items: center;
        text-align: center;
        flex-direction: column;

        font-size: 12px;
        line-height: 16px;
        font-weight: 400;

        p {
          margin: 0;

        }
      }
    }

    .part-one__content {
      margin-top: 16px;
      // gap: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;

      padding: 24px 32px 8px 32px;
      border-radius: 24px;
      background: radial-gradient(18.71% 33.59% at 50% 8.03%, #f4f3ff 0.01%, #ffffff 100%);
      box-shadow: 0 4px 20px 0 #0000001a;

      .part-one__content__header {
        gap: 8px;
        display: flex;
        align-items: center;

        p {
          margin: 0;
          font-size: 16px;
          line-height: 24px;
          font-weight: 500;
        }
      }

      .part-one__content__footer {
        margin: 16px 0 16px 0;
        display: flex;
        justify-content: center; // căn giữa theo chiều ngang

        .part-one__footer__button {
          width: 159px;
          height: 40px;
          border-radius: 12px;
          padding: 8px 24px;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
          background-color: #09196b;
          color: white;

          &:hover {
            color: white;
            background-color: #09196b;

          }
        }
      }


    }

    .part-one__body-language{
      margin-top: 16px;
    }

    .part-one__footer {
      margin-top: 16px;
      display: flex;
      // align-items: center;
      justify-content: center;


      &__border {
        // max-width: 140px;
        padding: 8px 24px;
        gap: 8px;
        background-color: #09196B;
        border-radius: 12px;
        cursor: pointer;

      }

      &__next {
        font-size: 16px;
        line-height: 24px;
        font-weight: 600;
        color: #ffffff;
      }

      .part-one__footer:empty {
        display: none;
      }
    }

  }
}