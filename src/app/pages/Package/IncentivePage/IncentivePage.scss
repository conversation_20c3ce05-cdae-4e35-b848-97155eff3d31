.incentive-page-container {
  .incentive-page-content {
    gap: 24px;
    font-family: Segoe UI;
    grid-template-columns: repeat(4, minmax(0, 1fr));

    @media screen and (max-width: 1000px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    @media screen and (max-width: 700px) {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }

  }

  .incentive-page-item {
    padding: 0 0 24px 0;
    display: flex;

    flex-direction: row;
    gap: 8px;
    flex-wrap: wrap;
    background-color: var(--background-light-background-2);
    box-shadow: var(--shadow-level-2);

    .incentive-page-item__image {
      object-fit: cover;
      width: 100%;
      aspect-ratio: 4/3;
      overflow: hidden;
    }

    .incentive-page-item__status {
      background-color: var(--background-light-background-grey);
      display: flex;
      color: var(--typo-colours-support-blue-light);
      padding: 4px 16px;
      font-weight: 400;
      font-size: 13px;
      width: 80px;

      justify-content: center;
      align-items: center;
      height: 25px;
    }

    .incentive-body {
      padding: 0 16px;
      gap: 24px;
      display: flex;
      flex-direction: column;
    }
    .incentive-body__content{
      gap: 16px;
      display: flex;
      flex-direction: column;

    }

    .incentive-page-item__body {
      gap: 8px;
      display: flex;
      flex-direction: column;
    }

    .incentive-page-item__title {
      font-weight: 600;
      font-size: 16px;
      line-height: 20px;
    }

    .incentive-page-item__description {
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      color: black;
    }
    .incentive-page-item__action{
      display: flex;
      justify-content: center;
    }
  }

}