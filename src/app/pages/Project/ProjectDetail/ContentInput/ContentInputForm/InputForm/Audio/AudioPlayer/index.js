import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";
import PropTypes from "prop-types";
import clsx from "clsx";

import Loading from "@component/Loading";

import { convertSecondToHHMMSS } from "@common/functionCommons";

import PLAY_WHITE from "@src/asset/icon/play/play-white.svg";
import PLAY_WHITE_24 from "@src/asset/icon/play/play-white-24.svg";
import PAUSE_WHITE from "@src/asset/icon/pause/pause-white.svg";
import PAUSE_WHITE_24 from "@src/asset/icon/pause/pause-white-24.svg";

import VOLUME_ON_24 from "@src/asset/icon/volume/volume-24.svg";
import VOLUME_ON from "@src/asset/icon/volume/volume-on.svg";

import VOLUME_OFF_24 from "@src/asset/icon/volume/volume-mute-24.svg";
import VOLUME_OFF from "@src/asset/icon/volume/volume-x.svg";

import "./AudioPlayer.scss";

const AudioPlayer = ({
                       id, src, disabled, size,
                       start = 0,
                       afterLoadAudio = () => null,
                       ...props
                     }) => {
  const [, updateState] = useState();
  const forceUpdate = useCallback(() => updateState({}), []);
  
  const jsAudio = useRef(undefined);
  const audioRef = useRef(null);
  
  const jsProgressBar = useRef(undefined);
  
  const isLoaded = useRef(false);
  const timeoutRef = useRef(null);
  
  const timeStart = useRef(0);
  
  const [isPlaying, setPlaying] = useState(false);
  const [isMute, setMute] = useState(false);
  const [durationTime, setDurationTime] = useState(0);
  
  const isSmall = useMemo(() => size === "small", [size]);
  
  useEffect(() => {
    if (isPlaying) setPlaying(false);
    if (!!timeoutRef.current) clearTimeout(timeoutRef.current);
    
    handleCheckRetry();
  }, [src]);
  
  useEffect(() => {
    timeStart.current = start;
    
    if (!jsAudio.current) return;
    if (jsAudio.current) {
      jsAudio.current.currentTime = start;
    }
  }, [start]);
  
  useLayoutEffect(() => {
    function timeUpdate(ev) {
      const currentTime = ev.target.currentTime;
      if (currentTime === 0) {
        jsProgressBar.current.value = 0;
        jsProgressBar.current.style.setProperty("--thumb-width", "1px");
      } else {
        jsProgressBar.current.style.setProperty("--thumb-width", "0");
      }
      jsProgressBar.current.style.setProperty("--value", currentTime);
      
      if (currentTime === ev.target.duration) {
        setPlaying(false);
      }
    }
    
    function getElement() {
      jsAudio.current = document.getElementById(`js-audio-${id}`);
      jsProgressBar.current = document.getElementById(`js-progress-bar-${id}`);
      
      jsAudio.current.addEventListener("timeupdate", timeUpdate);
      return () => jsAudio.current.removeEventListener("timeupdate", timeUpdate);
    }
    
    if (document.readyState === "complete") {
      getElement();
    } else {
      window.addEventListener("load", getElement);
      return () => document.removeEventListener("load", getElement);
    }
  }, []);
  
  
  useEffect(() => {
    function checkFinished() {
      if (jsAudio.current) {
        jsAudio.current.currentTime = timeStart.current;
      }
    }
    
    const jsAudioElement = document.getElementById(`js-audio-${id}`);
    jsAudioElement.addEventListener("ended", checkFinished, false);
    return () => jsAudioElement.removeEventListener("ended", checkFinished);
  }, []);
  
  
  useEffect(() => {
    if (!jsAudio.current) return;
    if (isPlaying) {
      jsAudio.current.play();
    } else {
      jsAudio.current.pause();
    }
  }, [isPlaying]);
  
  
  function handleCheckRetry() {
    if (!src) return;
    
    isLoaded.current = false;
    forceUpdate();
    
    timeoutRef.current = setTimeout(() => {
      if (!audioRef.current) {
        return handleCheckRetry();
      }
      
      if (!isLoaded.current) {
        const currentSrc = audioRef.current.src;
        audioRef.current.src = "";  // Reset src
        
        setTimeout(() => {
          audioRef.current.src = currentSrc;  // Gán lại src sau 1 giây
          // recheck
          handleCheckRetry();
        }, 100);
      }
      
    }, 1000);
  }
  
  const handlePlayOrPauseAudio = () => {
    setPlaying((pre) => !pre);
  };
  
  const toggleMute = () => {
    setMute((pre) => !pre);
  };
  
  const onChangeProgressBar = (e) => {
    jsAudio.current.currentTime = e.target.value;
  };
  
  const onLoadedMetadata = (e) => {
    const duration = e?.target?.duration;
    if (!jsProgressBar.current || !duration) return;
    
    isLoaded.current = true;
    forceUpdate();
    
    jsProgressBar.current.style.setProperty("--min", 0);
    jsProgressBar.current.style.setProperty("--thumb-width", "1px");
    jsProgressBar.current.style.setProperty("--max", duration);
    jsProgressBar.current.min = 0;
    jsProgressBar.current.max = duration;
    jsProgressBar.current.value = 0;
    
    setDurationTime(duration);
    afterLoadAudio(duration);
  };
  
  const formatTime = (time) => {
    if (time && !isNaN(time)) {
      return convertSecondToHHMMSS(time);
    }
    return "00:00:00";
  };
  
  return <Loading active={!isLoaded.current} className="audio-player">
    <audio
      id={`js-audio-${id}`}
      ref={audioRef}
      src={src}
      muted={isMute}
      onLoadedMetadata={onLoadedMetadata}
    />
    <div className={clsx("audio-player__controls", { "audio-player__controls-small": isSmall })}>
      <button type="button" className="play-button">
        <img
          src={isPlaying
            ? isSmall ? PAUSE_WHITE : PAUSE_WHITE_24
            : isSmall ? PLAY_WHITE : PLAY_WHITE_24}
          onClick={handlePlayOrPauseAudio}
          alt=""
        />
      </button>
      <div className="progress-bar-container">
        <input
          id={`js-progress-bar-${id}`}
          step='any'
          type="range"
          className="progress-bar"
          defaultValue="0"
          onChange={onChangeProgressBar} />
        <div className="duration">{formatTime(durationTime)}</div>
        <img
          className="volume-button"
          src={isMute
            ? isSmall ? VOLUME_OFF : VOLUME_OFF_24
            : isSmall ? VOLUME_ON : VOLUME_ON_24}
          onClick={toggleMute}
          alt=""
        />
      </div>
    </div>
  </Loading>;
};

AudioPlayer.propTypes = {
  afterLoadAudio: PropTypes.func,
  start: PropTypes.number,
};

export default AudioPlayer;