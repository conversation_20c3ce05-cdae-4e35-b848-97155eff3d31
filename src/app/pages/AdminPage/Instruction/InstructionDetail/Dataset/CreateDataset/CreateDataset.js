import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";
import { LINK } from "@link";
import Actions from "@src/app/component/Actions";
import { createDataset, getDetailDataset, updateDetailDataset } from "@src/app/services/Dataset";
import {
  copyConversation,
  createConversation,
  deleteConversation,
  editConversation,
  getAllConversation,
  approveConversation,
} from "@src/app/services/Dataset/Conversation";
import { Checkbox, Col, Form, Input, Modal, Row, Select, Space, Table } from "antd";

import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import "./CreateDataset.scss";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { BUTTON, ROLE_CONVERSATION } from "@constant";
import { parseJsonToText, parseTextToJson } from "@common/dataConverter";
import { formatTimeDate } from "@common/functionCommons";
import AntButton from "@src/app/component/AntButton";
import { useTranslation } from "react-i18next";
import CancelIcon from "@component/SvgIcons/CancelIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";
import CustomCKEditor from "@src/app/component/CKEditor";
import TableAdmin from "@src/app/component/TableAdmin";

CreateDataset.propTypes = {};

function CreateDataset({ recordId, instructionId, handleCreateDataset, handleCloseModal, handleReloadData, ...props }) {
  const { t } = useTranslation();
  const [formDataset] = Form.useForm();
  const [formConversation] = Form.useForm();
  const { id } = useParams();
  const { outputTypeCode } = props;
  
  const [dataConversations, setDataConversations] = useState([]);
  const [dataModal, setDataModal] = useState(null);
  const [isShowModal, setShowModal] = useState(false);
  const [defaultDataset, setDefaultDataset] = useState(false);
  const [responseFormat, setResponseFormat] = useState(undefined);
  const [selectedRoles, setSelectedRoles] = useState([]);
  
  const navigate = useNavigate();
  useEffect(() => {
    if (!recordId) {
      formDataset.resetFields();
      setDataModal(null);
      setResponseFormat(undefined);
    }
  }, [recordId]);
  const getDataDetailDataset = async () => {
    if (recordId) {
      const response = await getDetailDataset(recordId, true);
      if (response) {
        setResponseFormat(response?.instructionId?.outputTypeId?.responseFormat);
      }
      formDataset.setFieldsValue(response);
    }
  };
  
  useEffect(() => {
    getDataDetailDataset();
    getDataConversations();
  }, [recordId]);
  
  const openModal = () => {
    setShowModal(true);
  };
  
  const closeModal = () => {
    formConversation.resetFields();
    setDataModal(null);
    setShowModal(false);
    setSelectedRoles([]);
  };
  
  const handleCopy = async (conversationId) => {
    const copiedConversation = await copyConversation({ conversationId }, true);
    if (copiedConversation) {
      toast.success("COPY_CONVERSATION_SUCCESS");
      await getDataConversations();
      handleEdit(copiedConversation);
    }
  };
  
  const changeApprove = async (isChecked, itemConversation) => {
    const response = await approveConversation(itemConversation?._id, { approved: isChecked?.target?.checked }, true);
    if (response) {
      getDataConversations();
      toast.successKey("APPROVE_CONVERSATION_SUCCESS");
    }
  };
  
  const columns = [
    {
      title: t("ORDER"),
      width: 80,
      render: (value, row, index) => index + 1,
    },
    {
      title: "Creator",
      key: "creatorId",
      dataIndex: ["creatorId", "fullName"],
    },
    {
      title: "Rating",
      key: "rating",
      dataIndex: "rating",
    },
    {
      title: "Created at",
      key: "createdAt",
      align: "center",
      render: (value) => formatTimeDate(value.createdAt),
    },
    {
      title: "Approved",
      key: "approved",
      align: "center",
      render: (value) => (
        <div><Checkbox defaultChecked={value?.approved || false} onChange={(e) => changeApprove(e, value)}></Checkbox>
        </div>),
    },
    {
      title: "Approved by",
      key: "approvedBy",
      align: "center",
      render: (value) => (<span>{value?.approvedBy?.fullName}</span>),
      
    },
    
    {
      title: "Last modified",
      key: "updatedAt",
      align: "center",
      render: (value) => formatTimeDate(value.updatedAt),
    },
    {
      title: t("ACTION"),
      key: "action",
      align: "center",
      width: 200,
      render: (_, record) => (
        <Actions
          center
          handleCopy={() => handleCopy(record._id)}
          handleEdit={() => handleEdit(record)}
          handleDelete={() => handleDelete(record._id)}
        />
      ),
    },
  ];
  
  const onFinish = async (e) => {
    if (!recordId) {
      const createResponse = await createDataset({ name: e.name, instructionId: id, isDefault: defaultDataset }, true);
      if (createResponse) {
        toast.success("CREATE_DATASET_SUCCESS");
        handleReloadData();
        handleCreateDataset(createResponse);
      }
    } else {
      const updateResponse = await updateDetailDataset({
        _id: recordId,
        name: e.name,
        isDefault: defaultDataset,
      }, true);
      if (updateResponse) {
        handleReloadData();
        toast.success("EDIT_DATASET_SUCCESS");
      }
    }
  };
  
  const handleCancel = () => {
    handleCloseModal();
  };
  const getDataConversations = async () => {
    const response = await getAllConversation({ datasetId: recordId, sort: "-createdAt" });
    if (response) {
      setDataConversations(response);
    }
  };
  const handleEdit = (item) => {
    const updatedMessages = item.messages?.map((message) => {
      if (message.role === "assistant" && responseFormat !== "markdown") {
        return {
          ...message,
          content: parseJsonToText(message.content),
        };
      }
      return message;
    });
    
    setDataModal(item);
    setSelectedRoles(item.messages?.map((item) => item.role));
    formConversation.setFieldsValue({ messages: updatedMessages });
    openModal();
  };
  
  const handleDelete = async (item) => {
    confirm.delete({
      content: t("CONFIRM_DELETE_CONVERSATION"),
      handleConfirm: async (e) => {
        const response = await deleteConversation(item, true);
        if (response) {
          getDataConversations();
          toast.success("REMOVE_CONVERSATION_SUCCESS");
        }
      },
    });
  };
  
  const checkValidJSON = (value) => {
    const toJson = parseTextToJson(value);
    if (!toJson) {
      return null;
    }
    return toJson;
  };
  
  const formSubmit = async (data) => {
    let isValidData = true;
    data.messages?.map((item) => {
      if (item.role === "assistant" && responseFormat !== "markdown") {
        isValidData = item.content = checkValidJSON(item.content);
      }
      return item;
    });
    
    if (!isValidData) {
      return toast.error("CONTENT_OF_ROLE_ASSISTANT_NOT_VALID_JSON");
    }
    
    if (!dataModal) {
      const response = await createConversation({ datasetId: recordId, ...data }, true);
      if (response) {
        toast.success("CREATE_CONVERSATION_SUCCESS");
        closeModal();
        getDataConversations();
      }
    } else {
      const response = await editConversation(dataModal._id, data, true);
      if (response) {
        toast.success("EDIT_CONVERSATION_SUCCESS");
        closeModal();
        getDataConversations();
      }
    }
  };
  const onChangeCheckBox = (e) => {
    setDefaultDataset(e.target.checked);
  };
  
  const handleChangeRole = (value, index) => {
    const newSelectedRoles = [...selectedRoles];
    newSelectedRoles[index] = value;
    setSelectedRoles(newSelectedRoles);
  };
  
  const handleAddMessage = (callback) => {
    callback();
    setSelectedRoles([...selectedRoles, ""]);
  };
  
  const handleRemoveMessage = (index, name, callback) => {
    callback(name);
    const newSelectedRoles = [...selectedRoles];
    newSelectedRoles.splice(index, 1);
    setSelectedRoles(newSelectedRoles);
  };
  
  return (
    <div className="create-dataset-container">
      <div className="tool-studio-header">
        <span className="tool-studio-header__title">{recordId ? t("REFINED_DATASET") : t("CREATE_REFINED_DATASET")}</span>
      </div>
      <div>
        <Form onFinish={onFinish} layout="vertical" form={formDataset} size={"large"}>
          <Row gutter={24}>
            <Col md={12} lg={12} sm={24} xs={24}>
              <Form.Item label={t("NAME")} name="name" rules={[{ required: true, message: t("NAME_BLANK") }]}>
                <Input placeholder={"Enter name"} />
              </Form.Item>
            </Col>
            <Col md={12} lg={12} sm={24} xs={24}>
              <Form.Item label={t("DEFAULT_DATASET")} name="isDefault" valuePropName="checked">
                <Checkbox onChange={onChangeCheckBox} />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={24} justify="center" className="gap-4">
            <Form.Item className="save-button">
              <AntButton type={BUTTON.GHOST_WHITE} onClick={handleCancel} size={"large"}>
                {t("CANCEL")}
              </AntButton>
            </Form.Item>
            <Form.Item className="save-button">
              <AntButton type={BUTTON.DEEP_NAVY} htmlType="submit" size={"large"}>
                {recordId ? t("SAVE") : t("CREATE")}
              </AntButton>
            </Form.Item>
          </Row>
        </Form>
      </div>
      {recordId && (
        <div className="conversations-details">
          <div className="conversations-details-header">
            <span className="conversations-details-header__title">{t("DETAILS_CONVERSATION")}</span>
            <AntButton type={BUTTON.DEEP_NAVY} onClick={openModal} size={"large"}>
              {t("CREATE")}
            </AntButton>
          </div>
          <TableAdmin
            columns={columns}
            dataSource={dataConversations}
            pagination={{ hideOnSinglePage: true }}
            scroll={{ x: 1000 }}
          />
        </div>
      )}
      <Modal
        title={dataModal ? t("EDIT_CONVERSATION") : t("CREATE_NEW_CONVERSATION")}
        open={isShowModal}
        width="91%"
        className={"modal-createConversation"}
        onCancel={closeModal}
        footer={null}
      >
        <Form
          name="basic"
          layout="vertical"
          onFinish={formSubmit}
          size={"large"}
          className="form-conversation-details"
          form={formConversation}
        >
          <Form.List name="messages" initialValue={[{ role: null }]}>
            {(fields, { add, remove }) => (
              <>
                <div className="select-options-item">
                  <label>{t("LIST_MESSAGE")}</label>
                </div>
                {fields.map(({ key, name, ...restField }, index) => {
                  const isShowCKEditor = outputTypeCode === "html" && selectedRoles[index] === "assistant";
                  return (
                    <Space
                      key={key}
                      style={{
                        display: "flex",
                        alignItems: "flex-start",
                      }}
                      align="baseline"
                    >
                      <Form.Item
                        {...restField}
                        name={[name, "role"]}
                        rules={[{ required: true, message: "Type can't be blank!" }]}
                      >
                        <Select options={ROLE_CONVERSATION} placeholder={"Select role"}
                                onChange={(value) => handleChangeRole(value, index)}
                        />
                      </Form.Item>
                      {isShowCKEditor
                        ? (<Form.Item
                          name={[name, "content"]}
                          rules={[{ required: true, message: "Content can't be blank!" }]}
                          //valuePropName="data"
                          //getValueFromEvent={(event, editor) => {
                          //  return editor.getData();
                          //}}
                        >
                          <CustomCKEditor />
                        </Form.Item>)
                        : (<Form.Item
                            {...restField}
                            name={[name, "content"]}
                            rules={[
                              {
                                required: true,
                                message: "Content can't be blank!",
                              },
                            ]}
                          >
                            <Input.TextArea
                              autoSize={{
                                minRows: 1,
                              }}
                              placeholder={"Input content"}
                            />
                          </Form.Item>
                        )
                      }
                      
                      <AntButton
                        type={BUTTON.WHITE}
                        shape={"circle"}
                        className={"btn-cancel-add-conversation"}
                        icon={<CancelIcon />}
                        size={"small"}
                        onClick={() => handleRemoveMessage(index, name, remove)}
                      />
                    </Space>
                  );
                })}
                
                <Form.Item>
                  <div className={"btn-add-conversation"}>
                    <AntButton
                      shape={"circle"}
                      size={"large"}
                      type={BUTTON.WHITE_BLUE}
                      onClick={() => handleAddMessage(add)}
                      icon={<PlusIcon />}
                    ></AntButton>
                  </div>
                </Form.Item>
              </>
            )}
          </Form.List>
          <div className="modal-createConversation-action">
            <AntButton type={BUTTON.WHITE} onClick={closeModal} size={"large"}>
              {t("CANCEL")}
            </AntButton>
            <AntButton type={BUTTON.DEEP_NAVY} htmlType="submit" size={"large"}>
              {dataModal ? t("SAVE") : t("CREATE")}
            </AntButton>
          </div>
        </Form>
      </Modal>
    </div>
  );
}

export default CreateDataset;