import React, { useCallback, useEffect, useRef, useState } from "react";
import { useDropzone } from "react-dropzone";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import axios from "axios";

import { toast } from "@component/ToastProvider";
import Upload40 from "@component/SvgIcons/Upload/Upload40";
import AntButton from "@component/AntButton";
import UploadProgress from "@component/UploadProgress";

import { BUTTON } from "@constant";

import { getFileExtension } from "@common/functionCommons";
import { uploadImage } from "@services/File";
import { useProject } from "@app/pages/Project";
import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";

import "./UploadImage.scss";

function UploadImage({ isShowSelectImage, categorySelected, onSelectResourceImage, isDisabledContent }) {
  const { t } = useTranslation();
  const { projectData } = useProject();

  const [isUploading, setUploading] = useState(false);
  const [uploadPercent, setUploadPercent] = useState(0);
  const [file, setFile] = useState(null);
  const cancelTokenSourceRef = useRef(null);
  
  const acceptImg = ["jpg", "png"];
  
  useEffect(() => {
    return () => {
      handleCancelUpload();
    };
  }, []);
  
  function handleReject() {
    toast.warning({ description: t("WE_CAN_WORK_WITH_IMAGES").format(acceptImg.join(", ")) });
  }
  
  const handleCancelUpload = () => {
    cancelTokenSourceRef?.current?.cancel();
    cancelTokenSourceRef.current = null;
    setUploading(false);
    setUploadPercent(0);
    setFile(null);
  };
  
  async function onDrop([file]) {
    if (!file) return;
    setUploading(true);
    setFile(file);
    cancelTokenSourceRef.current = axios.CancelToken.source();
    
    const axiosConfig = {
      onUploadProgress: handleProgress,
      cancelToken: cancelTokenSourceRef.current.token,
    };
    const response = await uploadImage(file, projectData.workspaceId, axiosConfig);
    if (response?.success) {
      const imageData = {
        imageFileId: {
          _id: response?.data?.imageFileId?._id || response?.data?.imageFileId,
          name: response?.data?.imageFileId?.name || response?.data?.name,
        },
        _id: response?.data?._id,
      };
      onSelectResourceImage(imageData);
      setUploadPercent(100);
    }
    setUploading(false);
  }
  
  const handleProgress = (progressEvent) => {
    const percent = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
    setUploadPercent(Math.min(percent, 99));
  };
  
  const handleDrop = useCallback(async (acceptedFiles, fileRejections) => {
    if (fileRejections.length) {
      handleReject();
    } else {
      onDrop(acceptedFiles);
    }
  }, []);
  
  const handlePaste = useCallback(async (event) => {
    const images = [];
    const items = (event.clipboardData || event.originalEvent.clipboardData).items;
    for (let index in items) {
      const item = items[index];
      if (item.kind === "file" && item.type.includes("image")) {
        const blob = item.getAsFile();
        const fileExtension = getFileExtension(blob.name);
        if (acceptImg?.length && !!acceptImg?.includes(fileExtension)) {
          images.push(blob);
        }
      }
    }
    
    if (items.length === images.length) {
      onDrop(images);
    } else {
      handleReject();
    }
  }, []);
  
  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop: handleDrop,
    disabled: isDisabledContent,
    noClick: true,
    accept: {
      "image/jpg": [".jpg"],
      "image/png": [".png"],
    },
    multiple: false,
  });
  
  if (isUploading) {
    return <UploadProgress fileBlob={file} percent={uploadPercent} onCancel={handleCancelUpload} />;
  }
  
  return <div className={clsx("upload-image-container", { "disabled-upload": isDisabledContent })}>
    
    <div {...getRootProps()} onPaste={handlePaste}>
      <input {...getInputProps()} />
      
      <div className="upload-image-content">
        <div className="upload-image-inner">
          <div className="">
            <Upload40 />
          </div>
          <div className="upload-image__title">
            {t("DROP_YOUR_FILE_HERE")}
          </div>
          <div className="upload-image__description">
            {t("WE_CAN_WORK_WITH")} JPG, PNG
          </div>
          <div className="upload-image__description">
            {t("DROPZONE_IMAGE_DESCRIPTION")}
          </div>
          
          <div className="upload-image__select-image">
            <AntButton
              size="large"
              type={BUTTON.DEEP_NAVY}
              onClick={open}
            >
              {t("SELECT_IMAGE")}
            </AntButton>
          </div>
        </div>
      </div>
    </div>
  </div>;
}

export default UploadImage;