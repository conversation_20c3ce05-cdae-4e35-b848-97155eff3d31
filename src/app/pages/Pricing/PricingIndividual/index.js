import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

import { getAllFeature, getAllPackage } from "@src/app/services/Package";
import { getAvailablePromotions } from "@src/app/services/Promotion";
import { getCurrentPackage } from "@src/app/services/Subscription";

import { CONSTANT, PRICING_TYPE } from "@constant";
import PackageBase from "./PackageBase";
import PackageAddon from "./PackageAddon";
import PackageVinaphone from "./PackageVNPT";
const PricingIndividual = ({ pricingType }) => {
  const { t } = useTranslation();

  const [dataPackages, setDataPackages] = useState([]);
  const [dataVNPT, setDataVNPT] = useState([]);

  useEffect(() => {
    getData();
    getDataVNPT();
  }, []);

  const getDataVNPT = async () => {
    const query = { type: 'vnpt' };
    try {
      const result = await getAllPackage(query);
      setDataVNPT(result);
    } catch (error) {
      console.log(error)
    }
  }

  const getData = async () => {
    const query = { customerTarget: "student" };
    const [
      packageResponse,
      featuresResponse,
      promotionResponse,
      currentPackageResponse] = await Promise.all([
        getAllPackage(query),
        getAllFeature(),
        getAvailablePromotions(),
        getCurrentPackage()
      ]);
    if (packageResponse.length) {
      const reparePackage = packageResponse.sort((a, b) => a.order - b.order).map((item) => {
        const featuresData = [];
        if (featuresResponse.length) {
          Object.entries(item.features).forEach(([featureId, value]) => {
            const feature = featuresResponse.find((item) => item._id === featureId);
            featuresData.push({
              name: feature?.localization?.description,
              unit: feature?.unit,
              count: feature?.type === "Boolean" ? 0 : value,
            });
          });
        }
        if (item?.prices.length && promotionResponse) {
          const promotionPackage = promotionResponse?.[item._id] || {};
          Object.entries(promotionPackage).forEach(([key, value]) => {
            item.prices[key - 1] = {
              ...item.prices[key - 1],
              promotion: value?.discount,
              promotionType: value?.type,
              priceIndex: key
            }
          })
        }
        if (currentPackageResponse?.length && currentPackageResponse.some((current) => current.packageId._id === item._id)) {
          item.isUsingPackage = true;
        }
        return {
          ...item,
          features: featuresData,
        };
      });
      setDataPackages(reparePackage);
    }
  };

  const { packagesBase, packagesAddon } = useMemo(() => {
    let packagesBase = [];
    let packagesAddon = [];
    dataPackages.forEach((item) => {
      if (!!item?.features && item?.type === 'base') {
        packagesBase.push(item);
      } else if (!!item?.features && item?.type === CONSTANT.ADDON) {
        packagesAddon.push(item);
      }
    });
    return { packagesBase, packagesAddon };
  }, [dataPackages]);

  if (pricingType !== PRICING_TYPE.INDIVIDUAL) return null;

  return (
    <>
      <PackageBase packagesBase={packagesBase} />
      {!!packagesAddon.length && <div className="pay-per-submission">
        <span className="pay-per-submission__title">{t("PAY_PER_SUBMISSION")}</span>
        <span className="pay-per-submission__text">{t("PAY_PER_SUBMISSION_DESCRIPTION")}</span>
      </div>}
      <PackageAddon packagesAddon={packagesAddon} />
      {!!dataVNPT.length && <div className="pay-per-submission">
        <span className="pay-per-submission__title">{t("EXCLUSIVELY_FOR_VINAPHONE_SUBSCRIBERS")}</span>
      </div>}
      <PackageVinaphone dataVNPT={dataVNPT} />
    </>
  );
}

export default PricingIndividual;