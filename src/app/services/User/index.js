import { API } from "@api";
import { createBase, deleteBase, getAllBase, updateBase, getAllManager, getDetailBase, getAllPaginationBase } from "@services/Base";
import { cloneObj } from "@src/common/functionCommons";
import axios from "axios";

export function getAllUser(query, loading = false, searchLike = []) {
  return getAllBase(API.USERS, query, null, loading, searchLike);
}

export function removeUserFromOrg(id) {
  return deleteBase(API.REMOVE_USER_ORG, id, false, true);
}

export function updateUser(data) {
  return updateBase(API.USER_ID, data);
}

export function confirmRegister(data) {
  return axios
    .post(API.CONFIRM_REGISTER, data, { hideNoti: true })
    .then(response => {
      return {
        data: response.data,
        code: response.status,
      };
    })
    .catch((err) => {
      return err?.response?.data;
    });
}

export async function confirmInvatation(token, data) {
  return axios
    .post(API.CONFIRM_INVITATION, data, {
      headers: { Authorization: `Bear<PERSON> ${token}` },
      hideNoti: true
    })
    .then(response => {
      if (response?.status === 200) return {
        email: response.data?.email,
        code: response.status
      };
      return null;
    })
    .catch((err) => {
      return {
        email: err?.response?.data?.data?.email,
        code: err?.response?.status,
        message: err?.response?.data?.message
      }
    });
}

export async function rejectInvatation(token, data) {
  return axios
    .post(API.REJECT_INVITATION, data, {
      headers: { Authorization: `Bearer ${token}` },
      hideNoti: true
    })
    .then(response => {
      if (response?.status === 200) return {
        email: response.data?.email,
        code: response.status,
        message: response.data?.message
      };
      return null;
    })
    .catch((err) => {
      return {
        email: err?.response?.data?.data?.email,
        code: err?.response?.status,
        message: err?.response?.data?.message
      }
    });
}

export async function activateAccount(token, data) {
  return axios
    .post(API.USER_ACTIVE_ACCOUNT, data, {
      headers: { Authorization: `Bearer ${token}` },
      hideNoti: true
    })
    .then(response => {
      if (response?.status === 200) return {
        email: response.data?.email,
        code: response.status,
        message: response.data?.message
      };
      return null;
    })
    .catch((err) => {
      return {
        email: err?.response?.data?.data?.email,
        code: err?.response?.status,
        message: err?.response?.data?.message
      }
    });
}

export async function getOnePermissionID(id) {
  return axios
    .get(API.GET_ONE_PERMISSION_ID.format(id))
    .then(response => {
      if (response?.data) return response?.data;
    })
    .catch((err) => {
      return err?.response?.data;
    });
}

export async function updateDeveloperForUser(id) {
  return axios.put(API.USER_DEVELOPER.format(id))
    .then(response => {
      return response?.data;
    })
    .catch((err) => {
      return err?.response?.data;
    });
}

export function updateUserPersona(data) {
  return updateBase(API.UPDATE_USER_PERSONA, data);
}
export function updateUserHearAboutUs(data) {
  return updateBase(API.UPDATE_USER_HEAR_ABOUT_US, data);
}
export function resendActivationEmail(data) {
  return createBase(API.RESEND_EMAIL_ACTIVATION, data);
}

export async function findOneUser(query) {
  return axios
    .get(API.USER_FIND_ONE, { params: query })
    .then(response => {
      if (response?.data) return response?.data;
    })
    .catch((err) => {
      return err?.response?.data;
    });
}


export async function getUsersOrg() {
  return axios
    .get(API.USER_ORGANIZATION)
    .then(response => {
      if (response?.data) return response?.data;
    })
    .catch((err) => {
      return err?.response?.data;
    });
}

export const findAllUser = (paging, query, searchField) => {
  const queryObj = cloneObj(query);
  queryObj.sort = queryObj.sort || "-createdAt";
  return getAllPaginationBase(API.USERS, paging, queryObj, searchField);
}