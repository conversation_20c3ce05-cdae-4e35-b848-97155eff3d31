import { Affix } from "antd";
import SegmentedPreview from "@app/pages/Project/ProjectDetail/SegmentedPreview";
import OutlineList from "@app/pages/Project/ProjectDetail/Outline/OutlineList";
import React from "react";

function OutlineDesktop(){
  
  return <div className="project__outline">
    <Affix offsetTop={32} target={() => document.getElementById("js-layout-content")}>
      <div>
        <div className="project__switch-preview">
          <SegmentedPreview />
        </div>
        <OutlineList />
      </div>
    </Affix>
  </div>;
}

export default OutlineDesktop