import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";

const IdeasSuggest = ({ ideas = [] }) => {
  const { t } = useTranslation();
  
  if (!ideas?.length) return null;
  
  return <div className="evaluation__content__item evaluation-improved-essay">
    <div className="content__item__title evaluation-improved-essay__title">{t("IDEA_SUGGESTION")}</div>
    {Array.isArray(ideas) ? <ul>
        {ideas.map((idea, index) => (
          <li key={index}>{idea}</li>
        ))}
      </ul>
      : <HtmlContent dangerouslySetInnerHTML={{ __html: ideas }}/>}
  </div>;
};

export default IdeasSuggest;