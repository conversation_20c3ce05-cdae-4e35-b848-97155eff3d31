import React, {useState, useEffect} from "react";
import {useTranslation} from "react-i18next";
import {Card, Form, Row, Col, Input, Select, DatePicker} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import dayjs from "dayjs";

import Loading from "@src/app/component/Loading";
import {AntForm} from "@component/AntForm";
import AntButton from "@component/AntButton";
import TableAdmin from "@src/app/component/TableAdmin";
import {getPaginationTransactions} from "@src/app/services/Transaction";
import {getAllPackage} from "@src/app/services/Package";
import {renderMoney, formatDateTime, handleSearchParams, paginationConfig} from "@src/common/functionCommons";
import {CONSTANT, LANGUAGE, TRANSACTION_STATUS, BUTTON, PAGINATION_INIT} from "@constant";

import "./AdminTransactionHistory.scss";

const {RangePicker} = DatePicker;
const {Option} = Select;

function AdminTransactionHistory() {
  const {t, i18n} = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [formSearchTransaction] = Form.useForm();
  const [dataTransaction, setDataTransaction] = useState(PAGINATION_INIT);
  const [dataPackages, setDataPackages] = useState([]);
  const [isShowSelectDate, setShowSelectDate] = useState(false);
  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);
  const location = useLocation();
  const navigate = useNavigate();
  const {query, paging} = handleSearchParams(location.search);

  useEffect(() => {
    getPackagesData();
    getTransactionData(paging, query);
  }, [location.search]);

  const pagination = paginationConfig(dataTransaction.paging, dataTransaction.query, i18n.language);

  const getPackagesData = async () => {
    try {
      const packagesResponse = await getAllPackage();
      if (packagesResponse) {
        setDataPackages(packagesResponse);
      }
    } catch (error) {
      console.error("Error fetching packages:", error);
    }
  };

  const getTransactionData = async (paging = dataTransaction.paging, query = dataTransaction.query) => {
    setIsLoading(true);

    // Handle date display logic
    const { time, fromDate, toDate } = query;
    const newQuery = {
      ...query,
      ...fromDate ? { fromDate: dayjs(fromDate * 1000) } : {},
      ...toDate ? { toDate: dayjs(toDate * 1000) } : {},
    };

    setShowSelectDate(time === "custom");
    setMinDate(newQuery?.fromDate);
    setMaxDate(newQuery?.toDate);

    formSearchTransaction.setFieldsValue({
      email: query?.email,
      status: query?.status,
      packageId: query?.packageId,
      time: query?.time,
      fromDate: newQuery?.fromDate,
      toDate: newQuery?.toDate,
    });

    try {
      // Build query object for API
      const apiQuery = {};

      // Add sort to query if exists
      if (query?.sort) {
        apiQuery.sort = query.sort;
      }

      // Add filters to query
      if (query?.email && query.email.trim()) {
        apiQuery.email = query.email.trim();
      }

      if (query?.status) {
        apiQuery.state = query.status;
      }

      if (query?.packageId) {
        apiQuery.packageId = query.packageId;
      }

      // Add time filter - ALWAYS include time field if present
      if (query?.time) {
        apiQuery.time = query.time;
      }

      // Add date range filter
      if (query?.time === 'custom' && query?.fromDate && query?.toDate) {
        // Custom date range
        apiQuery.fromDate = query.fromDate;
        apiQuery.toDate = query.toDate;
      } else if (query?.time && query?.time !== 'custom') {
        // Handle predefined time ranges
        const now = dayjs();
        switch (query.time) {
          case 'week':
            apiQuery.fromDate = now.startOf('week').unix();
            apiQuery.toDate = now.endOf('week').unix();
            console.log("Adding week range:", { fromDate: apiQuery.fromDate, toDate: apiQuery.toDate }); // Debug log
            break;
          case 'month':
            apiQuery.fromDate = now.startOf('month').unix();
            apiQuery.toDate = now.endOf('month').unix();
            console.log("Adding month range:", { fromDate: apiQuery.fromDate, toDate: apiQuery.toDate }); // Debug log
            break;
        }
      }

      const dataResponse = await getPaginationTransactions(paging, apiQuery);
      if (dataResponse) {

        setDataTransaction({
          rows: dataResponse.rows || [],
          paging: {
            page: paging.page,
            pageSize: paging.pageSize,
            total: dataResponse.total || 0
          },
          query: query
        });
      }
    } catch (error) {
      console.error("Error fetching transaction data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const submitFormFilter = (formData) => {

    const { fromDate, toDate, time, email, status, packageId } = formData;

    const newQuery = {};

    // Add basic filters
    if (email && email.trim()) {
      newQuery.email = email.trim();
    }
    if (status) {
      newQuery.status = status;
    }
    if (packageId) {
      newQuery.packageId = packageId;
    }

    if (time) {
      newQuery.time = time;

      // Add custom date range only if custom is selected
      if (time === 'custom' && fromDate && toDate) {
        newQuery.fromDate = dayjs(fromDate)?.startOf("day")?.unix();
        newQuery.toDate = dayjs(toDate)?.endOf("day")?.unix();
      }
    }
    const urlParams = new URLSearchParams(newQuery).toString();

    navigate(`?${urlParams}`);
  };

  const clearFormFilter = () => {
    setShowSelectDate(false);
    formSearchTransaction.resetFields();
    navigate("");
  };

  const handleChangeSelectTime = (value) => {
    if (value === "custom") {
      setShowSelectDate(true);
    } else {
      setShowSelectDate(false);
      formSearchTransaction.setFieldsValue({ fromDate: null, toDate: null });
    }
  };

  const handleChangeTable = (pagination, filters, sorter) => {
    const {current, pageSize} = pagination;
    const params = new URLSearchParams(location.search);
    params.set('page', current);
    params.set('pageSize', pageSize);

    // Handle sorting
    if (sorter && sorter.field) {
      const sortOrder = sorter.order === 'ascend' ? '' : '-';
      params.set('sort', `${sortOrder}${sorter.field}`);
    } else if (!sorter || !sorter.field) {
      // Remove sort if no sorting is applied
      params.delete('sort');
    }

    navigate(`?${params.toString()}`);
  };

  const formatDate = (dateTime) => {
    const formatString = dayjs.locale() === LANGUAGE.EN ? "MMM-DD-YYYY HH:mm" : "DD-MM-YYYY HH:mm";
    return dayjs(dateTime).format(formatString);
  };

  const renderTransactionStatus = (status, responseCode) => {
    switch (status) {
      case TRANSACTION_STATUS.DONE:
        return <span className="transaction-status-done">{t("SUCCESSFUL_TRANSACTION")}</span>;
      case TRANSACTION_STATUS.ERROR:
        if (responseCode === "24") {
          return <span className="transaction-status-cancel">{t("TRANSACTION_CANCELED")}</span>;
        } else {
          return <span className="transaction-status-error">{t("TRANSACTION_FAILED")}</span>;
        }
      default:
        return <span className="transaction-status-processing">{t("TRANSACTION_PROCESSING")}</span>;
    }
  };

  const orderColumn = (paging) => ({
    title: t("ORDER"),
    key: "order",
    width: 80,
    align: "center",
    render: (_, __, index) => (
      <span>{(paging.page - 1) * paging.pageSize + index + 1}</span>
    ),
  });

  const columns = [
    orderColumn(dataTransaction.paging),
    {
      title: t("USER_EMAIL"),
      key: "userEmail",
      width: 250,
      render: (text, record) => {
        const userEmail = record.userId?.email || record.subscriptionId?.customerId?.email || "N/A";
        return <span className="user-email-value">{userEmail}</span>;
      },
    },
    {
      title: t("REGISTRATION_PACKAGE"),
      key: "package",
      width: 200,
      align: "center",
      render: (text, record) => {
        const packageName = record.subscriptionId?.packageId?.name;
        const unitPrice = record.subscriptionId?.unitPrice;
        const packageTerm = unitPrice?.toUpperCase() === CONSTANT.MONTH ? t("MONTHLY") : t("YEARLY");

        return (
          <>
            <div className="package-name-value">{packageName}</div>
            <div className="package-info__unit-price">{packageTerm}</div>
          </>
        );
      },
    },
    {
      title: t("TOTAL_AMOUNT"),
      key: "cost",
      dataIndex: "cost",
      width: 150,
      align: "center",
      render: (value) => <span className="amount-value">{renderMoney(value)}</span>,
    },
    {
      title: t("TRANSACTION_DATE"),
      key: "createdAt",
      dataIndex: "createdAt",
      width: 200,
      align: "center",
      sorter: true,
      sortDirections: ['descend', 'ascend'],
      defaultSortOrder: 'descend',
      render: (text, record) => formatDateTime(record?.createdAt),
    },
    {
      title: t("TRANSACTION_STATUS"),
      key: "status",
      width: 150,
      align: "center",
      render: (value, record) => renderTransactionStatus(record?.state, record?.responseCode),
    },
    {
      title: t("TRANSACTION_ID"),
      key: "transactionId",
      dataIndex: "_id",
      width: 150,
      align: "center",
      render: (text) => (
        <span className="transaction-id" title={text}>
          {text?.substring(0, 8)}...
        </span>
      ),
    },
  ];

  return (
    <Loading active={isLoading} transparent>
      <div className="transaction-history-container">
        <Card className="transaction-history-info-card">
          <div className="transaction-history-info-header">
            <div>
              <h1 className="transaction-history-title">{t("TRANSACTION_HISTORY_MANAGEMENT")}</h1>
              <p className="transaction-history-description">
                {t("TRANSACTION_HISTORY_MANAGEMENT_DESCRIPTION")}
              </p>
            </div>
          </div>
        </Card>

        <Card className="transaction-history-search-card">
          <AntForm form={formSearchTransaction} size={"large"} className="form-filter" onFinish={submitFormFilter}>
            <Row gutter={24} className="grow">
              {/*<Col xs={24} md={12} lg={6}>*/}
              {/*  <AntForm.Item name={"email"} className="search-form-item">*/}
              {/*    <Input*/}
              {/*      placeholder={t("SEARCH_BY_EMAIL")}*/}
              {/*      allowClear*/}
              {/*    />*/}
              {/*  </AntForm.Item>*/}
              {/*</Col>*/}
              <Col xs={24} md={12} lg={6}>
                <AntForm.Item name={"packageId"} className="search-form-item">
                  <Select
                    placeholder={t("SELECT_PACKAGE")}
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {dataPackages.map((pkg) => (
                      <Option key={pkg._id} value={pkg._id} label={pkg.name}>
                        {pkg.name}
                      </Option>
                    ))}
                  </Select>
                </AntForm.Item>
              </Col>
              <Col xs={24} md={12} lg={6}>
                <AntForm.Item name={"status"} className="search-form-item">
                  <Select
                    placeholder={t("SELECT_STATUS")}
                    allowClear
                  >
                    <Option value={TRANSACTION_STATUS.DONE}>{t("SUCCESSFUL_TRANSACTION")}</Option>
                    <Option value={TRANSACTION_STATUS.ERROR}>{t("TRANSACTION_FAILED")}</Option>
                    <Option value={TRANSACTION_STATUS.PROCESSING}>{t("TRANSACTION_PROCESSING")}</Option>
                  </Select>
                </AntForm.Item>
              </Col>

              <Col xs={24} md={12} lg={6}>
                <AntForm.Item name={"time"} className="search-form-item">
                  <Select
                    placeholder={t("SELECT_TIME")}
                    onChange={handleChangeSelectTime}
                    allowClear
                  >
                    <Option value={"week"}>{t("THIS_WEEK")}</Option>
                    <Option value={"month"}>{t("THIS_MONTH")}</Option>
                    <Option value={"custom"}>{t("CUSTOM")}</Option>
                  </Select>
                </AntForm.Item>
              </Col>
              {isShowSelectDate && (
                <>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item
                      name={"fromDate"}
                      className="search-form-item"
                      rules={[
                        () => ({
                          validator(_, value) {
                            if (!!value) {
                              return Promise.resolve();
                            }
                            return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                          },
                        }),
                      ]}
                    >
                      <DatePicker
                        placeholder={t("SELECT_FROM_DATE")}
                        size="large"
                        className="filter-form__date-picker"
                        format="DD/MM/YYYY"
                        maxDate={maxDate}
                        onChange={setMinDate}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item
                      name={"toDate"}
                      className="search-form-item"
                      rules={[
                        () => ({
                          validator(_, value) {
                            if (!!value) {
                              return Promise.resolve();
                            }
                            return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                          },
                        }),
                      ]}
                    >
                      <DatePicker
                        placeholder={t("SELECT_TO_DATE")}
                        size="large"
                        className="filter-form__date-picker"
                        format="DD/MM/YYYY"
                        minDate={minDate}
                        onChange={setMaxDate}
                      />
                    </AntForm.Item>
                  </Col>
                </>
              )}
            </Row>
            <Row justify="end" className="search-buttons-row">
              <Col>
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} onClick={clearFormFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} htmlType={"submit"}>
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="transaction-history-table-card">
          <TableAdmin
            columns={columns}
            dataSource={dataTransaction.rows}
            scroll={{x: 1000}}
            rowKey="_id"
            pagination={pagination}
            onChange={handleChangeTable}
            className="transaction-history-table"
            rowClassName={() => "transaction-history-table-row"}
            locale={{emptyText: t("NO_TRANSACTIONS_FOUND")}}
          />
        </Card>
      </div>
    </Loading>
  );
}

export default AdminTransactionHistory;
