.preview-image-container {
  display: flex;
  flex-direction: column;

  &::after {
    content: '';
    width: 100%;
    height: 1px;
    background: #DBDBDB;
  }

  .images-preview {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 4px;
    margin-bottom: 24px;

    .sortable-container {
      display: contents;
    }

    .images-preview__item {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      aspect-ratio: 1/1;
      border: 1px solid #DBDBDB;
      border-radius: 8px;
      box-sizing: border-box;

      img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 100%;
        max-height: 100%;
        object-fit: cover;
        object-position: center;
      }

      .item__name {
        position: absolute;
        top: calc(100% + 2px);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: 100%;
        text-align: left;
        font-size: 10px;
        line-height: 16px;
      }

      .item__backdrop {
        position: absolute;
        top: -1px;
        bottom: -1px;
        left: -1px;
        right: -1px;
        background-color: #00000066;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:not(.item__backdrop-uploading) {
          cursor: pointer;
        }

        img {
          width: 16px;
          height: 16px;
        }


        .item__loading {
          width: 40px;
          height: 40px;
        }

        .item__percent {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 10px;
          line-height: 16px;
          color: #FFFFFF;
        }

        .item__delete {
          position: absolute;
          top: 8px;
          right: 8px;
          width: 16px !important;
          min-width: 16px !important;
          height: 16px !important;
          background-color: unset;
          border: none !important;

          svg {
            width: 16px;
            height: 16px;
          }

          .ant-btn-icon svg:not(.svg-fill) path,
          svg:not(.svg-fill) path {
            stroke: var(--white);
          }

          .ant-btn-icon svg.svg-fill path,
          svg.svg-fill path {
            fill: var(--white);
          }
        }
      }

      &:hover {
        .item__order {
          display: none;
        }
      }

      &:not(:hover) {
        .item__backdrop {
          &:not(.item__backdrop-uploading) {
            display: none !important;
          }
        }
      }
    }

    &:has(.sortable-ghost),
    .previewing {
      .item__backdrop {
        background-color: unset;

        img {
          display: none;
        }

        button {
          display: none;
        }
      }

      .item__order {
        display: flex !important;
      }
    }

    .iimages-preview__upload {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      width: 100%;
      background-color: #ECF1F4CC;
      color: var(--primary-colours-blue-navy);
      aspect-ratio: 1/1;
    }
  }

  .writing-image-name {
    .ant-popover-inner-content {
      font-family: Inter, serif;
      font-size: 14px;
    }
  }
}