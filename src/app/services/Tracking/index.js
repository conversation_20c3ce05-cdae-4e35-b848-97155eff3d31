import axios from "axios";
import { API } from "@api";
import { createBase, deleteBase, getAllManager } from "@services/Base";
import { genQueryParam } from "@src/common/functionCommons";

export function createTracking(data) {
  return createBase(API.TRACKING_MANAGER, data);
}

export async function getTracking(query) {
  return axios
    .get(API.TRACKING_MANAGER, { params: query })
    .then((response) => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return null;
    });
};

export async function downloadTracking(query) {
  return axios
    .get(`${API.TRACKING_MANAGER}/download`, {
      params: query,
      responseType: 'arraybuffer'
    })
    .then((response) => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return null;
    });
};
