import { useMemo } from "react";
import { Breadcrumb } from "antd";
import { Link, matchPath, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import STUDENT_BREADCRUMB from "./studentBreadcrumb";

import BREADCRUMB_ARROW from "@src/asset/icon/breadcrumb-arrow.svg";

import "./StudentBreadcrumb.scss";

const SEPARATOR = <img src={BREADCRUMB_ARROW} alt="" />;

function StudentBreadcrumb({ breadcrumbData, availableWorkspaces, subTitle, ...props }) {
  const { t } = useTranslation();
  const { pathname } = useLocation();

  const breadcrumbItems = useMemo(() => {
    const breadcrumbList = [];
    const breadcrumb = STUDENT_BREADCRUMB.find(breadcrumb => matchPath(breadcrumb.path, pathname));
    if (breadcrumb) {
      breadcrumb.items.forEach(item => {
        let url, title;
        url = item.url;
        title = t(item.lang);

        if (title) {
          breadcrumbList.push({
            title: url ? <Link to={url}>{title}</Link> : title,
          });
        }
      });
      if (subTitle) {
        breadcrumbList.push({
          title: subTitle,
        });
      }
    }

    return breadcrumbList;
  }, [t, pathname, breadcrumbData, availableWorkspaces, subTitle]);

  if (!breadcrumbItems.length) return null;

  return <div className="student-breadcrumb">
    <Breadcrumb
      separator={SEPARATOR}
      items={breadcrumbItems}
    />
  </div>;
}

function mapStateToProps(store) {
  const { breadcrumbData } = store.app;
  const { availableWorkspaces } = store.workspace;
  return { breadcrumbData, availableWorkspaces };
}

export default connect(mapStateToProps)(StudentBreadcrumb);