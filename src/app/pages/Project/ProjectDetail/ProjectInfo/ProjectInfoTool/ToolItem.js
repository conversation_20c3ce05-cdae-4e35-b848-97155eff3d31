import React from "react";
import { connect } from "react-redux";
import { Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import { useProject } from "@app/pages/Project";
import { favoriteTool, unFavoriteTool } from "@services/Tool";

import Heart from "@component/SvgIcons/Heart";
import HeartEmpty from "@component/SvgIcons/Heart/HeartEmpty";

import * as tool from "@src/ducks/tool.duck";

function ToolItem({ toolData, ...props }) {
  
  const { isAllowEditing, addContentBlock, setToolDemoState } = useProject();
  const { t, i18n } = useTranslation();
  
  async function handleCreateContent(toolId) {
    if (!isAllowEditing) return;
    addContentBlock({ toolId });
  }
  
  async function handleFavTool(toolId, isFavorite) {
    let dataResponse;
    if (isFavorite) {
      dataResponse = await unFavoriteTool(toolId, true);
    } else {
      dataResponse = await favoriteTool(toolId, true);
    }
    if (dataResponse) {
      props.updateFavTool(toolId, !isFavorite);
    }
  }
  
  const handleOpenToolDemo = () => {
    setToolDemoState(prevState => ({ ...prevState, open: true, link: toolData.linkYoutube }));
  };
  
  const toolTipTitle = <div className="tool-tooltip__title" onClick={handleOpenToolDemo}>
    {t("VIEW_VIDEO_DEMO")}
  </div>;
  
  return <Tooltip
    //open={!!toolData.linkYoutube}
    title={toolTipTitle}
    overlayClassName={clsx("tool-tooltip",
      //{ "tool-tooltip-hidden": !toolData.linkYoutube }
    )}
    {...!toolData.linkYoutube ? { open: false } : {}}
    placement="topLeft"
  >
    <div
      className={clsx("tool-category__tool", { "tool-category__tool-disabled": !isAllowEditing })}
      onClick={() => handleCreateContent(toolData._id)}
    >
      <div className="tool-category__tool-fav">
        <div
          className="tool-category__tool-fav-icon"
          onClick={(e) => {
            e.stopPropagation();
            handleFavTool(toolData._id, toolData.isFavorite);
          }}
        >
          {toolData.isFavorite ? <Heart /> : <HeartEmpty />}
        </div>
      
      </div>
      <div className="tool-category__tool-name">
        {toolData?.name}
      </div>
    </div>
  </Tooltip>;
}

function mapStateToProps(store) {
  return {};
}

const mapDispatchToProps = {
  ...tool.actions,
};
export default connect(mapStateToProps, mapDispatchToProps)(ToolItem);