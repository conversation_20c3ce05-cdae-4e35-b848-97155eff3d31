import "./SpeakingResult.scss";
import { useTranslation } from "react-i18next";
import React, { useEffect, useRef, useState } from "react";
import { useSpeaking } from "@app/pages/Student/Speaking";
import { Popover } from "antd";
import clsx from "clsx";
import useWindowDimensions from "@common/windowDimensions";
import AntButton from "@src/app/component/AntButton";
import VolumeUp from "@src/app/component/SvgIcons/Volume/VolumeUp";
import EarIcon from "@src/app/component/SvgIcons/EarIcon";
import ReactPlayer from "react-player";
import { API } from "@api";


function SpeakingResult({
  label,
  scoreBreakdown,
}) {
  const { t } = useTranslation();
  const { width, height } = useWindowDimensions();

  const { text } = useSpeaking();
  const { wordAnalyzed, showErrors } = useSpeaking();
  const { isShowFeedback } = useSpeaking();
  const { audioFileId } = useSpeaking();

  const [wordIpa, setWordIpa] = useState('');

  const playerUserRef = useRef(null);
  const playerIpaRef = useRef(null);

  /*useEffect(() => {
   const jsSpeakingResult = document.getElementById("js-speaking-result");
   if (!jsSpeakingResult) return;
   if (isShowFeedback) {
   const jsSpeakingTopic = document.getElementById("js-speaking-topic");
   const jsSpeakingAudioPlayer = document.getElementById("js-speaking-audio-player");
   //jsSpeakingResult.style.height = height - jsSpeakingTopic.offsetHeight - jsSpeakingAudioPlayer.offsetHeight - 503 + "px";
   } else {
   //jsSpeakingResult.style.height = "unset";
   }
   }, [isShowFeedback, width, height]);*/

  useEffect(() => {
    if (wordIpa) {
      playerIpaRef.current.getInternalPlayer().play();
    } else {
      playerIpaRef?.current?.getInternalPlayer()?.pause();
    }
  }, [wordIpa]);

  const onPlayUserPronounce = (currentTime, duration) => {
    playerUserRef.current.seekTo(currentTime / 1e7,  "seconds");
    playerUserRef.current.getInternalPlayer().play();
    setTimeout(() => { playerUserRef.current.getInternalPlayer().pause(); }, duration / 1e4);
  };

  const onPlayIpaPronounce = (word) => {
    setWordIpa(word);
    playerIpaRef?.current?.seekTo(0);
  };

  const renderPhonemeText = (score) => {
    let className = '';
    let text = '';
    switch (true) {
      case score >= 90:
        className = "phoneme__excellent";
        text = t("EXCELLENT");
        break;
      case score >= 60 && score < 90:
        className = "phoneme__almost";
        text = t("ALMOST");
        break;
      default:
        className = "phoneme__try-again";
        text = t("TRY_AGAIN");
    };
    return <span className={className}>{`${text}`}</span>;
  }

  const renderPhonemeClassName = (score) => {
    let className = '';
    switch (true) {
      case score >= 90:
        className = "phoneme__excellent";
        break;
      case score >= 60 && score < 90:
        className = "phoneme__almost";
        break;
      default:
        className = "phoneme__try-again";
    };
    return className;
  }

  function renderOneWord(word, key) {
    const { unexpectedBreak, missingBreak, mispronunciation, monotone } = word.errors;
    const renderTitle = () => {
      const overallClassName = renderPhonemeClassName(word.pronunciationAssessment?.accuracyScore);
      return <>
        <span className="speaking-word__overall-accuracy">
          {`${word.word} : ${word.pronunciationAssessment?.accuracyScore}%`}
        </span>

        <div className="speaking-word__phonetic-transcriptions">
          <span className="transcription transcription__user">
            <AntButton
              size="xsmall"
              icon={<VolumeUp />}
              onClick={() => onPlayIpaPronounce(word?.word)}
            />
            <span>{`/${word.phonemes.map((phoneme) => phoneme?.phoneme).join("")}/`}</span>
          </span>

          <span className="transcription">
            <AntButton
              size="xsmall"
              icon={<EarIcon />}
              onClick={() => onPlayUserPronounce(word?.offset, word?.duration)}
            />
            <span >
              <span className={overallClassName}>/</span>
              {word.phonemes.map((phoneme, index) => {
                return (<span key={index} className={renderPhonemeClassName(phoneme.pronunciationAssessment?.accuracyScore)}>
                  {phoneme?.phoneme}
                </span>)
              })}
              <span className={overallClassName}>/</span>
            </span>
          </span>
        </div>
      </>
    };

    const renderContent = () => {
      return <div className="speaking-word__phonemes-evaluation">
        {word.phonemes.map((phoneme, index) => {
          return <div key={index} className="phonemes-evaluation__item">
            <div className="item__divider" />
            <div className="item__content">
              <span className="item__phoneme">/{phoneme?.phoneme}/</span>
              <span>{`${phoneme.pronunciationAssessment?.accuracyScore}% - `}
                {renderPhonemeText(phoneme.pronunciationAssessment?.accuracyScore)}
              </span>
            </div>
          </div>;
        })}
      </div>
    }

    return <React.Fragment key={word?.word + key}>

      {unexpectedBreak && showErrors.unexpectedBreak && <span className="speaking-word__unexpected-break" />}

      {missingBreak && showErrors.missingBreak && <span className="speaking-word__missing-break" />}

      <Popover
        overlayClassName="speaking-word__popover"
        title={renderTitle()}
        content={renderContent()}
        trigger="hover"
        mouseEnterDelay={0.05}
        mouseLeaveDelay={0.05}
        destroyTooltipOnHide={true}
        placement="topLeft"
      >
        <span className="speaking-result__word">
          <span className={clsx({
            "speaking-word__mispronunciation": mispronunciation && showErrors.mispronunciation,
            "speaking-word__monotone": monotone && showErrors.monotone,
          })}>
            {`${word?.word}${word?.punctuation || ""}`}
          </span>
        </span>
      </Popover>
    </React.Fragment >;
  }

  return <div id="js-speaking-result" className="speaking-result">
    {wordAnalyzed?.map((word, index) => {
      return renderOneWord(word, index);
    })}
    {text?.split(" ")?.map((text, index) => {
      return <span key={index} className="speaking-result__word">
        <span>{text}</span>
      </span>;
    })}

    {!wordAnalyzed?.length && !text && <div className="speaking-result__placeholder">
      {t("SPEAKING_SCRIPT")}
    </div>}

    {audioFileId && <ReactPlayer
      ref={playerUserRef}
      url={API.STREAM_MEDIA.format(audioFileId)}
      playing={false}
      width="0"
      height="0"
    />
    }

    <ReactPlayer
      ref={playerIpaRef}
      url={wordIpa ? API.SPEAKING_WORD_IPA.format(wordIpa) : ""}
      onEnded={() => setWordIpa(null)}
      playing={false}
      width="0"
      height="0"
    />

  </div>;
}

export default SpeakingResult;