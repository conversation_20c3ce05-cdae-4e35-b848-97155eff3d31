import { API } from "@api";
import { getDetailBase, createBase, deleteBase, updateBase } from "@services/Base";
import axios from "axios";

export function getOrganizationDetail(id, loading) {
  return getDetailBase(API.ORGANIZATION_DETAIL, id);
}

export async function uploadOrganizationAvatar(file, organizationId, requestConfig = {}) {
  const formData = new FormData();
  formData.append("organizationId", organizationId);
  formData.append("file", file);
  const config = {
    ...requestConfig,
    headers: { "Content-Type": "multipart/form-data" }
  };
  return await axios
    .post(API.UPLOAD_ORG_AVATAR, formData, config)
    .then(response => {
      if (response.status === 200) {
        return response.data;
      }
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      // renderMessageError(err);
      return null;
    });
}

export async function getStatisticOrganization(params) {
  const config = { params };
  return axios.get(API.STATISTIC_ORG, config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function updateOrganization(data) {
  return updateBase(API.ORGANIZATION_ID, data);
}

export function deleteOrganization(id) {
  return deleteBase(API.ORGANIZATION_ID.format(id));
}

export function createOrganization(data) {
  return createBase(API.ORGANIZATION, data);
}

export async function getAllOrganization(query) {
  return axios
    .get(API.ORGANIZATION_MANAGER, { params: query })
    .then((response) => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return null;
    });
};