.instruction-detail-options{
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin: 0 -24px;
  .instruction-detail-options-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 24px 0 0 24px ;
    .btn-create-options {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;

    }
  }
  .instruction-detail__option-table{
    .instruction-detail__option-table-actions {
      display: flex;
      flex-direction: row;
      gap: 24px;
      justify-content: center;
      .btn-edit-options {
        &:hover {
          background: var(--background-hover);
        }
      }
    }
  }
}