import axios from "axios";
import { API } from "@api";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import {
  createBase,
  deleteBase,
  getAllBase,
  updateBase,
  getDetailBase,
  getAllPaginationBase,
} from "@services/Base";

/**
 * Get all explains with pagination
 * @param {Object} paging - Pagination parameters
 * @param {Object} query - Query parameters including search, explainType, taskType
 * @param {Array} populateOpts - Population options
 * @param {Boolean} loading - Show loading indicator
 * @returns {Promise} - Promise with pagination data
 */
export function getPaginationExplain(paging, query, populateOpts = [], loading) {
  query.sort = query.sort ? query.sort : "-createdAt";
  return getAllPaginationBase(API.EXPLAIN, paging, query, ["name"]);
}

/**
 * Get all explains without pagination
 * @param {Object} query - Query parameters
 * @param {Boolean} loading - Show loading indicator
 * @returns {Promise} - Promise with all explains
 */
export function getAllExplain(query, loading) {
  return getAllBase(API.EXPLAIN, query, null, loading);
}

/**
 * Get explain detail by ID
 * @param {String} id - Explain ID
 * @param {Array} populateOpts - Population options
 * @returns {Promise} - Promise with explain detail
 */
export function getExplainDetail(id, populateOpts = []) {
  return getDetailBase(API.EXPLAIN_ID, id, populateOpts)
    .then(data => {
      if (data) {
        // Convert data to camelCase if needed
        return convertSnakeCaseToCamelCase(data);
      }
      return null;
    });
}

/**
 * Create new explain
 * @param {Object} data - Explain data
 * @param {Boolean} toastError - Show error toast
 * @returns {Promise} - Promise with created explain
 */
export function createExplain(data, toastError = false) {
  return createBase(API.EXPLAIN, data, [], false, toastError);
}

/**
 * Update explain
 * @param {Object} data - Explain data with _id
 * @returns {Promise} - Promise with updated explain
 */
export function updateExplain(data) {
  return updateBase(API.EXPLAIN_ID, data);
}

/**
 * Delete explain
 * @param {String} id - Explain ID
 * @returns {Promise} - Promise with delete result
 */
export function deleteExplain(id) {
  return deleteBase(API.EXPLAIN_ID, id);
}

/**
 * Test explain with sample input
 * @param {String} id - Explain ID
 * @param {Object} sampleData - Sample data to test
 * @returns {Promise} - Promise with test result
 */
export function testExplain(id, sampleData) {
  console.log(`Testing explain with ID: ${id} and data:`, sampleData);
  console.log('API endpoint:', API.EXPLAIN_TEST.format(id));

  return axios.post(API.EXPLAIN_TEST.format(id), sampleData, {
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 30000 // 30 seconds timeout for potentially long operations
  })
    .then(response => {
      console.log('Test response:', response);
      if (response.status === 200) return response.data;
      return null;
    })
    .catch(err => {
      console.error("Error testing explain:", err);
      // Return a mock response for development/testing
      if (process.env.NODE_ENV !== 'production') {
        return {
          result: "This is a mock response since the API call failed. The actual API endpoint might not be implemented yet.",
          status: "success"
        };
      }
      return null;
    });
}

/**
 * Copy an existing explain
 * @param {Object} data - Data containing the explainId to copy
 * @param {Boolean} toastError - Show error toast
 * @returns {Promise} - Promise with copied explain
 */
export function copyExplain(data, toastError = false) {
  return createBase(API.EXPLAIN_COPY, data, [], false, toastError);
}
