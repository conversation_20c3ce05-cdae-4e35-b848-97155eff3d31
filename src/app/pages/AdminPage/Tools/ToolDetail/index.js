import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Col, Divider, Form, Input, InputNumber, Row, Select } from "antd";

import AntButton from "@component/AntButton";

import { BUTTON, CATEGORIES, LANG_OPTIONS, TOOL_INPUT_TYPE, TOOL_TYPE, VISIBLE_TOOL } from "@constant";
import { LINK } from "@link";

import { cloneObj, coverLangArrayToObject, coverLanguageObjectToArray } from "@common/functionCommons";

import { createTool, getAllGroupTool, getToolDetail, updateTool } from "@services/Tool";
import { getInstructions, getAllInstructions } from "@services/Instruction";
import { getAllOrg } from "@services/OrganizationUser";
import { toast } from "@component/ToastProvider";
import "./ToolDetail.scss";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import CancelIcon from "@component/SvgIcons/CancelIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";

const ToolDetail = ({ ...props }) => {
  const { t, i18n } = useTranslation();
  const { id } = useParams();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [showSelectOrg, setShowSelectOrg] = useState(false);
  const [instructionsData, setInstructionsData] = useState([]);
  const [orgOptions, setOrgOptions] = useState([]);
  const [toolGroupData, setToolGroupData] = useState([]);
  const [nameLanguage, setNameLanguage] = useState([]);
  
  useEffect(() => {
    getInstructionsData();
    getOrgData();
    getGroupTools();
  }, []);
  
  useEffect(() => {
    if (id) {
      getToolData();
    }
  }, [id]);
  
  const getInstructionsData = async () => {
    const dataResponse = await getAllInstructions();
    
    if (dataResponse) {
      setInstructionsData(dataResponse);
    }
  };
  
  
  const getOrgData = async () => {
    const dataResponse = await getAllOrg();
    if (dataResponse) {
      setOrgOptions(dataResponse);
    }
  };
  const getGroupTools = async () => {
    const dataResponse = await getAllGroupTool();
    if (dataResponse) {
      setToolGroupData(dataResponse);
    }
  };
  
  const getToolData = async () => {
    const dataResponse = await getToolDetail(id);
    const repareLocalization = coverLanguageObjectToArray(dataResponse?.localization);
    
    
    if (dataResponse) {
      const { categories, visible, name } = dataResponse;
      if (visible === "private") {
        setShowSelectOrg(true);
      }
      
      form.setFieldsValue({
        ...dataResponse,
        //localization: convertToObjectWithOptionalChaining(dataResponse.localization),
        categories: categories?.split(","),
        name: name.replace(/,/g, ""),
        localization: repareLocalization,
      });
    }
  };
  
  const onFinish = async (values) => {
    let dataRequest = cloneObj(values);
    
    const localization = coverLangArrayToObject(values?.localization);
    
    dataRequest = {
      ...dataRequest,
      categories: values.categories?.join(", "),
      localization,
    };
    if (values.visible !== "private") {
      dataRequest.organizationIds = [];
    }
    dataRequest.isOrganizationTool = values.visible === "private";
    if (id) {
      const dataResponse = await updateTool({ ...dataRequest, _id: id }, true);
      if (dataResponse) {
        toast.success("UPDATE_TOOL_SUCCESS");
      }
    } else {
      const dataResponse = await createTool(dataRequest, true);
      if (dataResponse) {
        toast.success("CREATE_TOOL_SUCCESS");
        navigate(LINK.TOOL_DETAIL.format(dataResponse._id));
      }
    }
  };
  
  const handleCancel = () => {
    navigate(-1);
  };
  
  function onChangeVisible(value) {
    setShowSelectOrg(value === "private");
    if (value !== "private") {
      form.setFieldsValue({ organizationIds: [] });
    }
  }
  
  const onSelect = (data, index, setData) => {
    const { value } = data;
    setData((pre) => {
      const newData = cloneObj(pre);
      newData[index] = value;
      return newData;
    });
  };
  
  const onRemove = (index, name, remove, setData) => {
    remove(index, name);
    setData((pre) => {
      const newData = cloneObj(pre);
      newData.splice(index, 1);
      return newData;
    });
  };
  
  const getAvailabeOptions = (index, options) => {
    const optionsSelected = options.filter((lang, optionIndex) => optionIndex !== index);
    return LANG_OPTIONS.filter((option) => !optionsSelected.some((selected) => selected.value === option.value));
  };
  
  return (
    <div className="tool-detail">
      <div className="tool-detail-header">
        <span className="tool-detail-header__title">{id ? t("TOOL_DETAIL") : t("CREATE_TOOL")}</span>
      </div>
      <Form onFinish={onFinish} layout="vertical" className={"form-detail-tool"} form={form} size="large">
        <Row gutter={24}>
          <Col xs={24} lg={12} xl={12}>
            <Form.Item label="Name" required name="name">
              <Input/>
            </Form.Item>
          </Col>
          <Col xs={24} lg={12} xl={12}>
            <Form.Item label="Content title" required name="contentTitle">
              <Input/>
            </Form.Item>
          </Col>
          <Col xs={24} lg={24} xl={24}>
            <Form.Item label="Description" required name="description">
              <Input/>
            </Form.Item>
          </Col>
          <Col xs={24} lg={12} xl={12}>
            <Form.Item
              label="Categories"
              name="categories"
              rules={[{ required: true, message: "Categories can't be blank!" }]}
            >
              <Select options={CATEGORIES} mode="multiple" allowClear placeholder={"Select categories"}/>
            </Form.Item>
          </Col>
          <Col xs={24} lg={12} xl={12}>
            <Form.Item
              label="Groups"
              name="groupToolIds"
              rules={[{ required: true, message: "Groups can't be blank!" }]}
            >
              <Select
                options={toolGroupData}
                mode="multiple"
                fieldNames={{ label: "groupName", value: "_id" }}
                placeholder={"Select group"}
                allowClear
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col xs={24} lg={12} xl={12}>
            <Form.Item label="Instruction" name="instructionIds">
              <Select
                options={instructionsData}
                mode="multiple"
                allowClear
                placeholder={"Select Instruction"}
                fieldNames={{ label: "shortName", value: "_id" }}
                optionFilterProp="shortName"
              />
            </Form.Item>
          </Col>
          <Col xs={24} lg={12} xl={12}>
            <Form.Item label="Link Youtube" name="linkYoutube">
              <Input placeholder={"Enter link Youtube"}/>
            </Form.Item>
          </Col>
          <Col xs={24} lg={12} xl={12}>
            <Form.Item label="Input label" name="inputLabel">
              <Input placeholder={"Enter Input label"}/>
            </Form.Item>
          </Col>
       
          <Col xs={24} lg={12} xl={12}>
            <Form.Item label="Input placeholder" name="inputPlaceholder">
              <Input placeholder={"Enter input placeholder"}/>
            </Form.Item>
          </Col>
          
          <Col xs={24} lg={12} xl={6}>
            <Form.Item
              label="Input type"
              name="inputType"
            >
              <Select options={TOOL_INPUT_TYPE} placeholder={"Select type"}/>
            </Form.Item>
          </Col>
          <Col xs={24} lg={12} xl={6}>
            <Form.Item
              label="Input max length"
              name="maxInputLength"
            >
              <InputNumber
                size="large"
                // max={numPages}
                controls={false}
                changeOnBlur
              />
            </Form.Item>
          </Col>
        
          <Col xs={24} lg={12} xl={6}>
            <Form.Item label="Type" name="type">
              <Select
                options={Object.values(TOOL_TYPE)}
                allowClear
                placeholder={"Select type of tool"}
              />
            </Form.Item>
          </Col>
          <Col xs={24} lg={12} xl={6}>
            <Form.Item label="Visible" name="visible">
              <Select
                options={Object.values(VISIBLE_TOOL)}
                allowClear
                placeholder={"Select visible"}
                onChange={onChangeVisible}
              />
            </Form.Item>
          </Col>
          
          {showSelectOrg && (
            <Col xs={24} lg={12} xl={12}>
              <Form.Item label="Organization" name="organizationIds">
                <Select
                  options={orgOptions}
                  mode="multiple"
                  allowClear
                  placeholder={"Select instruction"}
                  fieldNames={{ label: "name", value: "_id" }}
                  optionFilterProp="name"
                />
              </Form.Item>
            </Col>
          )}
        </Row>
        
        <Divider/>
        <Form.Item required>
          <Form.List name="localization">
            {(fields, { add, remove }) => {
              if (fields.length === 0) add();
              return (
                <div className={"tool-detail-localization-container"}>
                  {fields.map(({ key, name, ...restField }, index) => (
                    <>
                      <div className={"tool-detail-localization-item"}>
                        <Row gutter={24} key={key} className="items-baseline">
                          <Col xs={24} lg={6}>
                            <Form.Item
                              {...restField}
                              label={index === 0 ? "Language" : ""}
                              name={[name, "lang"]}
                              rules={[{ required: true, message: "Missing language of name!" }]}
                            >
                              <Select
                                options={getAvailabeOptions(index, nameLanguage)}
                                placeholder="Select language"
                                onSelect={(_, option) => onSelect(option, index, setNameLanguage)}
                              />
                            </Form.Item>
                          </Col>
                          <Col xs={24} lg={6}>
                            <Form.Item
                              {...restField}
                              name={[name, "name"]}
                              label={index === 0 ? "Name" : ""}
                            >
                              <Input placeholder="Enter name"/>
                            </Form.Item>
                          </Col>
                          <Col xs={24} lg={6}>
                            <Form.Item
                              {...restField}
                              name={[name, "description"]}
                              label={index === 0 ? "Description" : ""}
                            >
                              <Input placeholder="Enter description"/>
                            </Form.Item>
                          </Col>
                          <Col xs={24} lg={6}>
                            <Form.Item
                              {...restField}
                              name={[name, "contentTitle"]}
                              label={index === 0 ? "Content title" : ""}
                            >
                              <Input placeholder="Enter content title"/>
                            </Form.Item>
                          </Col>
                          <Col xs={24} lg={6}>
                            <Form.Item
                              {...restField}
                              name={[name, "inputLabel"]}
                              label={index === 0 ? "Input label" : ""}
                            >
                              <Input placeholder="Enter Input label"/>
                            </Form.Item>
                          </Col>
                          <Col xs={24} lg={6}>
                            <Form.Item
                              {...restField}
                              name={[name, "inputPlaceholder"]}
                              label={index === 0 ? "Input placeholder" : ""}
                            >
                              <Input placeholder="Enter Input placeholder"/>
                            </Form.Item>
                          </Col>
                        </Row>
                        <div className={"tool-detail-item-action"}>
                          <AntButton
                            type={BUTTON.WHITE}
                            shape={"circle"}
                            className={clsx("btn-cancel-add-tool-detail", { "first-actions": !index })}
                            icon={<CancelIcon/>}
                            size={"small"}
                            onClick={() => onRemove(index, name, remove, setNameLanguage)}
                          />
                        </div>
                      </div>
                    </>
                  ))}
                  
                  {fields.length < 2 && (
                    <div className={"add-tool-actions"}>
                      <AntButton
                        shape={"circle"}
                        size={"large"}
                        type={BUTTON.WHITE_BLUE}
                        onClick={() => add()}
                        icon={<PlusIcon/>}
                      ></AntButton>
                    </div>
                  )}
                </div>
              );
            }}
          </Form.List>
        </Form.Item>
        <Row gutter={32} justify="center" className="gap-4">
          <Form.Item className="save-button">
            <AntButton onClick={handleCancel} type={BUTTON.WHITE}>
              {t("BACK")}
            </AntButton>
          </Form.Item>
          <Form.Item className="save-button">
            <AntButton htmlType="submit" type={BUTTON.DEEP_NAVY}>
              {t("SAVE")}
            </AntButton>
          </Form.Item>
        </Row>
      </Form>
    </div>
  );
};

export default ToolDetail;
