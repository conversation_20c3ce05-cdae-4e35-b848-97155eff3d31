import { useTranslation } from "react-i18next";
import { BUTTON, CONSTANT } from "@constant";
import AntButton from "@component/AntButton";

function NextTool({ toolData, type, ...props }) {
  const { t } = useTranslation();
  
  if (!toolData?.length) return null;
  return <div className="content-action__list">
    {toolData?.map((tool, index) => {
      return <AntButton
        key={index}
        size="large"
        type={BUTTON.LIGHT_PURPLE}
        onClick={() => props.onClick(tool)}
      >
        {tool?.name}
      </AntButton>
    })}
  </div>
}

export default NextTool;