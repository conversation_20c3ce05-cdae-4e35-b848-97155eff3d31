import React, { useEffect, useRef, useState } from 'react';
import "./PartOne.scss";
import { useTranslation } from 'react-i18next';
import PAUSE_ICON from "@src/asset/icon/pause.svg";
import PLAY_ICON from "@src/asset/icon/PLAY_ICON.svg";
// import HIDE_ICON from "@src/asset/icon/HIDE_ICON.svg";
import SHOW_ICON from "@src/asset/icon/SHOW_ICON.svg";
import ShowIcon from '@src/assets/icons/show.svg';
import HideIcon from '@src/assets/icons/hide.svg';
import START_ICON from "@src/asset/icon/Arrow _Right_ICON.svg";
import AI_GEN_STAR from '@src/assets/icons/ai-gen-star.svg';
import DOUBLE_ARROW from '@src/assets/icons/double-arrow.svg';
import Intro_MP3 from "@src/asset/voiceAudio/are-you-ready.mp3";
import { Button } from 'antd';
import ShowQuestion from './ShowQuestion/ShowQuestion';
import { getSessionDetail, submitCompleteSession } from '@src/app/services/SpeakingRoom';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import ReactPlayer from 'react-player';
import HelperContainer, { GeneratedIdeaContent } from '@src/app/pages/Writing/CreateWriting/HelperContainer';
import { LINK } from '@link';
import ShowQuestionPart2 from './ShowQuestion/ShowQuestionPart2';
import ShowQuestionPart3 from './ShowQuestion/ShowQuestionPart3';
import LoadingButton from './LoadingButton';
import { LANGUAGE } from '@constant';
import CustomLanguageSelector from '../CustomLanguageSelector';


const PartOne = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const playerRef = useRef(null);
  const location = useLocation();
  const navigate = useNavigate();

  const isPart1 = location?.pathname === `/study-hub/speaking-room/practice-mode/${id}/part-1`
  const isPart2 = location?.pathname === `/study-hub/speaking-room/practice-mode/${id}/part-2`
  const isPart3 = location?.pathname === `/study-hub/speaking-room/practice-mode/${id}/part-3`

  const [isStart, setIsStart] = useState(false);
  const [isShowAnswer, setIsShowAnswer] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);

  const [detailsTopic, setDetailsTopic] = useState([]);
  const [totalDetailsTopic, setTotalDetailsTopic] = useState([]);

  const [audioSource, setAudioSource] = useState(null);
  const [shouldRestart, setShouldRestart] = useState(false);

  const [isFullTest, setIsFullTest] = useState(false);

  const [showHint, setShowHint] = useState(false);
  const [contentHint, setContentHint] = useState('')

  const [loading, setLoading] = useState(false);

  const [isAutoNextQuestion, setIsAutoNextQuestion] = useState(false);

  const [currentOutputLanguage, setCurrentOutputLanguage] = useState(LANGUAGE.VI); // Mặc định là Tiếng Việt

  const handleChangeOutputLanguage = (langKey) => {
    setCurrentOutputLanguage(langKey);
  };

  const handleLetIsStart = () => {
    setIsStart(true);
  }

  const handleCheckFullTest = (value) => {
    setIsFullTest(value)
  }

  const handleShowHint = (value) => {
    setShowHint(value)
  }

  const handleShowContentHint = (value) => {
    setContentHint(value)
  }

  // Function to generate hidden answer
  const getHiddenAnswer = answer => {
    return '*'.repeat(answer.length);
  };

  useEffect(() => {
    setAudioSource(Intro_MP3);
  }, []);

  const handlePlay = () => {
    if (shouldRestart || !isPlaying) {
      playerRef.current.seekTo(0); // phát lại từ đầu
      setShouldRestart(false);     // sau khi chạy lại từ đầu thì đặt lại cờ
    }
    setIsPlaying(true);
  };

  const handlePause = () => {
    setIsPlaying(false);
    setShouldRestart(true); // đánh dấu là lần sau phải phát lại từ đầu
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setShouldRestart(true); // khi phát xong cũng phải phát lại từ đầu nếu nhấn play
  };

  const handleNextPart = () => {
    if (isPart1) {
      navigate(LINK.SPEAKING_ROOM_PRACTICE_PART_TWO.format(id))
      setIsStart(false)
      setIsPlaying(true)
      setIsFullTest(false)
    } else if (isPart2) {
      navigate(LINK.SPEAKING_ROOM_PRACTICE_PART_THREE.format(id))
      setIsStart(false)
      setIsPlaying(true)
      setIsFullTest(false)
    }
  }

  const handleViewResult = async () => {
    setLoading(true);
    const data = {
      language: currentOutputLanguage,
    }
    try {
      const result = await submitCompleteSession(id, data);
      // console.log(result);            
    } catch (error) {
      console.log(error)
    } finally {
      navigate(LINK.SPEAKING_RESULT.format(id))
      setLoading(false);
    }

  }

  const handleGetSessionDetail = async () => {
    try {
      const result = await getSessionDetail(id, [], true);
      // console.log(result)
      const part1Questions = result?.questions?.filter((item) => item.part == 'part1');
      const part2Questions = result?.questions?.filter((item) => item.part == 'part2');
      const part3Questions = result?.questions?.filter((item) => item.part == 'part3');

      if (isPart1) {
        const sortedQuestions = part1Questions.sort((a, b) => a.questionIndex - b.questionIndex);
        setDetailsTopic(sortedQuestions);
      } else if (isPart2) {
        const sortedQuestions = part2Questions.sort((a, b) => a.questionIndex - b.questionIndex);
        setDetailsTopic(sortedQuestions);
      } else {
        const sortedQuestions = part3Questions.sort((a, b) => a.questionIndex - b.questionIndex);
        setDetailsTopic(sortedQuestions);
      }

      setTotalDetailsTopic(result?.questions)
      // console.log()
    } catch (error) {
      console.log("Error fetching session detail:", error);
    } finally {
    }
  }

  useEffect(() => {
    if (id) {
      setIsPlaying(true);
      handleGetSessionDetail();
    }
  }, [id, location?.pathname]);

  const TextTitle = () => {
    if (isPart1) {
      return (
        <>
          {t('PART_1')}:  {t('INTRODUCTION_INTERVIEW')}
        </>
      )
    } else if (isPart2) {
      return (
        <>
          {t('PART_2')}:  {t('INDIVIDUAL_LONG_TURN')}
        </>
      )
    } else {
      return (
        <>
          {t('PART_3')}:  {t('TWO_WAY_DISCUSSION')}
        </>
      )
    }
  }

  const TextIntro = () => {
    if (isPart1) {
      return (
        <>
          <p>{t('WELCOME_TO_PART_1_OF_THE_SPEAKING_TEST')}</p>
          <p>{t('CLICK_LETS_START_TO_BEGIN')}</p>
        </>
      )
    } else if (isPart2) {
      return (
        <>
          <p>{t('WELCOME_TO_PART_2_OF_THE_SPEAKING_TEST')}</p>
          <p>{t('CLICK_LETS_START_TO_BEGIN')}</p>
        </>
      )
    } else {
      return (
        <>
          <p>{t('WELCOME_TO_PART_3_OF_THE_SPEAKING_TEST')}</p>
          <p>{t('CLICK_LETS_START_TO_BEGIN')}</p>
        </>
      )
    }
  }

  const TextInstruct = () => {
    if (isPart1) {
      return (
        <>
          <p>{t('PRESS_START_RECORDING_TO_BEGIN_ANSWERING')}</p>
        </>
      )
    } else if (isPart2) {
      return (
        <>
          <p>{t('YOU_HAVE_1_MINUTE_TO_PREPARE_PRESS_START_RECORDING_TO_BEGIN_ANSWERING_IMMEDIATEL')}</p>
        </>
      )
    } else {
      return (
        <>
          <p>{t('PRESS_START_RECORDING_TO_BEGIN_ANSWERING')}</p>
        </>
      )
    }
  }

  return (
    <div className='container123'>
      <div className="part-one">
        <div className="part-one__container">
          <div className='part-one__container__header'>
            <div className="part-one__container__title">
              <TextTitle />
            </div>
            <div className="part-one__container__content">
              {isStart ?
                <TextInstruct />
                :
                <>
                  <TextIntro />
                </>

              }

            </div>
          </div>
          <div className="part-one__content">
            {isStart ? (
              <>
                {isPart1 ? (
                  <ShowQuestion
                    {...(detailsTopic ? { detailsTopic } : {})}
                    fullTest={isFullTest}
                    isAutoNextQuestion={isAutoNextQuestion}
                    setIsAutoNextQuestion={setIsAutoNextQuestion}
                    totalDetailsTopic={totalDetailsTopic?.length}
                    isCheckFullTest={handleCheckFullTest}
                    isShowHint={handleShowHint}
                    contentHint={handleShowContentHint}
                    handleGetSessionDetail={handleGetSessionDetail}
                    setIsStart={setIsStart}
                  />
                ) : isPart2 ? (
                  <ShowQuestionPart2
                    {...(detailsTopic ? { detailsTopic } : {})}
                    fullTest={isFullTest}
                    isAutoNextQuestion={isAutoNextQuestion}
                    setIsAutoNextQuestion={setIsAutoNextQuestion}
                    totalDetailsTopic={totalDetailsTopic?.length}
                    isCheckFullTest={handleCheckFullTest}
                    isShowHint={handleShowHint}
                    contentHint={handleShowContentHint}
                    handleGetSessionDetail={handleGetSessionDetail}
                    setIsStart={setIsStart}
                  />
                ) : (
                  <ShowQuestionPart3
                    {...(detailsTopic ? { detailsTopic } : {})}
                    fullTest={isFullTest}
                    isAutoNextQuestion={isAutoNextQuestion}
                    setIsAutoNextQuestion={setIsAutoNextQuestion}
                    isCheckFullTest={handleCheckFullTest}
                    isShowHint={handleShowHint}
                    contentHint={handleShowContentHint}
                    handleGetSessionDetail={handleGetSessionDetail}
                  />
                )
                }
              </>
            ) : (
              <>
                <div className='part-one__content__header'>
                  <img
                    src={isPlaying ? PLAY_ICON : PAUSE_ICON}
                    alt="icon"
                    className='part-one__content__header__icon'
                    onClick={isPlaying ? handlePause : handlePlay}
                  />
                  <p>
                    {isShowAnswer ? getHiddenAnswer(t('ARE_YOU_READY')) : t('ARE_YOU_READY')}
                  </p>
                  <img
                    src={!isShowAnswer ? ShowIcon : HideIcon}
                    alt="icon"
                    className='part-one__content__header__icon'
                    onClick={() => setIsShowAnswer(!isShowAnswer)}
                  />
                </div>
                <div className='part-one__content__footer'>
                  <Button className='part-one__footer__button'
                    onClick={() => {
                      handleLetIsStart();
                    }}
                  >
                    {t('LETS_START')}
                    <img src={START_ICON} alt="" />
                  </Button>
                </div>
              </>
            )}
          </div>
          {isFullTest && ((totalDetailsTopic?.length <= 5 && isStart) || (totalDetailsTopic?.length > 5 && isStart && isPart3)) && (
            <div className='part-one__body-language'>
              <CustomLanguageSelector
                disabled={loading}
                value={currentOutputLanguage}
                onChange={handleChangeOutputLanguage}
              />
            </div>            
          )}
          <div className='part-one__footer'>
            {isFullTest && (
              totalDetailsTopic?.length > 5 && isStart && !isPart3 ? (
                <div className='part-one__footer__border'
                  onClick={handleNextPart}
                >
                  <div className='part-one__footer__next'>{t('NEXT_PART')}</div>
                </div>
              ) : ((totalDetailsTopic?.length <= 5 && isStart) || (totalDetailsTopic?.length > 5 && isStart && isPart3)) ? (
                <>
                  {loading ? (
                    <LoadingButton isLoading={loading}>
                      <div className='part-one__footer__next'>{t("SUBMIT")}</div>
                    </LoadingButton>
                  ) : (
                    <>
                      <div className='part-one__footer__border'
                        onClick={handleViewResult}
                      >
                        <div className='part-one__footer__next'>{t("SUBMIT")}</div>
                      </div>
                    </>

                  )}

                </>

              ) : null
            )}
          </div>

        </div>

        {audioSource && (
          <ReactPlayer
            ref={playerRef}
            url={Intro_MP3}
            playing={isPlaying}
            controls={false}
            width="0"
            height="0"
            muted={false}
            onEnded={handleEnded}
          />
        )}
      </div>
      {/* Panel hint */}
      <div style={{ display: showHint ? 'block' : 'none' }}>
        <HelperContainer
          title={t('Hint Answer')}
          mode="hint-answer"
          helperContent={<GeneratedIdeaContent content={contentHint} />}
          toggleHelperCollapse={() => setShowHint(false)}
          aiGenStarIcon={AI_GEN_STAR}
          doubleArrowIcon={DOUBLE_ARROW}
        />
      </div>
    </div>
  );
}

export default PartOne;