import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { toast } from "@component/ToastProvider";
import AntButton from "@component/AntButton";
import Actions from "@component/Actions";
import DocumentOptionDetail from "./DocumentOptionDetail";
import TableAdmin from "@src/app/component/TableAdmin";

import { BUTTON, CONSTANT } from "@constant";

import { cloneObj } from "@common/functionCommons";
import {
  createDocumentOption,
  deleteDocumentOption,
  editDocumentOption,
  getAllDocumentOption,
} from "@services/DocumentOption";

import PlusIcon from "@component/SvgIcons/PlusIcon";

import "./DocumentOptions.scss";
import NoData from "@component/NoData";
import CheckGreen from "@component/SvgIcons/Check/CheckGreen";

function DocumentOptions({ documentOptionsData, setDocumentOptionsData }) {
  const { t } = useTranslation();
  const documentTemplateId = useParams().id;


  const [optionState, setOptionState] = useState({
    isShowModal: false,
    optionSelected: null,
  });


  useEffect(() => {
    if (documentTemplateId) {
      getDocumentOptionData();
    }
  }, [documentTemplateId]);

  async function getDocumentOptionData() {
    try {
      const apiResponse = await getAllDocumentOption({ docxTemplateId: documentTemplateId });
      if (apiResponse?.length) {
        setDocumentOptionsData(apiResponse);
      } else {
        setDocumentOptionsData([]);
      }
    } catch (error) {
      console.error("Error loading document options:", error);
      setDocumentOptionsData([]);
    }
  }

  function handleShowDetail(isShowModal, optionSelected = null) {
    if (isShowModal) {
      setOptionState({ isShowModal, optionSelected });
    } else {
      setOptionState({ isShowModal: false, optionSelected: null });
    }
  }

  function handleSaveDocumentOption(values) {
    if (optionState.optionSelected) {
      onUpdateOption(values);
    } else {
      onCreateOption(values);
    }
  }

  async function onCreateOption(values) {
    const apiResponse = await createDocumentOption({ ...values, docxTemplateId: documentTemplateId });
    if (apiResponse) {
      setDocumentOptionsData(prevState => [...prevState, apiResponse]);
      handleShowDetail(false);
      toast.success("CREATE_SUCCESS");
    }
  }

  async function onUpdateOption(values) {
    const apiResponse = await editDocumentOption({ ...values, _id: optionState.optionSelected._id });
    if (apiResponse?._id) {
      setDocumentOptionsData(prevState => {
        const newState = cloneObj(prevState);
        return newState.map(state => state._id === apiResponse._id ? apiResponse : state);
      });
      handleShowDetail(false);
      toast.success("UPDATE_SUCCESS");
    }
  }

  async function handleDelete(optionId) {
    const apiResponse = await deleteDocumentOption(optionId);
    if (apiResponse) {
      setDocumentOptionsData(prevState => prevState.filter(state => state._id !== apiResponse._id));
      toast.success("DELETE_SUCCESS");
    }
  }

  return <>
    <div className="document-template-detail__header">
      {t("DOCUMENT_OPTION")}

      <AntButton
        icon={<PlusIcon />}
        size="large"
        iconLocation={CONSTANT.RIGHT}
        type={BUTTON.DEEP_NAVY}
        onClick={() => handleShowDetail(true)}
      >
        {t("ADD_FIELD")}
      </AntButton>
    </div>
    <div className="document-options">
      {!!documentOptionsData.length
        ? <div className="document-options-grid">
            {documentOptionsData.map((option, index) => (
              <div className="document-option-card" key={option._id}>
                <div className="document-option-card__content">
                  <div className="document-option-card__header">
                    <div className="document-option-card__title">
                      {option.fieldLabel}
                      {option.isImage && (
                        <span className="document-option-card__image-badge">
                          <CheckGreen />
                          <span>{t("IMAGE")}</span>
                        </span>
                      )}
                    </div>
                    <div className="document-option-card__actions">
                      <Actions
                        handleEdit={() => handleShowDetail(true, option)}
                        handleDelete={() => handleDelete(option._id)}
                      />
                    </div>
                  </div>
                  <div className="document-option-card__details">
                    <div className="document-option-card__field">
                      <span className="document-option-card__field-label">{t("FIELD_NAME")}:</span>
                      <span className="document-option-card__field-value">{option.fieldName}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        : <div className="document-template-detail__no-data">
          <NoData />
        </div>}
    </div>


    <DocumentOptionDetail
      isOpen={optionState.isShowModal}
      handleCancel={() => handleShowDetail(false)}
      optionSelected={optionState.optionSelected}
      handleSaveDocumentOption={handleSaveDocumentOption}
    />
  </>;
}

export default DocumentOptions;
