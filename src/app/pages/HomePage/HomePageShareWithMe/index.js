import { useTranslation } from "react-i18next";

import FolderProjectList from "@component/FolderProjectList";
import HomePageSection from "../HomePageSection";

import TWO_USER from "@src/asset/icon/2User/2User.svg";

import { LINK } from "@link";

function HomePageShareWithMe({ dataSource, ...props }) {
  const { handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterChangeStarred, handleAfterMove } = props;
  const { t } = useTranslation();
  
  const linkShowMore = dataSource?.length > 7 ? LINK.SHARE_WITH_ME : "";
  return (<HomePageSection
    title={t("SHARE_WITH_ME")}
    icon={TWO_USER}
  >
    
    <FolderProjectList
      dataSource={dataSource?.slice(0, 7)}
      {...linkShowMore ? { linkShowMore } : {}}
      allowActions
      showOwner
      handleAfterCopy={handleAfterCopy}
      handleAfterRename={handleAfterRename}
      handleAfterDelete={handleAfterDelete}
      handleAfterChangeStarred={handleAfterChangeStarred}
      handleAfterMove={handleAfterMove}
    />
  </HomePageSection>);
}

export default HomePageShareWithMe;
