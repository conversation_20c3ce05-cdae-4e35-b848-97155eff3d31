.price-list-container {

  display: flex;
  flex-direction: column;
  gap: 24px;

  .ant-table {
    border-radius: 0px !important;
  }

  .table-price-list {
    box-shadow: var(--shadow-level-2);
  }

  .ant-table-cell {
    border: 0.5px solid var(--background-light-background-grey) !important;
    background-color: unset !important;
    border-radius: 0px !important;

    &:before {
      display: none;
    }
  }

  .ant-table-thead {
    th {
      padding: 24px !important;
    }
  }

  .clickee-price-title {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 15px;
    border: 1px;
    width: auto;
    height: auto;

    .clickee-price-title__title {
      font-weight: 600;
      font-size: 20px;
      line-height: 25px;
      white-space: nowrap;
    }

    .clickee-price-title__price {
      font-weight: 700;
      font-size: 22px;
      line-height: 30px;
      white-space: nowrap;
    }

    .clickee-price-title__titleIndex0 {
      color: var(--typo-colours-support-green);
    }

    .clickee-price-title__titleIndex1 {
      color: var(--typo-colours-support-purple);
    }

    .clickee-price-title__titleIndex2 {
      color: var(--typo-colours-support-yellow);
    }

    .clickee-price-title__titleIndex3 {
      color: var(--typo-colours-support-blue);
    }

    button {
      width: 100%;
    }

    a {
      &:has(button:disabled) {
        cursor: not-allowed;
      }
    }
  }

  .table-buy-more-addon {
    display: grid;
    gap: 38px;

    @media screen and (min-width: 1400px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    @media screen and (min-width: 930px) and (max-width: 1399.98px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    @media screen and (max-width: 929.98px) {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }

  .table-item-buy-more-addon {
  }

  .buy-more-items__btnBuy {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .buy-more-items {
    padding: 24px;
    font-family: Segoe UI;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background-color: var(--background-light-background-2);
    box-shadow: var(--shadow-level-2);
    width: 430px;

    .buy-more-items__title {
      font-weight: 600;
      font-size: 20px;
      line-height: 25px;
    }

    .item-addon-0 {
      color: var(--typo-colours-support-green);

    }

    .item-addon-1 {
      color: var(--typo-colours-support-yellow);
    }

    .item-addon-2 {
      color: var(--typo-colours-support-purple);
    }

    .item-addon-3 {
      color: var(--typo-colours-support-blue);
    }

    .buy-more-items__info {
      font-weight: 700;
      font-size: 22px;
      // white-space: nowrap;
      line-height: 30px;

    }

    .buy-more-items__priceList {
      display: flex;
      flex-direction: row;
      width: 100%;
      gap: 19px;
      align-items: center;
      box-sizing: border-box;

      span {
        width: 100%;
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
        white-space: nowrap;
      }

      .buy-more-items__priceList__textLeft {
        text-align: left;

      }

      .buy-more-items__priceList__textRight {
        text-align: right;
      }
    }

    .buy-more-items__priceList__inputNumber {
      max-width: 100px;
      text-align: center;

      input {
        padding: 6px 12px;
        font-size: 14px;
        border-radius: 4px;
      }
    }

    .buy-more-items__totalAmount {
      background-color: var(--blue-light-2);
      border-radius: 4px;
      padding: 10px 16px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      color: var(--primary-colours-blue)
    }

    .buy-now-button {
      background-color: #008000;
      color: #fff;
      border: none;
      padding: 10px 20px;
      font-size: 16px;
      border-radius: 4px;
      cursor: pointer;
    }
  }

  .price-cell {
    text-align: center;

    .price-cell__price {
      font-weight: bold;
    }
  }
}