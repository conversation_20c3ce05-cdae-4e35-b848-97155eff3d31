import React, { useEffect, useState } from "react";
import { Card, Col, Form, Input, Row, Tag, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { EditOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { connect } from "react-redux";

import { BUTTON, INPUT_TYPE, PAGINATION_INIT } from "@constant";

import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";

import { deleteOption, getPaginationOptions } from "@services/Option";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import AntButton from "@component/AntButton";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import "./Options.scss";
import { LINK } from "@link";
import { AntForm } from "@component/AntForm";
import { handleReplaceUrlSearch, handleSearchParams, orderColumn, paginationConfig } from "@common/functionCommons";
import i18n from "i18next";
import { handlePagingData } from "@common/dataConverter";


const Options = ({ ...props }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [formSearch] = Form.useForm();
  const [optionsData, setOptionsData] = useState(PAGINATION_INIT);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formSearch.setFieldsValue(query);
    getOptionsData(paging, query);
  }, [location.search]);

  const getOptionsData = async (paging = optionsData.paging, query = optionsData.query) => {
    setIsLoading(true);
    const dataResponse = await getPaginationOptions(paging, query);
    if (dataResponse) {
      setOptionsData(handlePagingData(dataResponse, query));
    }
    setIsLoading(false);
  };

  const handleEdit = (option) => {
    navigate(LINK.ADMIN_OPTIONS_DETAIL.format(option?._id));
  };

  const redirectToCreateOption = () => {
    navigate(LINK.ADMIN_OPTIONS_CREATE);
  };

  const handleDelete = (optionId, optionName) => {
    confirm.delete({
      title: t("DELETE_OPTION"),
      content: t("DELETE_OPTION_CONFIRM", { name: optionName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async (e) => {
        setIsLoading(true);
        const apiResponse = await deleteOption(optionId, true);
        if (apiResponse) {
          toast.success(t("DELETE_OPTION_SUCCESS"));
          await getOptionsData();
        } else {
          toast.error(t("DELETE_OPTION_ERROR"));
          setIsLoading(false);
        }
      },
    });
  };

  const columns = [
    orderColumn(optionsData.paging),
    {
      title: t("NAME"),
      dataIndex: "name",
      key: "name",
      width: 200,
      render: (text) => <span className="option-name-value">{text}</span>,
    },
    {
      title: t("CODE"),
      dataIndex: "code",
      key: "code",
      width: 150,
    },
    {
      title: t("PLACEHOLDER"),
      dataIndex: "placeholder",
      width: 150,
    },
    {
      title: t("DEFAULT_VALUE"),
      dataIndex: "defaultValue",
      width: 150,
    },
    {
      title: t("TYPE"),
      dataIndex: "type",
      key: "type",
      width: 150,
      render: (value) => {
        const typeInfo = INPUT_TYPE.find(type => type.value === value);
        let color;

        switch (value) {
          case "text":
            color = "blue";
            break;
          case "number":
            color = "green";
            break;
          case "select":
            color = "purple";
            break;
          case "select_multiple":
            color = "geekblue";
            break;
          case "textarea":
            color = "cyan";
            break;
          case "tag":
            color = "magenta";
            break;
          case "checkbox":
            color = "orange";
            break;
          default:
            color = "default";
        }

        return (
          <Tag color={color}>
            {typeInfo?.label || value}
          </Tag>
        );
      },
    },
    {
      title: t("INSTRUCTION"),
      dataIndex: "instruction",
      key: "instruction",
    },
    {
      title: t("ACTION"),
      key: "action",
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className={"options-table-actions"}>
          <Tooltip title={t("EDIT_OPTION")}>
            <Link to={LINK.ADMIN_OPTIONS_DETAIL.format(record?._id)}>
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="small"
                className={"btn-edit-option"}
                icon={<EditOutlined/>}
              />
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_OPTION")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-option"}
              icon={<DeleteIcon/>}
              onClick={() => handleDelete(record._id, record.name)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];
  const submitFormFilter = (values) => {
    handleReplaceUrlSearch(1, optionsData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formSearch.resetFields();
    submitFormFilter({});
  };

  const pagination = paginationConfig(optionsData.paging, optionsData.query, i18n.language);

  return (
    <Loading active={isLoading} transparent>
      <div className="options-container">
        <Card className="options-info-card">
          <div className="options-info-header">
            <div>
              <h1 className="options-title">{t("OPTIONS_MANAGEMENT")}</h1>
              <p className="options-description">{t("OPTIONS_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN_OPTIONS_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create-option"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_OPTION")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="options-search-card">
          <AntForm form={formSearch} layout="horizontal" size={"large"} className="form-filter" onFinish={submitFormFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={12}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_OPTION_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={12}>
                    <AntForm.Item name="code" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_OPTION_CODE_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="options-table-card">
          <TableAdmin
            columns={columns}
            dataSource={optionsData.rows}
            pagination={pagination}
            className="options-table"
            scroll={{ x: 1000 }}
            rowClassName={() => "options-table-row"}
            locale={{ emptyText: t("NO_OPTIONS_FOUND") }}
          />
        </Card>
      </div>
    </Loading>
  );
};

const mapDispatchToProps = {};


export default connect(() => {
}, mapDispatchToProps)(Options);
