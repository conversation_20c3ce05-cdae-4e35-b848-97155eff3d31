import { useState, useEffect } from "react";
import dayjs from "dayjs";

const POPUP_SHOWN_KEY = "last_popup_shown";

export const useRenewPackagePopup = (user) => {
  const [showPopup, setShowPopup] = useState(false);
  const [popupType, setPopupType] = useState("package");

  useEffect(() => {
    checkPopupCondition();
  }, [user]);


  const checkPopupCondition = () => {
    if (!user) return;

    const { showRenew, type } = user;
    const lastShownDate = localStorage.getItem(POPUP_SHOWN_KEY);
    const today = dayjs().format("YYYY-MM-DD");

    // Kiểm tra xem popup đã được hiển thị hôm nay chưa
    if (lastShownDate === today) {
      return;
    }

    if (type !== "student") {
      return;
    }

    // <PERSON><PERSON><PERSON> tra điều kiện hiển thị popup và xác đ<PERSON>nh loại
    if (showRenew) {
      setPopupType("package");
      setShowPopup(true);
      localStorage.setItem(POPUP_SHOWN_KEY, today);
    }
  };

  const closePopup = () => {
    setShowPopup(false);
  };

  return {
    showPopup,
    closePopup,
    popupType,
  };
};

export default useRenewPackagePopup;
