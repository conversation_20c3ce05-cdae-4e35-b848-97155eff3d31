@import "src/app/styles/scroll";

.project__info {
  flex-shrink: 0;
  grid-column: span 1;
  width: 248px;

  display: flex;
  flex-direction: column;

  .project__info-content {
    @extend .scrollbar;
    @extend .scrollbar-show;

    scrollbar-gutter: stable both-edges;

    padding: 24px 18px;
    background: var(--background-light-background-2);
    box-shadow: var(--shadow-level-2);

    max-height: calc(100vh - 72px - 32px - 32px);
    overflow: auto;
    border-radius: 8px;
  }

  .project-info__header {

    display: flex;
    flex-direction: row;
    gap: 8px;

    .project-info__icon {
      padding: 8px 0;

      img {
        width: 18px;
        height: 24px;
        object-fit: cover;
      }
    }

    .project-info__name {
      color: var(--typo-colours-primary-black);
      font-size: 24px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      flex: 1;

      .project-info__name-label {
        font-weight: 600;
      }

      .project-info__name-text {
        &:before {
          content: ' '
        }
      }
    }

    .project-info__actions {
      padding: 8px 0;
      width: 16px;
      display: flex;
      flex-direction: column;
      gap: 13px;
      //background: red;
    }

  }


  .project-info__description {
    font-size: 14px;
    margin-top: 8px;
    display: flex;
    flex-direction: row;
    gap: 8px;

    &.project-info__description-allow-editing {
      cursor: pointer;
    }

    .project-description__icon {
      display: flex;
      align-items: center;

      svg path {
        stroke: #000000;
      }
    }

    .project-description__value {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
  }

  .project-info__divider {
    padding-top: 16px;
    margin: 16px 0;
    color: var(--primary-colours-blue-navy);
    font-weight: 600;
    border-top: 1px solid var(--background-light-background-grey);
  }

  .project-info__detail {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .project-detail-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .project-detail-label {
        color: var(--typo-colours-support-blue-light);
        font-size: 13px;
      }

      .project-detail-value {
        display: flex;
        flex-direction: row;
        gap: 8px;

        img {
          margin-top: 2px;
          height: 16px;
          width: 16px;
        }

        &.project-detail-value__folder-name {
          cursor: pointer;
          padding: 8px 24px;
          margin: 0 -24px;
          transition-duration: 0s;
          color: #000000;

          &:hover {
            background: var(--primary-colours-blue-navy-light-1);
          }

          &:active {
            background: var(--primary-colours-blue);
            color: var(--white);
          }
        }
      }
    }
  }

  .project-info__activity {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .project-activity-item {
      display: flex;
      flex-direction: column;
      gap: 8px;


      .project-activity-label {
        justify-content: space-between;
        display: flex;
        align-items: center;
        font-weight: 600;
      }

      .project-activity-value {

      }
    }
    .download-excel-icon{
      height: 21.3px;
    }
  }

}
