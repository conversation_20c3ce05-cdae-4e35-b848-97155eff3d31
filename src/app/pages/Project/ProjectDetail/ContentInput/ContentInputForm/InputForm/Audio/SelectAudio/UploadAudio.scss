.upload-audio-modal {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.audio-dropzone {
  padding: 80px 32px;
  display: flex;
  gap: 24px;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: var(--primary-colours-blue);
  background: var(--primary-colours-blue-light-1);
  border: 1px solid var(--primary-colours-blue-light-1);

  &.dragenter {
    border-color: var(--primary-colours-blue);
    border-style: dashed;
    background: #FCFDFF;
  }

  &.dragenter * {
    opacity: 0.7;
  }

  .audio-dropzone__label {
    font-size: 24px;
    font-weight: 700;
  }

  .audio-dropzone__file-type {
    font-weight: 600;
  }

}

.select-from-resource-modal {
  @media screen and (max-height: 720px) {
    top: 24px;

    .modal-content {
      padding: 16px 32px;

      .ant-modal-close {
        top: 16px;
      }
    }

    .upload-audio-modal {
      gap: 16px;
    }

    .resource-table-container {
      padding: 16px 32px;
      gap: 16px;
    }
  }
}